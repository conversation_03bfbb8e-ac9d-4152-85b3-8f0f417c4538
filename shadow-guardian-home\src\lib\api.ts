// API configuration and utilities
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

export const api = {
  // Get system health from lifeTrack backend
  async getSystemHealth() {
    try {
      // Since lifeTrack doesn't have a specific health endpoint, we'll check if it's responsive
      const response = await fetch(`${API_BASE_URL}/api/logs`);
      const isHealthy = response.ok;

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        uptime: '2d 14h 32m', // Mock data for now
        cpu: 45,
        memory: 62,
        storage: 78,
        lastUpdate: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        uptime: '0m',
        cpu: 0,
        memory: 0,
        storage: 0,
        lastUpdate: new Date().toISOString()
      };
    }
  },

  // Get alerts from analysis logs
  async getAlerts() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/logs`);
      const logs = await response.json();

      // Convert recent logs to alerts
      const alerts = logs.slice(-5).map((log: any, index: number) => ({
        id: `alert_${index}`,
        type: 'info',
        message: log.summary?.substring(0, 100) + '...' || 'Activity detected',
        timestamp: log.interval_start || new Date().toISOString(),
        severity: 'medium',
        images: log.images || []
      }));

      return alerts;
    } catch (error) {
      console.error('Failed to fetch alerts:', error);
      return [];
    }
  },

  // Get recent captures from lifeTrack
  async getRecentCaptures() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/logs`);
      const logs = await response.json();

      // Extract all images from logs and create capture objects
      const captures: any[] = [];

      logs.forEach((log: any) => {
        if (log.images && Array.isArray(log.images)) {
          log.images.forEach((filename: string) => {
            captures.push({
              id: filename.replace('.jpg', ''),
              filename: filename,
              timestamp: log.interval_start || new Date().toISOString(),
              thumbnail: `/captured_images/${filename}`,
              fullSize: `/captured_images/${filename}`,
              location: 'Security Camera',
              tags: ['motion'],
              summary: log.summary || 'Activity detected'
            });
          });
        }
      });

      // Sort by timestamp (newest first) and return latest 20
      return captures
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 20);
    } catch (error) {
      console.error('Failed to fetch captures:', error);
      return [];
    }
  },

  // Get sensor events from logs
  async getSensorEvents() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/logs`);
      const logs = await response.json();

      // Convert logs to sensor events
      const events = logs.slice(-10).map((log: any, index: number) => ({
        id: `event_${index}`,
        sensor: 'Security Camera System',
        event: log.summary?.substring(0, 50) + '...' || 'Activity detected',
        timestamp: log.interval_start || new Date().toISOString(),
        status: 'resolved',
        images: log.images || []
      }));

      return events.reverse(); // Show newest first
    } catch (error) {
      console.error('Failed to fetch sensor events:', error);
      return [];
    }
  },

  // Search objects using lifeTrack chat API
  async searchObjects(query: string) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: `Find images with ${query}` }),
      });

      const result = await response.json();

      // Convert chat response to search results
      const searchResults = (result.images_to_show || []).map((filename: string) => ({
        id: filename.replace('.jpg', ''),
        filename: filename,
        timestamp: new Date().toISOString(),
        confidence: 0.85,
        objects: [query],
        thumbnail: `/captured_images/${filename}`,
        description: result.answer || 'Object found in image'
      }));

      return searchResults;
    } catch (error) {
      console.error('Failed to search objects:', error);
      return [];
    }
  },

  // Chat with AI using lifeTrack backend
  async chatWithAI(query: string) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const result = await response.json();
      return {
        answer: result.answer || 'Sorry, I could not process your request.',
        images: result.images_to_show || []
      };
    } catch (error) {
      console.error('Failed to chat with AI:', error);
      return {
        answer: 'Sorry, the AI service is currently unavailable.',
        images: []
      };
    }
  }
};
