import { useState, ReactNode } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { Navbar } from "@/components/Navbar";
import { DetailPanel } from "@/components/DetailPanel";
import { AlertsDrawer } from "@/components/AlertsDrawer";
import { SettingsModal } from "@/components/SettingsModal";

interface LayoutProps {
  children: ReactNode;
  title?: string;
}

export function Layout({ children, title = "Dashboard Home" }: LayoutProps) {
  const [selectedItem, setSelectedItem] = useState(null);
  const [showAlerts, setShowAlerts] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <Navbar 
            onShowAlerts={() => setShowAlerts(true)}
            onShowSettings={() => setShowSettings(true)}
            title={title}
          />
          <main className="flex-1 p-6 relative">
            {/* Pass setSelectedItem to children if they need it */}
            {typeof children === 'function' ? children(setSelectedItem) : children}
            
            {selectedItem && (
              <DetailPanel 
                item={selectedItem} 
                onClose={() => setSelectedItem(null)} 
              />
            )}
            
            <AlertsDrawer 
              isOpen={showAlerts} 
              onClose={() => setShowAlerts(false)} 
            />
            
            <SettingsModal 
              isOpen={showSettings} 
              onClose={() => setShowSettings(false)} 
            />
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
