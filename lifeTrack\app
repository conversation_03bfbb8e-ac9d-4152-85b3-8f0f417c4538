import os
import datetime
import time
import json
import imagehash
from io import Bytes<PERSON>
from threading import Thread, Event
import logging # Added for better logging

from PIL import Image
import cv2
import numpy as np
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import google.generativeai as genai

# --- Configuration ---
UPLOAD_FOLDER        = 'captured_images'
ANALYSIS_LOG_FILE    = 'analysis_log.json'
HIST_DUP_THRESH      = 0.9        # OpenCV histogram correlation threshold
PHASH_DUP_THRESH     = 5          # Perceptual‑hash difference threshold
GEMINI_MODEL_NAME    = 'gemini-1.5-flash' # or 'gemini-pro' if flash is not available for text
BATCH_INTERVAL_SEC   = 60         # process every 60 seconds

# Gemini API Key — set via environment
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyDfADnGrJ9LwlrYTnnKY2dxeMh8qsHYjiU") # Fallback for local dev, replace or set ENV
if GEMINI_API_KEY == "YOUR_API_KEY_HERE" or not GEMINI_API_KEY:
    print("ERROR: Set GEMINI_API_KEY in your environment or update the script.")
    # exit(1) # Commenting out exit for dev, but you should have a key

# initialize Gemini client
if GEMINI_API_KEY and GEMINI_API_KEY != "YOUR_API_KEY_HERE":
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel(GEMINI_MODEL_NAME)
    # For chatbot, we might use a text-only model if the default is multimodal and we only send text
    # Or, we can use the same model if it handles text prompts well.
    # Let's assume gemini_model can handle text prompts for the chatbot.
else:
    gemini_model = None
    print("WARNING: Gemini API key not configured. AI features will be limited.")


# --- Globals & Setup ---
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['ANALYSIS_LOG_FILE'] = ANALYSIS_LOG_FILE # Store for easy access
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


previous_hist = None
previous_phash = None
image_buffer = []  # list of (filepath, timestamp_iso)
stop_event = Event()

# Ensure JSON log exists
def init_log():
    if not os.path.exists(ANALYSIS_LOG_FILE):
        with open(ANALYSIS_LOG_FILE, 'w') as f:
            json.dump([], f, indent=2)
    # Ensure it's a valid JSON list
    try:
        with open(ANALYSIS_LOG_FILE, 'r') as f:
            content = f.read()
            if not content.strip(): # if empty
                with open(ANALYSIS_LOG_FILE, 'w') as fw:
                    json.dump([], fw, indent=2)
            else:
                json.loads(content) # try to parse
    except (json.JSONDecodeError, FileNotFoundError):
        with open(ANALYSIS_LOG_FILE, 'w') as f:
            json.dump([], f, indent=2)
        logging.info(f"{ANALYSIS_LOG_FILE} was invalid or missing, initialized.")


init_log() # Initialize log at startup

# --- Duplicate Detection ---
def is_duplicate_opencv(img, prev_img_hist, thresh=HIST_DUP_THRESH):
    # Convert to grayscale for more robust histogram comparison
    gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    hist1 = cv2.calcHist([gray_img],[0],None,[256],[0,256])
    cv2.normalize(hist1, hist1, alpha=0, beta=1, norm_type=cv2.NORM_MINMAX) # Normalize
    # prev_img_hist is already a calculated and normalized histogram
    return cv2.compareHist(hist1, prev_img_hist, cv2.HISTCMP_CORREL) >= thresh

# --- JSON Logging & AI Processing ---
def append_log(entry):
    data = []
    try:
        with open(ANALYSIS_LOG_FILE, 'r') as f:
            content = f.read()
            if content.strip(): # if not empty
                data = json.loads(content)
            if not isinstance(data, list): # if content was not a list
                data = []
    except (FileNotFoundError, json.JSONDecodeError):
        logging.warning(f"Log file {ANALYSIS_LOG_FILE} was missing or corrupt. Creating a new one.")
        data = []
    except Exception as e:
        logging.error(f"Unexpected error reading log: {e}")
        data = [] # Fallback to empty list

    data.append(entry)
    try:
        with open(ANALYSIS_LOG_FILE, 'w') as f: # Use 'w' to overwrite with updated list
            json.dump(data, f, indent=2)
    except Exception as e:
        logging.error(f"Error writing log: {e}")


def process_batch(batch):
    if not batch:
        return
    if not gemini_model:
        logging.warning("Gemini model not initialized. Skipping AI analysis for batch.")
        # Log raw image data without summary if needed
        start_ts = batch[0][1]
        names = [os.path.basename(path) for path, ts in batch]
        entry = {
            "interval_start": start_ts,
            "interval_end": datetime.datetime.utcnow().isoformat(),
            "summary": "<AI analysis unavailable: Gemini model not configured>",
            "images": names
        }
        append_log(entry)
        logging.info(f"Logged batch without AI analysis: {names}")
        # Clean up processed images from buffer (even if not analyzed by AI)
        for path, _ in batch:
            # Optionally delete images if they are only for AI analysis and not general viewing
            # For now, we keep them for the timeline.
            pass
        return

    start_ts = batch[0][1]
    pil_images, names = [], []
    for path, ts in batch:
        try:
            pil_images.append(Image.open(path))
            names.append(os.path.basename(path))
        except Exception as e:
            logging.error(f"Error opening image {path} for batch processing: {e}")
            continue

    if not pil_images:
        logging.info("No valid images in batch to process.")
        return

    # Ensure prompt is well-formed for Gemini 1.5 Flash
    # It's better to send image data and text separately if the API supports it,
    # or structure the prompt correctly.
    # For gemini-1.5-flash, a list of [text, image, text, image] is typical.
    prompt_parts = ["You are an activity-monitoring AI. Analyze this sequence of images captured over approximately one minute. Describe the scene, any people, objects, and significant activities or changes. Be concise but informative."]
    for img_pil in pil_images:
        prompt_parts.append(img_pil)

    if len(pil_images) > 1:
         prompt_parts.append("Provide a single summary for the entire sequence.")
    else:
        prompt_parts.append("Provide a summary for this image.")

    try:
        # For models like gemini-1.5-flash, you often send a list of parts (text, image data)
        response = gemini_model.generate_content(prompt_parts)
        summary = response.text.strip()
    except Exception as e:
        summary = f"<Gemini error: {e}>"
        logging.error(f"Gemini API error: {e}")
        # Check for specific API errors if possible
        if hasattr(e, 'response') and hasattr(e.response, 'prompt_feedback'):
            logging.error(f"Gemini prompt feedback: {e.response.prompt_feedback}")


    entry = {
        "interval_start": start_ts,
        "interval_end": datetime.datetime.utcnow().isoformat(),
        "summary": summary,
        "images": names
    }
    append_log(entry)
    logging.info(f"Logged JSON batch: {entry['images']} Summary: {summary[:50]}...")

    # Clean up images after processing if they are no longer needed in their original location
    # For this project, we keep them in UPLOAD_FOLDER for the timeline.
    # If storage is a concern, you might move them to archive or delete.

# --- Background Batch Thread ---
def batch_worker():
    init_log() # Ensure log is ready before worker starts
    logging.info("Batch worker started.")
    while not stop_event.is_set():
        stop_event.wait(BATCH_INTERVAL_SEC) # Use wait with timeout
        if stop_event.is_set():
            break

        current_batch = []
        # Drain buffer safely
        global image_buffer
        while image_buffer:
            try:
                current_batch.append(image_buffer.pop(0))
            except IndexError: # Should not happen with while image_buffer but good for safety
                break

        if current_batch:
            logging.info(f"Processing batch of {len(current_batch)} images.")
            process_batch(current_batch)
        else:
            logging.info("No images in buffer to process this interval.")
    logging.info("Batch worker stopped.")


# --- Flask Endpoints ---

@app.route('/')
def index():
    """Serves the main HTML page."""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """Serves the static dashboard with chatbot."""
    return send_from_directory('static', 'index.html')

@app.route('/static/<path:filename>')
def serve_static(filename):
    """Serves static files."""
    return send_from_directory('static', filename)

@app.route('/images/<filename>')
def uploaded_file(filename):
    """Serves a captured image."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/captured_images/<filename>')
def serve_captured_image(filename):
    """Serves captured images for the React frontend."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """Provides the analysis log data."""
    init_log() # Ensure it exists and is readable
    try:
        with open(app.config['ANALYSIS_LOG_FILE'], 'r') as f:
            content = f.read()
            if not content.strip():
                return jsonify([]) # Return empty list if file is empty
            logs = json.load(f)
            return jsonify(logs)
    except FileNotFoundError:
        return jsonify([])
    except json.JSONDecodeError:
        logging.error(f"Error decoding {app.config['ANALYSIS_LOG_FILE']}")
        return jsonify({"error": "Log file is corrupt"}), 500

@app.route('/api/analysis', methods=['GET'])
def get_analysis():
    """Provides the analysis log data for the static dashboard."""
    return get_logs()  # Reuse the same logic

@app.route('/upload', methods=['POST'])
def upload():
    global previous_hist, previous_phash
    data = request.data
    if not data:
        return jsonify(error="No image data"), 400

    arr = np.frombuffer(data, np.uint8)
    img_color = cv2.imdecode(arr, cv2.IMREAD_COLOR)
    if img_color is None:
        return jsonify(error="Invalid image data"), 400

    # Duplicate check with OpenCV histogram
    current_gray_img = cv2.cvtColor(img_color, cv2.COLOR_BGR2GRAY)
    current_hist = cv2.calcHist([current_gray_img], [0], None, [256], [0, 256])
    cv2.normalize(current_hist, current_hist, alpha=0, beta=1, norm_type=cv2.NORM_MINMAX)

    if previous_hist is not None and is_duplicate_opencv(img_color, previous_hist, HIST_DUP_THRESH): # is_duplicate_opencv expects img_color and prev_hist
        logging.info("Duplicate image detected (histogram).")
        return jsonify(status="duplicate (hist)"), 200
    previous_hist = current_hist # Store the normalized histogram of the current image

    # Duplicate check with pHash
    try:
        pil_img = Image.open(BytesIO(data))
        phash = imagehash.phash(pil_img)
        if previous_phash and phash and (phash - previous_phash) < PHASH_DUP_THRESH:
            logging.info("Duplicate image detected (phash).")
            return jsonify(status="duplicate (phash)"), 200
        previous_phash = phash
    except Exception as e:
        logging.warning(f"Could not calculate pHash: {e}")
        # Decide if you want to reject image or proceed without pHash check
        pass

    ts = datetime.datetime.utcnow()
    ts_file = ts.strftime("%Y%m%d_%H%M%S_%f")[:-3] # Milliseconds
    filename = f"img_{ts_file}.jpg"
    path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

    try:
        with open(path, 'wb') as f:
            f.write(data)
        logging.info(f"Image saved: {filename}")
        image_buffer.append((path, ts.isoformat()))
        return jsonify(status="saved", filename=filename), 201
    except Exception as e:
        logging.error(f"Error saving image {filename}: {e}")
        return jsonify(error=f"Could not save image: {e}"), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    if not gemini_model:
        return jsonify({
            "answer": "I'm sorry, the AI chat functionality is currently unavailable as the Gemini model is not configured.",
            "images": []
        }), 503

    data = request.json
    user_query = data.get('query')
    if not user_query:
        return jsonify(error="No query provided"), 400

    logging.info(f"Chat query received: {user_query}")

    try:
        with open(app.config['ANALYSIS_LOG_FILE'], 'r') as f:
            logs = json.load(f)
            if not isinstance(logs, list): logs = [] # ensure logs is a list
    except (FileNotFoundError, json.JSONDecodeError):
        logs = []

    if not logs:
        return jsonify({
            "answer": "I don't have any activity logs to search yet. Please wait for some images to be processed.",
            "images": []
        })

    # Prepare context for Gemini from the logs
    # We'll try to find the most relevant log entries first.
    # A simple keyword search to pre-filter, then use Gemini for Q&A on those entries.

    relevant_entries = []
    query_tokens = set(user_query.lower().split())

    for entry in logs:
        summary_tokens = set(entry.get("summary", "").lower().split())
        if query_tokens.intersection(summary_tokens): # Simple keyword overlap
            relevant_entries.append(entry)

    if not relevant_entries: # If no keyword match, use all logs (or a recent subset)
        # For simplicity, let's consider last 5-10 entries if log is large
        relevant_entries = logs[-5:] # Take last 5 entries as context if no direct match

    if not relevant_entries:
         return jsonify({
            "answer": "I couldn't find any specific information related to your query in the recent logs.",
            "images": []
        })

    # Construct a prompt for Gemini
    # We will give it the user's question and the summaries of relevant log entries.
    context_str = "Here are some relevant activity log summaries:\n"
    for i, entry in enumerate(relevant_entries):
        context_str += f"\nEntry {i+1} (Images: {', '.join(entry.get('images',[]))}, Time: {entry.get('interval_start','')[:19]} to {entry.get('interval_end','')[:19]}):\nSummary: {entry.get('summary', 'N/A')}\n"

    prompt = f"""
You are a helpful AI assistant looking through activity logs.
The user is asking: "{user_query}"

Based ONLY on the following context from the activity logs, answer the user's question.
If the logs help answer the question, provide a concise answer.
Also, identify which images (by their filenames listed in the context) are most relevant to the answer.
If the context does not contain an answer, say so. keep the summerise short , only keepo  it focused on the main object infront which has more focuu no decription of backgroud is needed

Context:
{context_str}

Respond in JSON format with two keys: "answer" (a string for the user) and "images_to_show" (a list of image filenames string, e.g., ["img1.jpg", "img2.jpg"], or an empty list [] if no specific images are directly relevant or found).
Example of good JSON response: {{"answer": "The log from around 10:30 AM shows you were reading a book. The relevant images are [\"img_timeA.jpg\", \"img_timeB.jpg\"]", "images_to_show": ["img_timeA.jpg", "img_timeB.jpg"]}}
Example of good JSON response if nothing found: {{"answer": "I couldn't find specific information about that in these logs.", "images_to_show": []}}
"""

    logging.info(f"Gemini prompt for chat: {prompt[:500]}...") # Log part of the prompt

    try:
        response = gemini_model.generate_content(prompt)

        # Attempt to parse Gemini's response as JSON
        # Gemini can sometimes output markdown ```json ... ```, so we clean it.
        raw_response_text = response.text.strip()
        logging.info(f"Raw Gemini response: {raw_response_text}")

        json_response_text = raw_response_text
        if json_response_text.startswith("```json"):
            json_response_text = json_response_text[7:]
        if json_response_text.endswith("```"):
            json_response_text = json_response_text[:-3]
        json_response_text = json_response_text.strip()

        chat_response = json.loads(json_response_text)

        # Validate the structure (basic check)
        if not isinstance(chat_response.get("answer"), str) or not isinstance(chat_response.get("images_to_show"), list):
            raise ValueError("Gemini response not in expected JSON format.")

        # Ensure image paths are just filenames
        chat_response["images_to_show"] = [os.path.basename(img) for img in chat_response.get("images_to_show", [])]

        logging.info(f"Chatbot response: {chat_response}")
        return jsonify(chat_response)

    except json.JSONDecodeError as e:
        logging.error(f"Failed to parse Gemini JSON response: {e}. Raw response: {raw_response_text}")
        return jsonify({
            "answer": f"I received a response, but had trouble understanding its format. The raw response was: {raw_response_text}",
            "images": []
        })
    except Exception as e:
        logging.error(f"Error in chat processing with Gemini: {e}")
        # Check for prompt feedback in case of API errors
        if hasattr(e, 'response') and hasattr(e.response, 'prompt_feedback'):
            logging.error(f"Gemini prompt feedback: {e.response.prompt_feedback}")

        return jsonify({
            "answer": f"Sorry, I encountered an error trying to answer your question: {str(e)}",
            "images": []
        }), 500


# --- Main ---
if __name__ == '__main__':
    # Start the background worker thread
    worker_thread = Thread(target=batch_worker, daemon=True)
    worker_thread.start()

    logging.info(f"Starting server with Gemini model {GEMINI_MODEL_NAME if gemini_model else 'NOT CONFIGURED'}")
    app.run(host='0.0.0.0', port=5000, debug=True)

    # Cleanup on exit (optional, as daemon thread will exit with main)
    # stop_event.set()
    # worker_thread.join()
    # logging.info("Application shutting down.")