# 🔒 Integrated Security Dashboard System

This project combines the **lifeTrack backend** with the **shadow-guardian-home frontend** into a **single integrated application**.

## 🏗️ Architecture

### Integrated System
- **Technology**: Python Flask serving React build
- **Port**: 5000 (single server)
- **Backend Features**:
  - Image capture and processing
  - AI analysis using Google Gemini
  - Duplicate detection
  - REST API endpoints
  - Real captured images (451 images included)
- **Frontend Features**:
  - Modern UI with shadcn/ui components
  - Real-time data from backend
  - Dashboard with system health, alerts, captures
  - Object search functionality
  - Responsive design

## 🚀 Quick Start

### Option 1: Automated Setup
```bash
# Run the automated startup script
start-integrated-system.bat
```

### Option 2: Manual Setup

#### Start Integrated System
```bash
cd lifeTrack
pip install -r requirements.txt
python app
```

**That's it!** The Flask server now serves both the API and the React UI.

## 📡 API Integration

The frontend connects to the backend through these endpoints:

- **GET /api/logs** - Fetch analysis logs and captured images
- **POST /api/chat** - AI-powered search and chat functionality
- **GET /images/{filename}** - Serve captured images

## 🖼️ Image Assets

- **451 real captured images** copied to `shadow-guardian-home/public/captured_images/`
- Images are served directly from the frontend for optimal performance
- Fallback placeholders for missing images

## 🔧 Configuration

### System Configuration
- **CORS enabled** for API communication
- **Gemini API** for AI analysis (configure API key in environment)
- **Image processing** with OpenCV and PIL
- **React build** served directly by Flask
- **Auto-refresh**: Components refresh every 10-30 seconds
- **Error handling**: Graceful fallbacks for API issues

## 📊 Features

### Dashboard Components
1. **System Health Card**
   - Real-time backend status
   - Last capture timestamp
   - CPU usage monitoring

2. **Alerts Overview**
   - Categorized alerts from AI analysis
   - Motion detection, activity alerts
   - Real-time alert counts

3. **Recent Captures Preview**
   - Latest 4 captured images
   - Timestamps and AI summaries
   - Click to view full details

4. **Sensor Events Table**
   - Recent activity events
   - Image thumbnails
   - Detailed event descriptions

5. **Object Search**
   - AI-powered search through captured images
   - Natural language queries
   - Confidence scores and results

## 🛠️ Development

### Integrated Development
```bash
cd lifeTrack
python app  # Serves both backend and frontend
```

### Frontend Development (if needed)
```bash
cd shadow-guardian-home
npm run dev  # For UI development only
npm run build  # Build and copy to lifeTrack
```

## 📁 Project Structure

```
├── lifeTrack/                 # Backend (Python Flask)
│   ├── app                    # Main Flask application
│   ├── captured_images/       # Original captured images
│   ├── analysis_log.json      # AI analysis results
│   └── requirements.txt       # Python dependencies
│
├── shadow-guardian-home/      # Frontend (React)
│   ├── src/
│   │   ├── components/        # UI components
│   │   ├── lib/api.ts        # Backend API integration
│   │   └── pages/            # Application pages
│   ├── public/
│   │   └── captured_images/  # Copied images for frontend
│   └── package.json          # Node.js dependencies
│
└── start-integrated-system.bat  # Automated startup script
```

## 🔍 Key Integration Points

1. **Real Data Flow**: Frontend fetches real analysis logs and images from backend
2. **CORS Configuration**: Backend configured to accept requests from frontend
3. **Image Serving**: Images copied to frontend public folder for direct access
4. **Error Handling**: Graceful degradation when backend is unavailable
5. **Real-time Updates**: Components auto-refresh to show latest data

## 🎯 Usage

1. **Start the integrated system** using the startup script or manually
2. **Open dashboard** at `http://localhost:5000`
3. **View real captured images** in the modern React UI
4. **Search for objects** using natural language AI
5. **Monitor system health** and recent activities in real-time

## 🔧 Troubleshooting

### Backend Issues
- Ensure Python dependencies are installed: `pip install -r requirements.txt`
- Check if port 5000 is available
- Verify Gemini API key configuration

### Frontend Issues
- Ensure Node.js dependencies are installed: `npm install`
- Check if port 5173 is available
- Verify backend is running and accessible

### CORS Issues
- Backend includes CORS headers for frontend communication
- If issues persist, check browser console for specific errors

## 🚀 Production Deployment

For production deployment:
1. Build frontend: `npm run build`
2. Configure backend for production environment
3. Set up proper reverse proxy (nginx/Apache)
4. Configure environment variables
5. Set up SSL certificates

---

**Note**: This integration maintains the original functionality of both projects while providing a seamless user experience with real data and modern UI components.
