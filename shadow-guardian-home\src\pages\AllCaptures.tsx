import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Layout } from "@/components/Layout";
import {
  Image as ImageIcon,
  Search,
  Filter,
  Calendar,
  Download,
  Eye,
  Clock
} from "lucide-react";

interface CaptureImage {
  id: number;
  timestamp: string;
  description: string;
  thumbnail: string;
  fullImage: string;
  tags: string[];
  confidence: number;
}

interface AllCapturesProps {
  onSelectImage?: (image: CaptureImage) => void;
}

function AllCapturesContent({ onSelectImage }: AllCapturesProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");

  // Extended list of all captures
  const allCaptures: CaptureImage[] = [
    {
      id: 1,
      timestamp: "2024-01-15 14:30",
      description: "Motion detected in living room",
      thumbnail: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=800&h=600&fit=crop",
      tags: ["motion", "living room", "daytime"],
      confidence: 95
    },
    {
      id: 2,
      timestamp: "2024-01-15 14:25",
      description: "Cat moving through kitchen",
      thumbnail: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=800&h=600&fit=crop",
      tags: ["animal", "kitchen", "movement"],
      confidence: 88
    },
    {
      id: 3,
      timestamp: "2024-01-15 14:20",
      description: "Package delivery detected",
      thumbnail: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=800&h=600&fit=crop",
      tags: ["delivery", "outdoor", "package"],
      confidence: 92
    },
    {
      id: 4,
      timestamp: "2024-01-15 14:15",
      description: "Normal surveillance",
      thumbnail: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=600&fit=crop",
      tags: ["normal", "surveillance"],
      confidence: 75
    },
    {
      id: 5,
      timestamp: "2024-01-15 14:10",
      description: "Movement in backyard",
      thumbnail: "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=800&h=600&fit=crop",
      tags: ["backyard", "movement", "outdoor"],
      confidence: 89
    },
    {
      id: 6,
      timestamp: "2024-01-15 13:45",
      description: "Person entering front door",
      thumbnail: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop",
      tags: ["person", "entrance", "front door"],
      confidence: 97
    },
    {
      id: 7,
      timestamp: "2024-01-15 13:30",
      description: "Vehicle in driveway",
      thumbnail: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop",
      tags: ["vehicle", "driveway", "outdoor"],
      confidence: 91
    },
    {
      id: 8,
      timestamp: "2024-01-15 13:15",
      description: "Bird activity in garden",
      thumbnail: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop",
      tags: ["bird", "garden", "nature"],
      confidence: 82
    },
    {
      id: 9,
      timestamp: "2024-01-15 12:50",
      description: "Security check - all clear",
      thumbnail: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop",
      tags: ["security", "clear", "routine"],
      confidence: 70
    },
    {
      id: 10,
      timestamp: "2024-01-15 12:30",
      description: "Wind moving plants",
      thumbnail: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",
      tags: ["wind", "plants", "natural"],
      confidence: 65
    },
    {
      id: 11,
      timestamp: "2024-01-15 12:15",
      description: "Mailman delivery",
      thumbnail: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop",
      tags: ["mailman", "delivery", "person"],
      confidence: 94
    },
    {
      id: 12,
      timestamp: "2024-01-15 11:45",
      description: "Neighbor walking dog",
      thumbnail: "https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=200&fit=crop",
      fullImage: "https://images.unsplash.com/photo-1552053831-71594a27632d?w=800&h=600&fit=crop",
      tags: ["neighbor", "dog", "walking"],
      confidence: 90
    }
  ];

  const filteredCaptures = allCaptures.filter(capture => {
    const matchesSearch = capture.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         capture.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    if (selectedFilter === "all") return matchesSearch;
    if (selectedFilter === "motion") return matchesSearch && capture.tags.includes("motion");
    if (selectedFilter === "people") return matchesSearch && (capture.tags.includes("person") || capture.tags.includes("mailman"));
    if (selectedFilter === "animals") return matchesSearch && (capture.tags.includes("animal") || capture.tags.includes("bird") || capture.tags.includes("dog"));

    return matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">All Captures</h1>
          <p className="text-muted-foreground">Browse through all captured images and events</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="border-border text-muted-foreground">
            {filteredCaptures.length} captures
          </Badge>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="bg-card border-border">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search captures by description or tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-input border-border text-foreground placeholder-muted-foreground"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={selectedFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("all")}
                className={selectedFilter === "all" ? "bg-primary text-primary-foreground" : "border-border text-muted-foreground hover:border-primary hover:text-primary"}
              >
                All
              </Button>
              <Button
                variant={selectedFilter === "motion" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("motion")}
                className={selectedFilter === "motion" ? "bg-primary text-primary-foreground" : "border-border text-muted-foreground hover:border-primary hover:text-primary"}
              >
                Motion
              </Button>
              <Button
                variant={selectedFilter === "people" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("people")}
                className={selectedFilter === "people" ? "bg-primary text-primary-foreground" : "border-border text-muted-foreground hover:border-primary hover:text-primary"}
              >
                People
              </Button>
              <Button
                variant={selectedFilter === "animals" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("animals")}
                className={selectedFilter === "animals" ? "bg-primary text-primary-foreground" : "border-border text-muted-foreground hover:border-primary hover:text-primary"}
              >
                Animals
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Captures Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredCaptures.map((capture) => (
          <Card key={capture.id} className="bg-card border-border hover:border-primary transition-colors group cursor-pointer">
            <CardContent className="p-0">
              <div className="relative">
                <img
                  src={capture.thumbnail}
                  alt={capture.description}
                  className="w-full h-48 object-cover rounded-t-lg"
                  onClick={() => onSelectImage?.(capture)}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-t-lg" />
                <div className="absolute top-2 right-2 flex gap-1">
                  <Button
                    variant="secondary"
                    size="icon"
                    className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white border-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectImage?.(capture);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white border-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle download
                    }}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
                <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {capture.timestamp}
                  </div>
                </div>
              </div>
              <div className="p-4">
                <p className="text-sm text-card-foreground font-medium mb-2">{capture.description}</p>
                <div className="flex flex-wrap gap-1 mb-2">
                  {capture.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs border-border text-muted-foreground">
                      {tag}
                    </Badge>
                  ))}
                  {capture.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs border-border text-muted-foreground">
                      +{capture.tags.length - 3}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>Confidence: {capture.confidence}%</span>
                  <span>ID: {capture.id}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCaptures.length === 0 && (
        <Card className="bg-card border-border">
          <CardContent className="p-8 text-center">
            <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-card-foreground mb-2">No captures found</h3>
            <p className="text-muted-foreground">Try adjusting your search or filter criteria.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export function AllCaptures() {
  return (
    <Layout title="All Captures">
      {(onSelectImage) => <AllCapturesContent onSelectImage={onSelectImage} />}
    </Layout>
  );
}
