
import { X, Download, Star, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface DetailPanelProps {
  item: any;
  onClose: () => void;
}

export function DetailPanel({ item, onClose }: DetailPanelProps) {
  const detectedObjects = [
    { name: "Person", confidence: 95 },
    { name: "Cat", confidence: 89 },
    { name: "Furniture", confidence: 76 },
    { name: "Plant", confidence: 62 }
  ];

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-card border-l border-border z-50 animate-slide-in">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h3 className="text-lg font-semibold text-card-foreground">Event Details</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="text-muted-foreground hover:text-card-foreground">
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {/* Image/Video View */}
          <Card className="bg-secondary border-border">
            <CardContent className="p-0">
              <img
                src={item.thumbnail || "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400&h=300&fit=crop"}
                alt="Event capture"
                className="w-full h-48 object-cover rounded-t-lg"
              />
            </CardContent>
          </Card>

          {/* Event Info */}
          <div className="space-y-3">
            <div>
              <h4 className="text-card-foreground font-medium mb-1">Event Time</h4>
              <p className="text-muted-foreground font-mono text-sm">{item.timestamp || item.time}</p>
            </div>

            <div>
              <h4 className="text-card-foreground font-medium mb-1">AI Description</h4>
              <p className="text-muted-foreground text-sm">{item.description}</p>
            </div>
          </div>

          {/* Detected Objects */}
          <Card className="bg-secondary border-border">
            <CardContent className="p-4">
              <h4 className="text-card-foreground font-medium mb-3">Detected Objects</h4>
              <div className="space-y-3">
                {detectedObjects.map((obj, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-secondary-foreground">{obj.name}</span>
                      <span className="text-primary">{obj.confidence}%</span>
                    </div>
                    <Progress value={obj.confidence} className="h-2 bg-muted" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Event Classification */}
          <div>
            <h4 className="text-card-foreground font-medium mb-2">Classification</h4>
            <div className="flex gap-2">
              <Badge variant="secondary" className="bg-yellow-600 text-white">
                Motion Event
              </Badge>
              <Badge variant="outline" className="border-primary text-primary">
                Normal Activity
              </Badge>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="border-t border-border p-4">
          <div className="flex gap-2">
            <Button className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground">
              <Star className="h-4 w-4 mr-2" />
              Mark Important
            </Button>
            <Button variant="outline" className="border-border text-muted-foreground hover:border-primary hover:text-primary">
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="outline" className="border-border text-muted-foreground hover:border-primary hover:text-primary">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
