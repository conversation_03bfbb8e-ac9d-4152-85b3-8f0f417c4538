
import { Home, Camera, History, Bell, Settings, Image, Images, AlertTriangle, Search } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

const menuItems = [
  {
    title: "Home",
    url: "/",
    icon: Home,
  },
  {
    title: "Live View",
    url: "#",
    icon: Camera,
  },
  {
    title: "All Captures",
    url: "/all-captures",
    icon: Images,
  },
  {
    title: "History Timeline",
    url: "#",
    icon: History,
  },
  {
    title: "Sensor Events",
    url: "#",
    icon: Bell,
  },
  {
    title: "Object Search",
    url: "#",
    icon: Search,
  },
  {
    title: "Alerts",
    url: "#",
    icon: AlertTriangle,
  },
  {
    title: "Settings",
    url: "#",
    icon: Settings,
  },
];

export function AppSidebar() {
  return (
    <Sidebar className="border-r border-sidebar-border">
      <SidebarContent className="bg-sidebar">
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-primary font-semibold text-sm mb-4">
            AI Security System
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className="hover:bg-sidebar-accent hover:text-sidebar-primary transition-colors group"
                  >
                    {item.url.startsWith('/') ? (
                      <Link to={item.url} className="flex items-center gap-3 p-3">
                        <item.icon className="h-5 w-5 group-hover:text-sidebar-primary" />
                        <span className="font-medium">{item.title}</span>
                      </Link>
                    ) : (
                      <a href={item.url} className="flex items-center gap-3 p-3">
                        <item.icon className="h-5 w-5 group-hover:text-sidebar-primary" />
                        <span className="font-medium">{item.title}</span>
                      </a>
                    )}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
