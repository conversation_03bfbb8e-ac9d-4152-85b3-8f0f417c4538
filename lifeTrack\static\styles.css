:root {
    /* Dark theme color palette */
    --primary-bg: #1a1a1a;
    --secondary-bg: #2a2a2a;
    --card-bg: #333333;
    --sidebar-bg: #0f0f0f;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --accent-blue: #4a9eff;
    --accent-green: #4ade80;
    --accent-red: #ef4444;
    --accent-orange: #f59e0b;
    --border-color: #404040;
    --hover-bg: #404040;

    /* Additional variables for All Captures page */
    --accent-color: #4a9eff;
    --accent-hover: #3b82f6;

    /* Status colors */
    --status-online: #22c55e;
    --status-offline: #ef4444;
    --status-warning: #f59e0b;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);

    /* Transitions */
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.5;
    overflow-x: hidden;
}

.app-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--primary-bg);
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 16px;
}

.logo i {
    color: var(--accent-blue);
    font-size: 20px;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: var(--hover-bg);
    color: var(--text-primary);
}

.nav-item.active {
    background-color: var(--hover-bg);
    color: var(--text-primary);
    border-left-color: var(--accent-blue);
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 250px;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-left i {
    color: var(--accent-blue);
}

.header-center h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--status-online);
}

.status-dot.online {
    background-color: var(--status-online);
}

.status-dot.offline {
    background-color: var(--status-offline);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-menu i {
    color: var(--text-secondary);
    font-size: 18px;
    cursor: pointer;
    transition: var(--transition);
}

.user-menu i:hover {
    color: var(--text-primary);
}

/* Dashboard Content */
.dashboard-content {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
}

.dashboard-row {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
}

.dashboard-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.dashboard-card.full-width {
    flex: 1;
}

.dashboard-card.system-health {
    flex: 1;
}

.dashboard-card.alerts-overview {
    flex: 1;
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: 20px;
}

/* System Health Card */
.health-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.health-item:last-child {
    margin-bottom: 0;
}

.health-item i {
    color: var(--text-muted);
    width: 16px;
}

.health-item .label {
    color: var(--text-secondary);
    font-size: 14px;
    flex: 1;
}

.health-item .value {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.error {
    background-color: rgba(239, 68, 68, 0.2);
    color: var(--accent-red);
}

.status-badge.success {
    background-color: rgba(34, 197, 94, 0.2);
    color: var(--accent-green);
}

.status-badge.warning {
    background-color: rgba(245, 158, 11, 0.2);
    color: var(--accent-orange);
}

/* Alerts Overview Card */
.alert-type {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.alert-type:last-of-type {
    margin-bottom: 20px;
}

.alert-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.alert-icon.motion {
    background-color: rgba(74, 158, 255, 0.2);
    color: var(--accent-blue);
}

.alert-icon.activity {
    background-color: rgba(74, 222, 128, 0.2);
    color: var(--accent-green);
}

.alert-icon.other {
    background-color: rgba(176, 176, 176, 0.2);
    color: var(--text-muted);
}

.alert-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-label {
    color: var(--text-secondary);
    font-size: 14px;
}

.alert-count {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.total-alerts {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

.total-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.total-label {
    color: var(--text-muted);
    font-size: 12px;
}

/* Recent Captures Card */
.view-all-btn {
    background: none;
    border: none;
    color: var(--accent-blue);
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    padding: 6px 12px;
    border-radius: 4px;
}

.view-all-btn:hover {
    color: var(--text-primary);
    background-color: var(--hover-bg);
}

.captures-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.capture-item {
    background-color: var(--secondary-bg);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.capture-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.capture-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.capture-info {
    padding: 12px;
}

.capture-time {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 4px;
}

.capture-description {
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.no-captures {
    text-align: center;
    color: var(--text-muted);
    padding: 40px 20px;
    font-size: 14px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--card-bg);
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    max-width: 800px;
    position: relative;
    border: 1px solid var(--border-color);
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    font-weight: bold;
    color: var(--text-secondary);
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.close:hover {
    background-color: var(--hover-bg);
    color: var(--text-primary);
}

#modal-image {
    width: 100%;
    max-height: 60vh;
    object-fit: contain;
    margin-bottom: 16px;
    border-radius: 4px;
}

.modal-info {
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

#modal-timestamp {
    color: var(--accent-blue);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

#modal-description {
    color: var(--text-primary);
    font-size: 16px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .dashboard-row {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 12px 16px;
    }

    .header-center h2 {
        font-size: 18px;
    }

    .dashboard-content {
        padding: 16px;
    }

    .captures-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;
    }

    .capture-image {
        height: 100px;
    }

    .modal-content {
        margin: 10% auto;
        padding: 16px;
        max-width: 90%;
    }
}

@media (max-width: 480px) {
    .dashboard-content {
        padding: 12px;
    }

    .card-content {
        padding: 16px;
    }

    .captures-container {
        grid-template-columns: 1fr 1fr;
    }

    .health-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .alert-type {
        gap: 8px;
    }

    .alert-icon {
        width: 28px;
        height: 28px;
    }
}









#search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px var(--accent-light);
}

#search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    padding: 6px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--hover-transition);
}

.btn {
    padding: 10px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--hover-transition);
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Main Content */
main {
    display: grid;
    grid-template-columns: 1fr 380px;
    gap: 30px;
    position: relative;
}

section {
    background-color: var(--card-color);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    padding: 24px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
}

section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0.8;
}

section h2 {
    color: var(--primary-color);
    margin-bottom: 24px;
    font-size: 22px;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
}

section h2::before {
    content: '';
    display: block;
    width: 4px;
    height: 22px;
    background: var(--accent-color);
    border-radius: 2px;
}

/* Timeline Section */
.timeline-section {
    position: relative;
}

.timeline-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--card-color);
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 24px;
    border: 1px solid var(--border-color);
}

.filter-container, .sort-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.filter-container label, .sort-container label {
    font-weight: 500;
    color: var(--text-color);
}

#date-filter, #sort-select {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--card-color);
    color: var(--text-color);
    transition: var(--hover-transition);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

#date-filter:hover, #sort-select:hover {
    border-color: var(--accent-color);
}

#date-filter:focus, #sort-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px var(--accent-light);
}

/* Timeline Wrapper */
.timeline-wrapper {
    position: relative;
    padding: 0;
    margin-top: 30px;
}

/* Main Timeline View */
.timeline-view {
    position: relative;
    height: var(--timeline-height);
    overflow: hidden;
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    box-shadow: var(--shadow-inner);
}

.timeline-container {
    position: relative;
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    padding: 20px 0;
    scrollbar-width: none; /* Firefox */
}

.timeline-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

/* Timeline Drag Bar (Microsoft Recall Style) */
.timeline-scrubber {
    position: relative;
    height: var(--timeline-zoom-height);
    margin: 10px 0 20px;
    border-radius: 8px;
    background-color: var(--card-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.timeline-minimap {
    position: relative;
    height: 100%;
    width: 100%;
    background-color: var(--background-color);
}

.timeline-minimap::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        var(--accent-color) 0%,
        var(--primary-color) 50%,
        var(--accent-color) 100%
    );
    z-index: 1;
}

.timeline-minimap-points {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 0 10px;
}

.minimap-point {
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    margin: 0 3px;
    flex-shrink: 0;
}

.timeline-drag-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 100%;
    background-color: rgba(0, 112, 243, 0.15);
    border: 2px solid var(--primary-color);
    border-radius: 6px;
    cursor: grab;
    z-index: 10;
}

.timeline-drag-handle::before,
.timeline-drag-handle::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.timeline-drag-handle::before {
    left: 8px;
}

.timeline-drag-handle::after {
    right: 8px;
}

.timeline-zoom-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.zoom-btn {
    background-color: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--hover-transition);
    box-shadow: var(--shadow-sm);
}

.zoom-btn:hover {
    background-color: var(--accent-light);
    border-color: var(--accent-color);
}

.zoom-btn i {
    color: var(--primary-color);
}

/* Timeline Content */
.timeline {
    display: flex;
    position: relative;
    padding: 20px 0;
    min-width: max-content;
    transition: transform 0.3s ease;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        var(--accent-color) 0%,
        var(--primary-color) 50%,
        var(--accent-color) 100%
    );
    z-index: 1;
}

.timeline-batch {
    position: relative;
    margin: 0 30px;
    min-width: 320px;
    width: 320px;
    z-index: 2;
    transition: var(--hover-transition);
}

.timeline-point {
    width: 28px;
    height: 28px;
    background: var(--primary-color);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    box-shadow: 0 0 0 6px rgba(0, 112, 243, 0.2);
    transition: var(--hover-transition);
    cursor: pointer;
}

.timeline-point::after {
    content: '';
    position: absolute;
    width: 14px;
    height: 14px;
    background: white;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.timeline-batch:hover .timeline-point {
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 0 8px rgba(0, 112, 243, 0.3);
}

.timeline-date-label {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: 500;
    color: var(--text-light);
    white-space: nowrap;
}

.timeline-card {
    background-color: var(--card-color);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    padding: 20px;
    margin-top: 50px;
    border: 1px solid rgba(0,0,0,0.05);
    transition: var(--hover-transition);
    position: relative;
    overflow: hidden;
    transform-origin: bottom center;
    width: 320px;
    max-width: 100%;
}

.timeline-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0.8;
}

.timeline-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.timeline-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.timeline-card-time {
    font-size: 13px;
    font-weight: 500;
    color: var(--primary-color);
    background-color: var(--accent-light);
    padding: 4px 10px;
    border-radius: 12px;
}

.timeline-card-expand {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--accent-light);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--hover-transition);
}

.timeline-card-expand:hover {
    background-color: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.timeline-card-expand i {
    font-size: 12px;
    color: var(--primary-color);
    transition: var(--hover-transition);
}

.timeline-card-expand:hover i {
    color: white;
}

.timeline-card-summary {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--accent-light);
    border-radius: 8px;
    color: var(--text-color);
    border-left: 3px solid var(--accent-color);
    animation: fadeIn 0.3s ease;
}

.timeline-card-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 8px;
    padding-bottom: 5px;
}

.timeline-card-image {
    width: 100%;
    height: 70px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s;
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
}

.timeline-card-image:hover {
    transform: scale(1.05);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-md);
}

.timeline-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: var(--text-light);
}

.timeline-loading i {
    margin-right: 10px;
    font-size: 24px;
    color: var(--accent-color);
    animation: spin 1.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-light);
    font-size: 16px;
    text-align: center;
}

.no-data i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--accent-color);
    opacity: 0.5;
}

/* Chatbot Section */
.chatbot-section {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    position: relative;
}

.chatbot-header {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chatbot-header i {
    color: var(--accent-color);
    font-size: 20px;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    background-color: var(--card-color);
    border-radius: 12px;
    overflow: hidden;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-color: rgba(245, 247, 250, 0.5);
    scrollbar-width: thin;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-color);
}

.message {
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    animation: fadeIn 0.3s;
    box-shadow: var(--shadow-sm);
    line-height: 1.5;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 4px;
    margin-left: 20px;
}

.message.user::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -10px;
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    clip-path: polygon(0 0, 0% 100%, 100% 100%);
}

.message.bot {
    align-self: flex-start;
    background-color: white;
    color: var(--text-color);
    border-bottom-left-radius: 4px;
    border: 1px solid var(--border-color);
    margin-right: 20px;
}

.message.bot::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 20px;
    height: 20px;
    background-color: white;
    clip-path: polygon(100% 0, 0% 100%, 100% 100%);
    border-left: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.message.system {
    align-self: center;
    background-color: var(--accent-light);
    color: var(--primary-color);
    border-radius: 12px;
    width: 90%;
    text-align: center;
    font-weight: 500;
    border: 1px solid rgba(0, 163, 255, 0.2);
}

.message.typing {
    align-self: flex-start;
    background-color: white;
    color: var(--text-color);
    border-bottom-left-radius: 4px;
    border: 1px solid var(--border-color);
}

.message.typing p {
    display: flex;
    align-items: center;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--accent-color);
    border-radius: 50%;
    opacity: 0.6;
    animation: typingPulse 1.4s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingPulse {
    0%, 100% { transform: scale(0.7); opacity: 0.4; }
    50% { transform: scale(1); opacity: 0.9; }
}

.message-content {
    font-size: 14px;
}

.message-content p {
    margin-bottom: 8px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.message-image {
    width: 110px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
}

.message-image:hover {
    transform: scale(1.05);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-md);
}

.chat-input {
    display: flex;
    margin-top: 15px;
    border-top: 1px solid var(--border-color);
    padding: 16px;
    background-color: white;
    position: relative;
}

.chat-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--accent-color) 50%,
        transparent 100%
    );
    opacity: 0.5;
}

#chat-input {
    flex-grow: 1;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 24px;
    font-size: 14px;
    background-color: var(--background-color);
    transition: var(--hover-transition);
    box-shadow: var(--shadow-inner);
}

#chat-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px var(--accent-light);
}

#send-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--hover-transition);
    box-shadow: var(--shadow-sm);
}

#send-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

#send-btn:active {
    transform: translateY(0);
}

#send-btn i {
    font-size: 16px;
}

/* Chatbot Toggle Button (for mobile) */
.chatbot-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: none;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    z-index: 100;
    transition: var(--hover-transition);
}

.chatbot-toggle:hover {
    transform: scale(1.05);
    background-color: var(--secondary-color);
}

.chatbot-toggle i {
    font-size: 24px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow: auto;
    backdrop-filter: blur(5px);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: var(--card-color);
    margin: 3% auto;
    padding: 25px;
    border-radius: 16px;
    max-width: 900px;
    position: relative;
    box-shadow: var(--shadow-lg);
    animation: modalSlideIn 0.4s ease;
    border: 1px solid rgba(0,0,0,0.1);
}

@keyframes modalSlideIn {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    color: var(--text-light);
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--hover-transition);
    z-index: 10;
}

.close:hover {
    background-color: rgba(0,0,0,0.05);
    color: var(--error-color);
}

#modal-image {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
}

.modal-info {
    padding: 15px 0;
    border-top: 1px solid var(--border-color);
    position: relative;
}

.modal-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--accent-color) 50%,
        transparent 100%
    );
    opacity: 0.5;
}

#modal-timestamp {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 8px;
    display: inline-block;
    background-color: var(--accent-light);
    padding: 4px 12px;
    border-radius: 12px;
}

#modal-description {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
    main {
        grid-template-columns: 1fr 350px;
        gap: 20px;
    }
}

@media (max-width: 1024px) {
    main {
        grid-template-columns: 1fr;
    }

    .chatbot-section {
        height: 500px;
    }

    .timeline-drag-handle {
        width: 150px;
    }
}

@media (max-width: 768px) {
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .header-controls {
        width: 100%;
        flex-direction: column;
        gap: 12px;
    }

    .search-container {
        width: 100%;
    }

    #search-input {
        width: 100%;
    }

    .timeline-controls {
        flex-direction: column;
        gap: 15px;
    }

    .timeline-view {
        height: 250px;
    }

    .timeline-card {
        padding: 15px;
    }

    .timeline-batch {
        min-width: 280px;
        margin: 0 20px;
    }

    .modal-content {
        width: 90%;
        margin: 10% auto;
        padding: 20px;
    }

    /* Show chatbot toggle button on mobile */
    .chatbot-toggle {
        display: flex;
    }

    /* Hide chatbot section by default on mobile */
    .chatbot-section.mobile-hidden {
        display: none;
    }
}

@media (max-width: 480px) {
    .app-container {
        padding: 12px;
    }

    section {
        padding: 16px;
        border-radius: 12px;
    }

    section h2 {
        font-size: 18px;
    }

    .timeline-card {
        padding: 12px;
    }

    .timeline-card-images {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    }

    .timeline-card-image {
        height: 55px;
    }

    .message {
        max-width: 95%;
        padding: 10px 14px;
    }

    .timeline-view {
        height: 220px;
    }

    .timeline-scrubber {
        height: 40px;
    }

    .timeline-drag-handle {
        width: 100px;
    }

    .timeline-zoom-controls {
        gap: 10px;
    }

    .zoom-btn {
        padding: 5px 10px;
    }

    .modal-content {
        padding: 15px;
        margin: 15% auto;
    }
}

/* All Captures Page Styles */
.all-captures-content {
    padding: 20px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.page-title p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
}

.capture-count {
    font-size: 1.2rem;
    color: var(--accent-color);
    font-weight: 600;
}

.search-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
}

.search-bar {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-bar i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-bar input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 10px 20px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.captures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.capture-card {
    background: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.capture-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.capture-image-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.capture-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.capture-card:hover .capture-image-container img {
    transform: scale(1.05);
}

.capture-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.capture-card:hover .capture-overlay {
    opacity: 1;
}

.view-btn, .download-btn {
    padding: 12px;
    background: var(--accent-color);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover, .download-btn:hover {
    background: var(--accent-hover);
    transform: scale(1.1);
}

.capture-info {
    padding: 15px;
}

.capture-timestamp {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.capture-timestamp i {
    color: var(--accent-color);
}

.capture-description {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 12px;
    line-height: 1.4;
}

.capture-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.confidence {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    background: var(--accent-color);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.loading-captures, .no-captures, .error-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.loading-captures i, .no-captures i, .error-message i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--accent-color);
}

.loading-captures p, .no-captures p, .error-message p {
    font-size: 1.2rem;
    margin: 0;
}

.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* History Timeline Specific Styles */
.timeline-content {
    padding: 20px;
    height: calc(100vh - 120px);
    overflow: hidden;
}

.timeline-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.timeline-filters {
    display: flex;
    gap: 20px;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.timeline-input {
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
}

.timeline-actions {
    display: flex;
    gap: 10px;
}

.timeline-btn {
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.timeline-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.timeline-scrubber-container {
    margin-bottom: 20px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.timeline-scrubber {
    position: relative;
    height: 60px;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.timeline-minimap {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--secondary-color) 50%,
        var(--accent-color) 100%);
    opacity: 0.3;
}

.timeline-minimap-points {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.timeline-point {
    position: absolute;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    opacity: 0.8;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 2px;
    min-height: 20%;
}

.timeline-point:hover {
    width: 6px;
    background: var(--accent-color);
    opacity: 1;
    transform: scaleY(1.1);
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

.timeline-drag-handle {
    position: absolute;
    top: 0;
    left: 10%;
    width: 80px;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    cursor: grab;
    transition: all 0.3s ease;
}

.timeline-drag-handle:active {
    cursor: grabbing;
    background: rgba(255, 255, 255, 0.3);
}

.timeline-zoom-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.zoom-btn {
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.zoom-btn:hover {
    background: var(--primary-color);
    color: white;
}

.timeline-view {
    height: calc(100vh - 400px);
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.timeline-container {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
}

.timeline {
    position: relative;
}

.timeline-group {
    margin-bottom: 30px;
    position: relative;
}

.timeline-group-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 15px 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-group-time {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 15px;
    min-width: 200px;
}

.timeline-group-count {
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--card-bg);
    padding: 6px 12px;
    border-radius: 16px;
    margin-right: 15px;
}

.timeline-group-date {
    font-size: 14px;
    color: var(--text-secondary);
    margin-right: auto;
    font-weight: 500;
}

.timeline-group-toggle {
    margin-left: auto;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    transition: color 0.3s ease;
}

.timeline-group-toggle:hover {
    color: var(--primary-color);
}

.timeline-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding-left: 20px;
}

.timeline-image-card {
    background: var(--bg-secondary);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.timeline-image-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.timeline-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.timeline-image-info {
    padding: 10px;
}

.timeline-image-time {
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 5px;
}

.timeline-image-description {
    font-size: 11px;
    color: var(--text-secondary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Timeline View Toggle */
.timeline-view-toggle {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
}

.view-toggle-btn {
    padding: 10px 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.view-toggle-btn:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.view-toggle-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.view-toggle-btn i {
    margin-right: 8px;
}

/* Horizontal Timeline View */
.horizontal-timeline-view {
    height: calc(100vh - 400px);
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.horizontal-timeline-container {
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 20px;
}

.horizontal-timeline {
    display: flex;
    gap: 20px;
    height: 100%;
    min-width: max-content;
    align-items: flex-start;
}

.horizontal-timeline-group {
    display: flex;
    flex-direction: column;
    min-width: 300px;
    max-width: 350px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    height: fit-content;
    max-height: calc(100vh - 500px);
}

.horizontal-timeline-header {
    padding: 15px 20px;
    background: var(--primary-color);
    color: white;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.horizontal-timeline-time {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.horizontal-timeline-count {
    font-size: 12px;
    opacity: 0.9;
}

.horizontal-timeline-images {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
    max-height: calc(100vh - 600px);
}

.horizontal-image-card {
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.horizontal-image-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.horizontal-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.horizontal-image-info {
    padding: 10px;
}

.horizontal-image-time {
    font-size: 11px;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 5px;
}

.horizontal-image-description {
    font-size: 10px;
    color: var(--text-secondary);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Vertical Timeline Controls */
.vertical-timeline-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px 20px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.timeline-search {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.timeline-search input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
}

.timeline-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.timeline-search i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
}

.timeline-control-buttons {
    display: flex;
    gap: 10px;
}

.timeline-control-btn {
    padding: 8px 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 500;
}

.timeline-control-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.timeline-control-btn i {
    margin-right: 6px;
}

/* Enhanced Timeline Group Styles */
.timeline-group.collapsed {
    opacity: 0.7;
}

.timeline-group.collapsed .timeline-group-header {
    border-left-color: var(--text-secondary);
}

.timeline-group:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.timeline-group-toggle:hover {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
}

/* Smooth animations for timeline images */
.timeline-images {
    transition: all 0.3s ease;
}

.timeline-image-card {
    animation: fadeInUp 0.3s ease forwards;
    opacity: 0;
    transform: translateY(10px);
}

.timeline-image-card:nth-child(1) { animation-delay: 0.1s; }
.timeline-image-card:nth-child(2) { animation-delay: 0.2s; }
.timeline-image-card:nth-child(3) { animation-delay: 0.3s; }
.timeline-image-card:nth-child(4) { animation-delay: 0.4s; }
.timeline-image-card:nth-child(5) { animation-delay: 0.5s; }
.timeline-image-card:nth-child(n+6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced image hover effects */
.timeline-image-card:hover .timeline-image {
    transform: scale(1.05);
}

.timeline-image, .horizontal-image {
    transition: transform 0.3s ease;
}

/* Image loading states */
.image-container {
    position: relative;
    overflow: hidden;
}

.image-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-secondary);
    font-size: 18px;
    z-index: 1;
}

.timeline-image.loaded + .image-loading,
.horizontal-image.loaded + .image-loading {
    display: none;
}

.timeline-image.error,
.horizontal-image.error {
    opacity: 0.6;
    filter: grayscale(100%);
}

.timeline-image,
.horizontal-image {
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.timeline-image.loaded,
.horizontal-image.loaded {
    opacity: 1;
}

/* Loading state improvements */
.timeline-loading {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Search highlighting */
.search-highlight {
    border: 2px solid var(--primary-color) !important;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3) !important;
    animation: searchPulse 1s ease-in-out;
}

@keyframes searchPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* Search results indicator */
.search-results-info {
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    border-radius: 6px;
    font-size: 12px;
    margin-left: 10px;
    animation: fadeIn 0.3s ease;
}
