
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Bell, Camera, AlertTriangle, Eye, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";

interface SensorEventsTableProps {
  onSelectEvent: (event: any) => void;
}

export function SensorEventsTable({ onSelectEvent }: SensorEventsTableProps) {
  const { data: sensorEvents = [], isLoading } = useQuery({
    queryKey: ['sensorEvents'],
    queryFn: api.getSensorEvents,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Format events for display
  const events = sensorEvents.slice(0, 5).map((event: any) => ({
    id: event.id,
    time: new Date(event.timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }),
    type: "camera",
    icon: Camera,
    thumbnail: event.images && event.images.length > 0
      ? `/captured_images/${event.images[0]}`
      : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNyAxM0wxOSAxNUwyMyAxMUwyOSAxN0gzM1YyM0gxM1YxN0wxNyAxM1oiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',
    description: event.event || 'Activity detected'
  }));

  return (
    <Card className="bg-card border-border">
      <CardHeader>
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <Bell className="h-5 w-5 text-primary" />
          Recent Sensor Events
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground text-sm">Loading events...</span>
          </div>
        ) : events.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <p>No sensor events available</p>
          </div>
        ) : (
          <>
            <div className="space-y-3">
              {events.map((event) => (
                <div
                  key={event.id}
                  className="flex items-center gap-4 p-3 bg-secondary rounded-lg hover:bg-accent transition-colors cursor-pointer group"
                  onClick={() => onSelectEvent(event)}
                >
                  <div className="text-muted-foreground font-mono text-sm min-w-[70px]">
                    {event.time}
                  </div>

                  <div className="flex items-center gap-2">
                    <event.icon className="h-4 w-4 text-primary" />
                    <img
                      src={event.thumbnail}
                      alt="Event thumbnail"
                      className="w-8 h-8 rounded object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNyAxM0wxOSAxNUwyMyAxMUwyOSAxN0gzM1YyM0gxM1YxN0wxNyAxM1oiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                      }}
                    />
                  </div>

                  <div className="flex-1 text-secondary-foreground text-sm">
                    {event.description}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity text-primary hover:text-primary/80"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            <div className="mt-4 text-center">
              <Button variant="outline" className="border-border text-muted-foreground hover:border-primary hover:text-primary">
                View All Events
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
