<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Captures - AI Security Hub</title>
    <link rel="stylesheet" href="/static/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>AI Security Hub</span>
                </div>
            </div>
            <nav class="sidebar-nav">
                <a href="/static/index.html" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-video"></i>
                    <span>Live View</span>
                </a>
                <a href="/static/all-captures.html" class="nav-item active">
                    <i class="fas fa-camera"></i>
                    <span>All Captures</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-history"></i>
                    <span>History Timeline</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-bell"></i>
                    <span>Sensor Events</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-search"></i>
                    <span>Object Search</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Alerts</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1><i class="fas fa-shield-alt"></i> </h1>
                </div>
                <div class="header-center">
                    <h2>All Captures</h2>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span>System Online</span>
                    </div>
                    <div class="user-menu">
                        <i class="fas fa-user-circle"></i>
                        <i class="fas fa-bell"></i>
                    </div>
                </div>
            </header>

            <!-- All Captures Content -->
            <div class="all-captures-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title">
                        <h1>All Captures</h1>
                        <p>Browse through all captured images and events</p>
                    </div>
                    <div class="capture-count">
                        <span id="total-captures">0</span> captures
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="search-filters">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-input" placeholder="Search captures by description or tags...">
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="motion">Motion</button>
                        <button class="filter-btn" data-filter="people">People</button>
                        <button class="filter-btn" data-filter="animals">Animals</button>
                    </div>
                </div>

                <!-- Captures Grid -->
                <div class="captures-grid" id="captures-grid">
                    <div class="loading-captures">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading captures...</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Image Modal -->
        <div id="image-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <img id="modal-image" src="">
                <div class="modal-info">
                    <p id="modal-timestamp"></p>
                    <p id="modal-description"></p>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/all-captures.js"></script>
</body>
</html>
