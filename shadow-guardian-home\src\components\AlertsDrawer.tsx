
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface AlertsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AlertsDrawer({ isOpen, onClose }: AlertsDrawerProps) {
  if (!isOpen) return null;

  const recentAlerts = [
    {
      id: 1,
      time: "2 min ago",
      type: "Intrusion",
      severity: "high",
      message: "Unauthorized person detected in backyard",
      image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=60&h=60&fit=crop"
    },
    {
      id: 2,
      time: "15 min ago",
      type: "Animal",
      severity: "low",
      message: "Cat detected in kitchen area",
      image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=60&h=60&fit=crop"
    },
    {
      id: 3,
      time: "1 hour ago",
      type: "Object",
      severity: "medium",
      message: "Package left unattended at front door",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=60&h=60&fit=crop"
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-600';
      case 'medium': return 'bg-yellow-600';
      case 'low': return 'bg-green-600';
      default: return 'bg-gray-600';
    }
  };

  return (
    <div className="fixed inset-0 z-50">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute inset-y-0 right-0 w-96 bg-card border-l border-border animate-slide-in">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <h3 className="text-lg font-semibold text-card-foreground">Recent Alerts</h3>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose} className="text-muted-foreground hover:text-card-foreground">
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Controls */}
          <div className="p-4 border-b border-border">
            <div className="flex gap-2">
              <Button size="sm" className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground">
                <CheckCheck className="h-4 w-4 mr-2" />
                Acknowledge All
              </Button>
              <Button variant="outline" size="sm" className="border-border text-muted-foreground hover:border-primary hover:text-primary">
                <VolumeX className="h-4 w-4 mr-2" />
                Mute
              </Button>
            </div>
          </div>

          {/* Alerts List */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {recentAlerts.map((alert) => (
              <div key={alert.id} className="bg-secondary rounded-lg p-4 border-l-4 border-l-red-400 hover:bg-accent transition-colors">
                <div className="flex items-start gap-3">
                  <img
                    src={alert.image}
                    alt="Alert thumbnail"
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge className={`text-white ${getSeverityColor(alert.severity)}`}>
                        {alert.type}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {alert.time}
                      </div>
                    </div>
                    <p className="text-secondary-foreground text-sm">{alert.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="border-t border-border p-4">
            <Button variant="outline" className="w-full border-border text-muted-foreground hover:border-primary hover:text-primary">
              View All Alerts History
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
