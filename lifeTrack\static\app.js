// DOM Elements
const capturesContainer = document.getElementById('captures-container');
const lastCaptureEl = document.getElementById('last-capture');
const lastSensorEl = document.getElementById('last-sensor');
const overallStatusEl = document.getElementById('overall-status');
const cpuUsageEl = document.getElementById('cpu-usage');
const motionCountEl = document.getElementById('motion-count');
const activityCountEl = document.getElementById('activity-count');
const otherCountEl = document.getElementById('other-count');
const totalAlertsEl = document.getElementById('total-alerts');
const viewAllBtn = document.getElementById('view-all-btn');
const modal = document.getElementById('image-modal');
const modalImage = document.getElementById('modal-image');
const modalTimestamp = document.getElementById('modal-timestamp');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Global state
let analysisData = [];
let allImages = [];
let systemStats = {
    lastCapture: null,
    lastSensor: null,
    cpuUsage: 0,
    alerts: {
        motion: 0,
        activity: 0,
        other: 0,
        total: 0
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    loadData();
    setupEventListeners();
    updateSystemHealth();
    setInterval(updateSystemHealth, 30000); // Update every 30 seconds
});

// Load data from the server
async function loadData() {
    try {
        // Fetch analysis data
        const analysisResponse = await fetch('/api/analysis');
        analysisData = await analysisResponse.json();

        // Fetch image list
        const imagesResponse = await fetch('/api/images');
        allImages = await imagesResponse.json();

        // Display recent captures
        displayRecentCaptures();
        updateAlertCounts();
        updateAlertsPanel();
    } catch (error) {
        console.error('Error loading data:', error);
        capturesContainer.innerHTML = `
            <div class="no-captures">
                <p>Failed to load captures. Please try again.</p>
            </div>
        `;
    }
}

// Display recent captures in the dashboard
function displayRecentCaptures() {
    if (!analysisData || analysisData.length === 0) {
        capturesContainer.innerHTML = `
            <div class="no-captures">
                <p>No captures available yet.</p>
            </div>
        `;
        return;
    }

    // Sort by most recent first and take only the latest 6 captures for preview
    const recentCaptures = [...analysisData]
        .sort((a, b) => new Date(b.interval_start) - new Date(a.interval_start))
        .slice(0, 6);

    capturesContainer.innerHTML = '';

    recentCaptures.forEach(batch => {
        if (batch.images && batch.images.length > 0) {
            // Use the first image from each batch
            const imageName = batch.images[0];
            const captureEl = createCaptureItem(batch, imageName);
            capturesContainer.appendChild(captureEl);
        }
    });
}

// Create a capture item element
function createCaptureItem(batch, imageName) {
    const captureEl = document.createElement('div');
    captureEl.className = 'capture-item';

    const timestamp = new Date(batch.interval_start);
    const timeString = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const dateString = timestamp.toLocaleDateString();

    captureEl.innerHTML = `
        <img class="capture-image" src="/api/images/${imageName}" alt="Capture" loading="lazy">
        <div class="capture-info">
            <div class="capture-time">${timeString} - ${dateString}</div>
            <div class="capture-description">${batch.summary || 'Activity detected'}</div>
        </div>
    `;

    // Add click event to open modal
    captureEl.addEventListener('click', () => {
        openImageModal(`/api/images/${imageName}`, batch.interval_start, batch.summary);
    });

    return captureEl;
}

// Update alert counts in the dashboard
function updateAlertCounts() {
    if (!analysisData || analysisData.length === 0) {
        motionCountEl.textContent = '0';
        activityCountEl.textContent = '0';
        otherCountEl.textContent = '0';
        totalAlertsEl.textContent = '0';
        return;
    }

    // Count different types of alerts based on analysis data
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayData = analysisData.filter(batch => {
        const batchDate = new Date(batch.interval_start);
        batchDate.setHours(0, 0, 0, 0);
        return batchDate.getTime() === today.getTime();
    });

    // Simple categorization based on summary content
    let motionCount = 0;
    let activityCount = 0;
    let otherCount = 0;

    todayData.forEach(batch => {
        const summary = (batch.summary || '').toLowerCase();
        if (summary.includes('motion') || summary.includes('movement')) {
            motionCount++;
        } else if (summary.includes('activity') || summary.includes('person') || summary.includes('people')) {
            activityCount++;
        } else {
            otherCount++;
        }
    });

    const totalCount = motionCount + activityCount + otherCount;

    motionCountEl.textContent = motionCount;
    activityCountEl.textContent = activityCount;
    otherCountEl.textContent = otherCount;
    totalAlertsEl.textContent = totalCount;

    // Update system stats
    systemStats.alerts = {
        motion: motionCount,
        activity: activityCount,
        other: otherCount,
        total: totalCount
    };

    // Update notification badge
    updateNotificationCount();
}

// Update system health information
function updateSystemHealth() {
    // Update last capture time
    if (analysisData && analysisData.length > 0) {
        const latestBatch = analysisData.reduce((latest, batch) => {
            return new Date(batch.interval_start) > new Date(latest.interval_start) ? batch : latest;
        });

        const lastCaptureTime = new Date(latestBatch.interval_start);
        const timeAgo = getTimeAgo(lastCaptureTime);
        lastCaptureEl.textContent = timeAgo;
        systemStats.lastCapture = lastCaptureTime;
    } else {
        lastCaptureEl.textContent = 'No data';
    }

    // Update last sensor event (using same data for now)
    lastSensorEl.textContent = lastCaptureEl.textContent;

    // Update CPU usage (simulated for now)
    const cpuUsage = Math.floor(Math.random() * 30) + 10; // Random between 10-40%
    cpuUsageEl.textContent = `${cpuUsage}%`;
    systemStats.cpuUsage = cpuUsage;

    // Update overall status
    const now = new Date();
    const lastUpdate = systemStats.lastCapture;

    if (!lastUpdate || (now - lastUpdate) > 300000) { // 5 minutes
        overallStatusEl.textContent = 'System Issues Detected';
        overallStatusEl.className = 'status-badge error';
    } else {
        overallStatusEl.textContent = 'System Online';
        overallStatusEl.className = 'status-badge success';
    }
}

// Get time ago string
function getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
}

// Open the image modal
function openImageModal(src, timestamp, description) {
    modalImage.src = src;

    // Format the timestamp
    const date = new Date(timestamp);
    modalTimestamp.textContent = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

    modalDescription.textContent = description || 'Activity detected';
    modal.style.display = 'block';
}

// Set up event listeners
function setupEventListeners() {
    // Modal close events
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // View All button
    if (viewAllBtn) {
        viewAllBtn.addEventListener('click', () => {
            // Navigate to all captures view (could be implemented later)
            console.log('View all captures clicked');
        });
    }

    // Navigation items (for future implementation)
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();

            // Remove active class from all items
            document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

            // Add active class to clicked item
            item.classList.add('active');

            // Handle navigation (for future implementation)
            const text = item.querySelector('span').textContent;
            console.log(`Navigating to: ${text}`);
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // ESC to close modal
        if (e.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = 'none';
        }
    });
}

// Add a chatbot API endpoint to handle more complex queries
async function queryChatbot(message) {
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: message }),
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('Chatbot response:', result); // Debug log
        return result;
    } catch (error) {
        console.error('Error querying chatbot:', error);
        return {
            answer: "Sorry, I'm having trouble processing your request right now. Please try again.",
            images_to_show: []
        };
    }
}

// Alerts Panel Functions
function toggleAlertsPanel() {
    const alertsPanel = document.getElementById('alerts-panel');
    const alertsOverlay = document.getElementById('alerts-overlay');

    if (alertsPanel.classList.contains('open')) {
        // Close panel
        alertsPanel.classList.remove('open');
        alertsOverlay.classList.remove('active');
        document.body.style.overflow = '';
    } else {
        // Open panel
        alertsPanel.classList.add('open');
        alertsOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function acknowledgeAllAlerts() {
    // Update notification badge
    const notificationBadge = document.getElementById('notification-count');
    notificationBadge.textContent = '0';
    notificationBadge.style.display = 'none';

    // Add visual feedback
    const alertItems = document.querySelectorAll('.alert-item');
    alertItems.forEach(item => {
        item.style.opacity = '0.5';
        item.style.pointerEvents = 'none';
    });

    // Show success message (you can customize this)
    console.log('All alerts acknowledged');

    // Optional: Close the panel after acknowledging
    setTimeout(() => {
        toggleAlertsPanel();
    }, 1000);
}

function muteAlerts() {
    const muteBtn = document.querySelector('.mute-btn');
    const icon = muteBtn.querySelector('i');

    if (icon.classList.contains('fa-volume-mute')) {
        // Unmute
        icon.classList.remove('fa-volume-mute');
        icon.classList.add('fa-volume-up');
        muteBtn.innerHTML = '<i class="fas fa-volume-up"></i> Unmute';
        console.log('Alerts unmuted');
    } else {
        // Mute
        icon.classList.remove('fa-volume-up');
        icon.classList.add('fa-volume-mute');
        muteBtn.innerHTML = '<i class="fas fa-volume-mute"></i> Mute';
        console.log('Alerts muted');
    }
}

function viewAllAlerts() {
    // Navigate to alerts history page or show all alerts
    console.log('Viewing all alerts history');
    // You can implement navigation to a dedicated alerts page here
    // For now, we'll just close the panel
    toggleAlertsPanel();
}

// Update notification count based on alerts
function updateNotificationCount() {
    const notificationBadge = document.getElementById('notification-count');
    const totalAlerts = systemStats.alerts.total;

    if (totalAlerts > 0) {
        notificationBadge.textContent = totalAlerts > 99 ? '99+' : totalAlerts.toString();
        notificationBadge.style.display = 'flex';
    } else {
        notificationBadge.style.display = 'none';
    }
}

// Generate recent alerts from analysis data
function generateRecentAlerts() {
    if (!analysisData || analysisData.length === 0) {
        return [];
    }

    // Get recent analysis data (last 24 hours)
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentData = analysisData
        .filter(batch => new Date(batch.interval_start) > yesterday)
        .sort((a, b) => new Date(b.interval_start) - new Date(a.interval_start))
        .slice(0, 10); // Get latest 10 alerts

    return recentData.map(batch => {
        const summary = (batch.summary || '').toLowerCase();
        let alertType = 'object';
        let alertTypeLabel = 'Object';

        if (summary.includes('person') || summary.includes('people') || summary.includes('human') || summary.includes('intruder')) {
            alertType = 'intrusion';
            alertTypeLabel = 'Intrusion';
        } else if (summary.includes('cat') || summary.includes('dog') || summary.includes('animal') || summary.includes('pet')) {
            alertType = 'animal';
            alertTypeLabel = 'Animal';
        }

        const alertTime = new Date(batch.interval_start);
        const timeAgo = getTimeAgo(alertTime);

        // Use the first image from the batch as thumbnail
        const thumbnailImage = batch.images && batch.images.length > 0
            ? `/api/images/${batch.images[0]}`
            : getDefaultThumbnail(alertType);

        return {
            type: alertType,
            typeLabel: alertTypeLabel.toUpperCase(),
            time: timeAgo,
            thumbnail: thumbnailImage,
            timestamp: batch.interval_start
        };
    });
}

// Get default thumbnail for alert type
function getDefaultThumbnail(alertType) {
    const thumbnails = {
        intrusion: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=80&h=80&fit=crop&crop=face',
        animal: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=80&h=80&fit=crop&crop=center',
        object: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=80&h=80&fit=crop&crop=center'
    };
    return thumbnails[alertType] || thumbnails.object;
}

// Update alerts panel with real data
function updateAlertsPanel() {
    const alertsContent = document.getElementById('alerts-content');
    const recentAlerts = generateRecentAlerts();

    if (recentAlerts.length === 0) {
        alertsContent.innerHTML = `
            <div class="no-alerts">
                <p>No recent alerts</p>
            </div>
        `;
        return;
    }

    alertsContent.innerHTML = recentAlerts.map(alert => `
        <div class="alert-item ${alert.type}" onclick="openAlertDetails('${alert.timestamp}')">
            <div class="alert-thumbnail">
                <img src="${alert.thumbnail}" alt="${alert.typeLabel} Alert" loading="lazy"
                     onerror="this.src='${getDefaultThumbnail(alert.type)}'">
            </div>
            <div class="alert-info">
                <div class="alert-type-badge ${alert.type}">${alert.typeLabel}</div>
                <div class="alert-time">${alert.time}</div>
            </div>
        </div>
    `).join('');
}

// Open alert details (can show full image or more info)
function openAlertDetails(timestamp) {
    const batch = analysisData.find(b => b.interval_start === timestamp);
    if (batch && batch.images && batch.images.length > 0) {
        openImageModal(`/api/images/${batch.images[0]}`, batch.interval_start, batch.summary);
        toggleAlertsPanel(); // Close alerts panel when opening modal
    }
}

// Chatbot Functions
let isChatbotOpen = false;

function toggleChatbot() {
    const chatbotWindow = document.getElementById('chatbot-window');
    const chatbotOverlay = document.getElementById('chatbot-overlay');

    isChatbotOpen = !isChatbotOpen;

    if (isChatbotOpen) {
        chatbotWindow.classList.add('open');
        chatbotOverlay.classList.add('active');
        document.getElementById('chatbot-input').focus();
    } else {
        chatbotWindow.classList.remove('open');
        chatbotOverlay.classList.remove('active');
    }
}

async function sendChatMessage() {
    const input = document.getElementById('chatbot-input');
    const sendButton = document.getElementById('chatbot-send');
    const messagesContainer = document.getElementById('chatbot-messages');

    const message = input.value.trim();
    if (!message) return;

    // Add user message
    addChatMessage(message, 'user');
    input.value = '';

    // Disable send button and show typing indicator
    sendButton.disabled = true;
    showTypingIndicator();

    try {
        // Use the existing queryChatbot function
        const response = await queryChatbot(message);

        // Remove typing indicator
        removeTypingIndicator();

        // Check if we got an error response
        if (response.error) {
            addChatMessage(`Error: ${response.error}`, 'bot');
            return;
        }

        // Add bot response
        const botMessage = response.answer || response.response || 'Sorry, I could not process your request.';
        addChatMessage(botMessage, 'bot');

        // Show related images if any
        const images = response.images_to_show || response.images || [];
        if (images && images.length > 0) {
            addRelatedImages(images);
        }

    } catch (error) {
        console.error('Chat error:', error);
        removeTypingIndicator();
        addChatMessage(`Sorry, I encountered an error: ${error.message}. Please make sure the server is running.`, 'bot');
    } finally {
        sendButton.disabled = false;
    }
}

function addChatMessage(content, sender) {
    const messagesContainer = document.getElementById('chatbot-messages');

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;

    messageDiv.appendChild(contentDiv);
    messagesContainer.appendChild(messageDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function showTypingIndicator() {
    const messagesContainer = document.getElementById('chatbot-messages');

    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-indicator';
    typingDiv.id = 'typing-indicator';

    typingDiv.innerHTML = `
        <div class="typing-indicator">
            <span>AI is thinking</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    `;

    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function addRelatedImages(imageFilenames) {
    const messagesContainer = document.getElementById('chatbot-messages');

    const imagesDiv = document.createElement('div');
    imagesDiv.className = 'message bot-message';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `
        <div>Here are the related images:</div>
        <div class="related-images">
            ${imageFilenames.map(filename => `
                <div class="related-image" onclick="openImageModal('/api/images/${filename}', '', 'Related image')">
                    <img src="/api/images/${filename}" alt="Related image" loading="lazy">
                </div>
            `).join('')}
        </div>
    `;

    imagesDiv.appendChild(contentDiv);
    messagesContainer.appendChild(imagesDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Add keyboard shortcuts and event listeners
document.addEventListener('keydown', (e) => {
    // Press 'A' to toggle alerts panel
    if (e.key === 'a' || e.key === 'A') {
        if (!e.ctrlKey && !e.altKey && !e.metaKey) {
            // Only if not typing in an input field
            if (document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
                toggleAlertsPanel();
            }
        }
    }

    // Press 'C' to toggle chatbot
    if (e.key === 'c' || e.key === 'C') {
        if (!e.ctrlKey && !e.altKey && !e.metaKey) {
            // Only if not typing in an input field
            if (document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
                toggleChatbot();
            }
        }
    }

    // ESC to close panels
    if (e.key === 'Escape') {
        const alertsPanel = document.getElementById('alerts-panel');
        const chatbotWindow = document.getElementById('chatbot-window');

        if (alertsPanel.classList.contains('open')) {
            toggleAlertsPanel();
        } else if (chatbotWindow.classList.contains('open')) {
            toggleChatbot();
        }
    }

    // Enter to send chat message
    if (e.key === 'Enter' && document.activeElement.id === 'chatbot-input') {
        e.preventDefault();
        sendChatMessage();
    }
});
