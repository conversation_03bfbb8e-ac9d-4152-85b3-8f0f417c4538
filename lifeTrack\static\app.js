// DOM Elements
const capturesContainer = document.getElementById('captures-container');
const lastCaptureEl = document.getElementById('last-capture');
const lastSensorEl = document.getElementById('last-sensor');
const overallStatusEl = document.getElementById('overall-status');
const cpuUsageEl = document.getElementById('cpu-usage');
const motionCountEl = document.getElementById('motion-count');
const activityCountEl = document.getElementById('activity-count');
const otherCountEl = document.getElementById('other-count');
const totalAlertsEl = document.getElementById('total-alerts');
const viewAllBtn = document.getElementById('view-all-btn');
const modal = document.getElementById('image-modal');
const modalImage = document.getElementById('modal-image');
const modalTimestamp = document.getElementById('modal-timestamp');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Global state
let analysisData = [];
let allImages = [];
let systemStats = {
    lastCapture: null,
    lastSensor: null,
    cpuUsage: 0,
    alerts: {
        motion: 0,
        activity: 0,
        other: 0,
        total: 0
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    loadData();
    setupEventListeners();
    updateSystemHealth();
    setInterval(updateSystemHealth, 30000); // Update every 30 seconds
});

// Load data from the server
async function loadData() {
    try {
        // Fetch analysis data
        const analysisResponse = await fetch('/api/analysis');
        analysisData = await analysisResponse.json();

        // Fetch image list
        const imagesResponse = await fetch('/api/images');
        allImages = await imagesResponse.json();

        // Display recent captures
        displayRecentCaptures();
        updateAlertCounts();
    } catch (error) {
        console.error('Error loading data:', error);
        capturesContainer.innerHTML = `
            <div class="no-captures">
                <p>Failed to load captures. Please try again.</p>
            </div>
        `;
    }
}

// Display recent captures in the dashboard
function displayRecentCaptures() {
    if (!analysisData || analysisData.length === 0) {
        capturesContainer.innerHTML = `
            <div class="no-captures">
                <p>No captures available yet.</p>
            </div>
        `;
        return;
    }

    // Sort by most recent first and take the latest 12 captures
    const recentCaptures = [...analysisData]
        .sort((a, b) => new Date(b.interval_start) - new Date(a.interval_start))
        .slice(0, 12);

    capturesContainer.innerHTML = '';

    recentCaptures.forEach(batch => {
        if (batch.images && batch.images.length > 0) {
            // Use the first image from each batch
            const imageName = batch.images[0];
            const captureEl = createCaptureItem(batch, imageName);
            capturesContainer.appendChild(captureEl);
        }
    });
}

// Create a capture item element
function createCaptureItem(batch, imageName) {
    const captureEl = document.createElement('div');
    captureEl.className = 'capture-item';

    const timestamp = new Date(batch.interval_start);
    const timeString = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const dateString = timestamp.toLocaleDateString();

    captureEl.innerHTML = `
        <img class="capture-image" src="/api/images/${imageName}" alt="Capture" loading="lazy">
        <div class="capture-info">
            <div class="capture-time">${timeString} - ${dateString}</div>
            <div class="capture-description">${batch.summary || 'Activity detected'}</div>
        </div>
    `;

    // Add click event to open modal
    captureEl.addEventListener('click', () => {
        openImageModal(`/api/images/${imageName}`, batch.interval_start, batch.summary);
    });

    return captureEl;
}

// Update alert counts in the dashboard
function updateAlertCounts() {
    if (!analysisData || analysisData.length === 0) {
        motionCountEl.textContent = '0';
        activityCountEl.textContent = '0';
        otherCountEl.textContent = '0';
        totalAlertsEl.textContent = '0';
        return;
    }

    // Count different types of alerts based on analysis data
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayData = analysisData.filter(batch => {
        const batchDate = new Date(batch.interval_start);
        batchDate.setHours(0, 0, 0, 0);
        return batchDate.getTime() === today.getTime();
    });

    // Simple categorization based on summary content
    let motionCount = 0;
    let activityCount = 0;
    let otherCount = 0;

    todayData.forEach(batch => {
        const summary = (batch.summary || '').toLowerCase();
        if (summary.includes('motion') || summary.includes('movement')) {
            motionCount++;
        } else if (summary.includes('activity') || summary.includes('person') || summary.includes('people')) {
            activityCount++;
        } else {
            otherCount++;
        }
    });

    const totalCount = motionCount + activityCount + otherCount;

    motionCountEl.textContent = motionCount;
    activityCountEl.textContent = activityCount;
    otherCountEl.textContent = otherCount;
    totalAlertsEl.textContent = totalCount;

    // Update system stats
    systemStats.alerts = {
        motion: motionCount,
        activity: activityCount,
        other: otherCount,
        total: totalCount
    };
}

// Update system health information
function updateSystemHealth() {
    // Update last capture time
    if (analysisData && analysisData.length > 0) {
        const latestBatch = analysisData.reduce((latest, batch) => {
            return new Date(batch.interval_start) > new Date(latest.interval_start) ? batch : latest;
        });

        const lastCaptureTime = new Date(latestBatch.interval_start);
        const timeAgo = getTimeAgo(lastCaptureTime);
        lastCaptureEl.textContent = timeAgo;
        systemStats.lastCapture = lastCaptureTime;
    } else {
        lastCaptureEl.textContent = 'No data';
    }

    // Update last sensor event (using same data for now)
    lastSensorEl.textContent = lastCaptureEl.textContent;

    // Update CPU usage (simulated for now)
    const cpuUsage = Math.floor(Math.random() * 30) + 10; // Random between 10-40%
    cpuUsageEl.textContent = `${cpuUsage}%`;
    systemStats.cpuUsage = cpuUsage;

    // Update overall status
    const now = new Date();
    const lastUpdate = systemStats.lastCapture;

    if (!lastUpdate || (now - lastUpdate) > 300000) { // 5 minutes
        overallStatusEl.textContent = 'System Issues Detected';
        overallStatusEl.className = 'status-badge error';
    } else {
        overallStatusEl.textContent = 'System Online';
        overallStatusEl.className = 'status-badge success';
    }
}

// Get time ago string
function getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
}

// Open the image modal
function openImageModal(src, timestamp, description) {
    modalImage.src = src;

    // Format the timestamp
    const date = new Date(timestamp);
    modalTimestamp.textContent = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

    modalDescription.textContent = description || 'Activity detected';
    modal.style.display = 'block';
}

// Set up event listeners
function setupEventListeners() {
    // Modal close events
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // View All button
    if (viewAllBtn) {
        viewAllBtn.addEventListener('click', () => {
            // Navigate to all captures view (could be implemented later)
            console.log('View all captures clicked');
        });
    }

    // Navigation items (for future implementation)
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();

            // Remove active class from all items
            document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

            // Add active class to clicked item
            item.classList.add('active');

            // Handle navigation (for future implementation)
            const text = item.querySelector('span').textContent;
            console.log(`Navigating to: ${text}`);
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // ESC to close modal
        if (e.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = 'none';
        }
    });
}



// Update the minimap with points representing timeline items
function updateMinimap(data) {
    // Clear existing points
    timelineMinimapPoints.innerHTML = '';

    if (data.length === 0) return;

    // Create points for each data item
    data.forEach(() => {
        const point = document.createElement('div');
        point.className = 'minimap-point';
        timelineMinimapPoints.appendChild(point);
    });

    // Show drag handle
    timelineDragHandle.style.display = 'block';
}

// Initialize the drag handle
function initDragHandle() {
    // Position the handle at the start
    timelineDragHandle.style.left = '0px';

    // Set initial handle width based on viewport ratio
    calculateDimensions();
}

// Calculate dimensions for drag functionality
function calculateDimensions() {
    // Get the full width of the timeline content
    const timeline = document.querySelector('.timeline');
    if (!timeline) return;

    timelineWidth = timeline.scrollWidth;
    minimapWidth = timelineMinimap.clientWidth;

    // Calculate handle width as a proportion of visible area to total width
    const visibleRatio = timelineContainer.clientWidth / timelineWidth;
    handleWidth = Math.max(minimapWidth * visibleRatio, 100); // Minimum 100px for usability

    // Update handle width
    timelineDragHandle.style.width = `${handleWidth}px`;
}

// Handle timeline drag functionality
function handleTimelineDrag(e) {
    if (!isDragging) return;

    e.preventDefault();

    // Calculate how far the mouse has moved
    const x = e.clientX - dragStartX;

    // Calculate new position for the handle
    let newLeft = dragStartScrollLeft + x;

    // Constrain to minimap bounds
    newLeft = Math.max(0, Math.min(newLeft, minimapWidth - handleWidth));

    // Update handle position
    timelineDragHandle.style.left = `${newLeft}px`;

    // Calculate the corresponding scroll position for the timeline
    const scrollRatio = newLeft / (minimapWidth - handleWidth);
    const newScrollLeft = scrollRatio * (timelineWidth - timelineContainer.clientWidth);

    // Update timeline scroll position
    timelineContainer.scrollLeft = newScrollLeft;
}

// Handle zoom functionality
function handleZoom(direction) {
    // Store current scroll position ratio
    const scrollRatio = timelineContainer.scrollLeft / (timelineWidth - timelineContainer.clientWidth);

    if (direction === 'in') {
        timelineZoomLevel = Math.min(timelineZoomLevel + 0.1, 2); // Max zoom: 2x
    } else if (direction === 'out') {
        timelineZoomLevel = Math.max(timelineZoomLevel - 0.1, 0.5); // Min zoom: 0.5x
    } else {
        timelineZoomLevel = 1; // Reset to default
    }

    // Apply zoom to timeline
    const timeline = document.querySelector('.timeline');
    if (timeline) {
        timeline.style.transform = `scale(${timelineZoomLevel})`;
        timeline.style.transformOrigin = 'center top';

        // Recalculate dimensions after zoom
        setTimeout(() => {
            calculateDimensions();

            // Restore scroll position ratio
            if (timelineWidth > timelineContainer.clientWidth) {
                timelineContainer.scrollLeft = scrollRatio * (timelineWidth - timelineContainer.clientWidth);

                // Update drag handle position
                const handleLeft = (timelineContainer.scrollLeft / (timelineWidth - timelineContainer.clientWidth)) * (minimapWidth - handleWidth);
                timelineDragHandle.style.left = `${handleLeft}px`;
            }
        }, 50);
    }
}

// Update drag handle position when scrolling the timeline
function updateHandleOnScroll() {
    if (isDragging) return; // Don't update while user is dragging

    if (timelineWidth <= timelineContainer.clientWidth) {
        // If content fits without scrolling, position handle at start
        timelineDragHandle.style.left = '0px';
        return;
    }

    // Calculate handle position based on scroll position
    const scrollRatio = timelineContainer.scrollLeft / (timelineWidth - timelineContainer.clientWidth);
    const handleLeft = scrollRatio * (minimapWidth - handleWidth);

    // Update handle position
    timelineDragHandle.style.left = `${handleLeft}px`;
}

// Toggle chatbot visibility on mobile
function toggleChatbot() {
    chatbotSection.classList.toggle('mobile-hidden');
}

// Set up event listeners
function setupEventListeners() {
    // Timeline drag handle events
    timelineDragHandle.addEventListener('mousedown', (e) => {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartScrollLeft = parseInt(timelineDragHandle.style.left || '0', 10);
        document.body.style.cursor = 'grabbing';
    });

    document.addEventListener('mousemove', handleTimelineDrag);

    document.addEventListener('mouseup', () => {
        isDragging = false;
        document.body.style.cursor = '';
    });

    // Timeline scroll event
    timelineContainer.addEventListener('scroll', updateHandleOnScroll);

    // Zoom controls
    zoomInBtn.addEventListener('click', () => handleZoom('in'));
    zoomOutBtn.addEventListener('click', () => handleZoom('out'));
    zoomResetBtn.addEventListener('click', () => handleZoom('reset'));

    // Window resize event
    window.addEventListener('resize', calculateDimensions);

    // Chatbot toggle for mobile
    chatbotToggle.addEventListener('click', toggleChatbot);

    // Send button click
    sendBtn.addEventListener('click', () => {
        const query = chatInput.value.trim();
        if (query) {
            processQuery(query);
            chatInput.value = '';
        }
    });

    // Enter key in chat input
    chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const query = chatInput.value.trim();
            if (query) {
                processQuery(query);
                chatInput.value = '';
            }
        }
    });

    // Refresh button
    refreshBtn.addEventListener('click', loadData);

    // Search button
    searchBtn.addEventListener('click', () => {
        processData();
        renderTimeline(filteredData);
    });

    // Search input enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            processData();
            renderTimeline(filteredData);
        }
    });

    // Date filter change
    dateFilter.addEventListener('change', () => {
        processData();
        renderTimeline(filteredData);
    });

    // Sort select change
    sortSelect.addEventListener('change', () => {
        processData();
        renderTimeline(filteredData);
    });

    // Close modal
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    // Click outside modal to close
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Add a chatbot API endpoint to handle more complex queries
async function queryChatbot(message) {
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: message }),
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        return await response.json();
    } catch (error) {
        console.error('Error querying chatbot:', error);
        return {
            response: "Sorry, I'm having trouble processing your request right now.",
            images: []
        };
    }
}
