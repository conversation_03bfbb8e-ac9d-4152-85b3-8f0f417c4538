
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Camera, Activity, Clock, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";

export function SystemHealthCard() {
  const { data: systemHealth, isLoading } = useQuery({
    queryKey: ['systemHealth'],
    queryFn: api.getSystemHealth,
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  const { data: recentCaptures = [] } = useQuery({
    queryKey: ['recentCaptures'],
    queryFn: api.getRecentCaptures,
    refetchInterval: 30000,
  });

  const { data: sensorEvents = [] } = useQuery({
    queryKey: ['sensorEvents'],
    queryFn: api.getSensorEvents,
    refetchInterval: 30000,
  });

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  const lastCapture = recentCaptures[0];
  const lastEvent = sensorEvents[0];

  return (
    <Card className="bg-card border-border hover:border-primary transition-colors">
      <CardHeader>
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <Activity className="h-5 w-5 text-primary" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground text-sm">Loading...</span>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Camera className="h-4 w-4" />
                <span>Last Capture</span>
              </div>
              <span className="text-card-foreground font-mono">
                {lastCapture ? getTimeAgo(lastCapture.timestamp) : 'No data'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Last Sensor Event</span>
              </div>
              <span className="text-card-foreground font-mono">
                {lastEvent ? getTimeAgo(lastEvent.timestamp) : 'No data'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Overall Status</span>
              <Badge className={systemHealth?.status === 'healthy'
                ? "bg-green-600 hover:bg-green-700"
                : "bg-red-600 hover:bg-red-700"
              }>
                {systemHealth?.status === 'healthy' ? 'All Systems Operational' : 'System Issues Detected'}
              </Badge>
            </div>

            <div className="pt-2 border-t border-border">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">CPU Usage</span>
                <span className="text-card-foreground">{systemHealth?.cpu || 0}%</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2 mt-1">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${systemHealth?.cpu || 0}%` }}
                ></div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
