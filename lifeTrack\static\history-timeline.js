// History Timeline JavaScript
let timelineData = [];
let filteredData = [];
let currentZoom = 1;
let currentOffset = 0;
let isDragging = false;

// Initialize the timeline
document.addEventListener('DOMContentLoaded', function() {
    initializeTimeline();
    setupEventListeners();
    loadTimelineData();
});

function initializeTimeline() {
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date-filter').value = today;
}

function setupEventListeners() {
    // Filter controls
    document.getElementById('date-filter').addEventListener('change', filterByDate);
    document.getElementById('sort-select').addEventListener('change', sortData);
    
    // Action buttons
    document.getElementById('refresh-btn').addEventListener('click', refreshTimeline);
    document.getElementById('export-btn').addEventListener('click', exportTimeline);
    
    // Zoom controls
    document.getElementById('zoom-in-btn').addEventListener('click', () => zoomTimeline(1.5));
    document.getElementById('zoom-out-btn').addEventListener('click', () => zoomTimeline(0.75));
    document.getElementById('zoom-reset-btn').addEventListener('click', resetZoom);
    
    // Timeline scrubber
    setupTimelineScrubber();
    
    // Modal controls
    setupModal();
}

function setupTimelineScrubber() {
    const dragHandle = document.getElementById('timeline-drag-handle');
    const scrubber = document.querySelector('.timeline-scrubber');
    
    dragHandle.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);
    
    function startDrag(e) {
        isDragging = true;
        dragHandle.style.cursor = 'grabbing';
    }
    
    function drag(e) {
        if (!isDragging) return;
        
        const rect = scrubber.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        dragHandle.style.left = `${percentage}%`;
        updateTimelineView(percentage);
    }
    
    function endDrag() {
        isDragging = false;
        dragHandle.style.cursor = 'grab';
    }
}

function setupModal() {
    const modal = document.getElementById('image-modal');
    const closeBtn = modal.querySelector('.close');
    
    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

async function loadTimelineData() {
    try {
        showLoading();
        
        const response = await fetch('/api/analysis');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (Array.isArray(data)) {
            timelineData = data;
            filteredData = [...timelineData];
            updateMinimap();
            displayTimeline();
        } else {
            showError('Unexpected data format received');
        }
    } catch (error) {
        console.error('Error loading timeline data:', error);
        showError(`Error loading timeline: ${error.message}`);
    }
}

function showLoading() {
    const timeline = document.getElementById('timeline');
    timeline.innerHTML = `
        <div class="timeline-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading timeline...</p>
        </div>
    `;
}

function showError(message) {
    const timeline = document.getElementById('timeline');
    timeline.innerHTML = `
        <div class="timeline-loading">
            <i class="fas fa-exclamation-triangle"></i>
            <p>${message}</p>
        </div>
    `;
}

function filterByDate() {
    const selectedDate = document.getElementById('date-filter').value;
    if (!selectedDate) {
        filteredData = [...timelineData];
    } else {
        const filterDate = new Date(selectedDate);
        filteredData = timelineData.filter(item => {
            const itemDate = new Date(item.timestamp);
            return itemDate.toDateString() === filterDate.toDateString();
        });
    }
    
    updateMinimap();
    displayTimeline();
}

function sortData() {
    const sortOrder = document.getElementById('sort-select').value;
    
    filteredData.sort((a, b) => {
        const dateA = new Date(a.timestamp);
        const dateB = new Date(b.timestamp);
        
        return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
    });
    
    displayTimeline();
}

function updateMinimap() {
    const minimapPoints = document.getElementById('timeline-minimap-points');
    minimapPoints.innerHTML = '';
    
    if (filteredData.length === 0) return;
    
    // Group data by hour for minimap visualization
    const hourGroups = {};
    filteredData.forEach(item => {
        const hour = new Date(item.timestamp).getHours();
        if (!hourGroups[hour]) hourGroups[hour] = 0;
        hourGroups[hour]++;
    });
    
    // Create points for each hour with data
    Object.keys(hourGroups).forEach(hour => {
        const point = document.createElement('div');
        point.className = 'timeline-point';
        point.style.left = `${(hour / 24) * 100}%`;
        point.style.opacity = Math.min(1, hourGroups[hour] / 10); // Scale opacity by count
        minimapPoints.appendChild(point);
    });
}

function displayTimeline() {
    const timeline = document.getElementById('timeline');
    
    if (filteredData.length === 0) {
        timeline.innerHTML = `
            <div class="timeline-loading">
                <i class="fas fa-calendar-times"></i>
                <p>No captures found for the selected date</p>
            </div>
        `;
        return;
    }
    
    // Group data by time periods (hours)
    const timeGroups = groupByTime(filteredData);
    
    let html = '';
    Object.keys(timeGroups).forEach(timeKey => {
        const group = timeGroups[timeKey];
        const groupTime = formatTimeGroup(timeKey);
        
        html += `
            <div class="timeline-group">
                <div class="timeline-group-header">
                    <div class="timeline-group-time">${groupTime}</div>
                    <div class="timeline-group-count">${group.length} captures</div>
                    <button class="timeline-group-toggle" onclick="toggleGroup(this)">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="timeline-images">
                    ${group.map(item => createTimelineImageCard(item)).join('')}
                </div>
            </div>
        `;
    });
    
    timeline.innerHTML = html;
}

function groupByTime(data) {
    const groups = {};
    
    data.forEach(item => {
        const date = new Date(item.timestamp);
        const hourKey = `${date.getHours()}:00`;
        
        if (!groups[hourKey]) {
            groups[hourKey] = [];
        }
        groups[hourKey].push(item);
    });
    
    return groups;
}

function formatTimeGroup(timeKey) {
    const hour = parseInt(timeKey.split(':')[0]);
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
}

function createTimelineImageCard(item) {
    const timestamp = new Date(item.timestamp);
    const timeString = timestamp.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
    });
    
    const description = item.description || 'No description available';
    const imagePath = item.image_path ? `/images/${item.image_path.split('/').pop()}` : '';
    
    return `
        <div class="timeline-image-card" onclick="openImageModal('${imagePath}', '${timestamp.toISOString()}', '${description.replace(/'/g, "\\'")}')">
            <img src="${imagePath}" alt="Capture" class="timeline-image" onerror="this.src='/static/placeholder.jpg'">
            <div class="timeline-image-info">
                <div class="timeline-image-time">${timeString}</div>
                <div class="timeline-image-description">${description}</div>
            </div>
        </div>
    `;
}

function toggleGroup(button) {
    const group = button.closest('.timeline-group');
    const images = group.querySelector('.timeline-images');
    const icon = button.querySelector('i');
    
    if (images.style.display === 'none') {
        images.style.display = 'grid';
        icon.className = 'fas fa-chevron-down';
    } else {
        images.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
    }
}

function openImageModal(imagePath, timestamp, description) {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const modalTimestamp = document.getElementById('modal-timestamp');
    const modalDescription = document.getElementById('modal-description');
    
    modalImage.src = imagePath;
    modalTimestamp.textContent = new Date(timestamp).toLocaleString();
    modalDescription.textContent = description;
    
    modal.style.display = 'block';
}

function closeModal() {
    document.getElementById('image-modal').style.display = 'none';
}

function zoomTimeline(factor) {
    currentZoom *= factor;
    currentZoom = Math.max(0.5, Math.min(3, currentZoom)); // Limit zoom range
    
    // Apply zoom effect (could be implemented with CSS transforms)
    const timeline = document.getElementById('timeline');
    timeline.style.transform = `scale(${currentZoom})`;
    timeline.style.transformOrigin = 'top left';
}

function resetZoom() {
    currentZoom = 1;
    const timeline = document.getElementById('timeline');
    timeline.style.transform = 'scale(1)';
}

function updateTimelineView(percentage) {
    // Update the visible portion of the timeline based on scrubber position
    currentOffset = percentage;
    // This could be implemented to scroll the timeline view
}

function refreshTimeline() {
    loadTimelineData();
}

function exportTimeline() {
    // Create CSV export of timeline data
    const csvContent = "data:text/csv;charset=utf-8," 
        + "Timestamp,Description,Image Path\n"
        + filteredData.map(item => 
            `"${item.timestamp}","${item.description || ''}","${item.image_path || ''}"`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `timeline_export_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
    if (e.key === 'r' && e.ctrlKey) {
        e.preventDefault();
        refreshTimeline();
    }
});
