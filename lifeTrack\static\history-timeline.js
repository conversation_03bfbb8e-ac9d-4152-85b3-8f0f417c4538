// History Timeline JavaScript
let timelineData = [];
let filteredData = [];
let currentZoom = 1;
let currentOffset = 0;
let isDragging = false;
let currentView = 'horizontal'; // 'horizontal' or 'vertical'
let isFullView = false;

// Initialize the timeline
document.addEventListener('DOMContentLoaded', function() {
    initializeTimeline();
    setupEventListeners();
    loadTimelineData();
});

function initializeTimeline() {
    // Don't set a default date - show all data initially
    // const today = new Date().toISOString().split('T')[0];
    // document.getElementById('date-filter').value = today;
}

function setupEventListeners() {
    // Filter controls
    document.getElementById('date-filter').addEventListener('change', filterByDate);
    document.getElementById('sort-select').addEventListener('change', sortData);

    // Action buttons
    document.getElementById('refresh-btn').addEventListener('click', refreshTimeline);
    document.getElementById('export-btn').addEventListener('click', exportTimeline);

    // View toggle buttons
    document.getElementById('horizontal-view-btn').addEventListener('click', () => switchView('horizontal'));
    document.getElementById('vertical-view-btn').addEventListener('click', () => switchView('vertical'));

    // Zoom controls
    document.getElementById('zoom-in-btn').addEventListener('click', () => zoomTimeline(1.5));
    document.getElementById('zoom-out-btn').addEventListener('click', () => zoomTimeline(0.75));
    document.getElementById('zoom-reset-btn').addEventListener('click', resetZoom);

    // Timeline scrubber
    setupTimelineScrubber();

    // Modal controls
    setupModal();
}

function setupTimelineScrubber() {
    const dragHandle = document.getElementById('timeline-drag-handle');
    const scrubber = document.querySelector('.timeline-scrubber');

    dragHandle.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    function startDrag(e) {
        isDragging = true;
        dragHandle.style.cursor = 'grabbing';
    }

    function drag(e) {
        if (!isDragging) return;

        const rect = scrubber.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

        dragHandle.style.left = `${percentage}%`;
        updateTimelineView(percentage);
    }

    function endDrag() {
        isDragging = false;
        dragHandle.style.cursor = 'grab';
    }
}

function setupModal() {
    const modal = document.getElementById('image-modal');
    const closeBtn = modal.querySelector('.close');

    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

async function loadTimelineData() {
    try {
        showLoading();

        const response = await fetch('/api/analysis');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Raw API data:', data); // Debug log

        if (Array.isArray(data)) {
            // Process and validate the data
            timelineData = data.map(item => {
                // Handle different possible timestamp formats
                let timestamp = item.interval_start || item.timestamp || item.time || item.created_at;

                // If timestamp is a string, try to parse it
                if (typeof timestamp === 'string') {
                    // Handle various timestamp formats
                    if (timestamp.includes('T')) {
                        timestamp = new Date(timestamp);
                    } else {
                        // Try parsing as ISO string or other formats
                        timestamp = new Date(timestamp);
                    }
                } else if (typeof timestamp === 'number') {
                    // Handle Unix timestamp (seconds or milliseconds)
                    timestamp = timestamp > 1000000000000 ? new Date(timestamp) : new Date(timestamp * 1000);
                } else {
                    // Default to current time if no valid timestamp
                    timestamp = new Date();
                }

                // Extract image filename from images array if available
                let imagePath = '';
                if (item.images && item.images.length > 0) {
                    imagePath = item.images[0]; // Use first image
                } else if (item.image_path) {
                    imagePath = item.image_path;
                } else if (item.filename) {
                    imagePath = item.filename;
                }

                return {
                    ...item,
                    timestamp: timestamp.toISOString(),
                    description: item.summary || item.description || 'Activity detected',
                    image_path: imagePath
                };
            }).filter(item => {
                // Filter out items with invalid timestamps and ensure we have images
                const date = new Date(item.timestamp);
                return !isNaN(date.getTime()) && item.image_path;
            });

            console.log('Processed timeline data:', timelineData); // Debug log

            filteredData = [...timelineData];
            updateMinimap();
            displayCurrentView();
        } else {
            showError('Unexpected data format received');
        }
    } catch (error) {
        console.error('Error loading timeline data:', error);
        showError(`Error loading timeline: ${error.message}`);
    }
}

function showLoading() {
    const timeline = document.getElementById('timeline');
    timeline.innerHTML = `
        <div class="timeline-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading timeline...</p>
        </div>
    `;
}

function showError(message) {
    const timeline = document.getElementById('timeline');
    timeline.innerHTML = `
        <div class="timeline-loading">
            <i class="fas fa-exclamation-triangle"></i>
            <p>${message}</p>
        </div>
    `;
}

function filterByDate() {
    const selectedDate = document.getElementById('date-filter').value;
    if (!selectedDate) {
        filteredData = [...timelineData];
    } else {
        const filterDate = new Date(selectedDate);
        filteredData = timelineData.filter(item => {
            const itemDate = new Date(item.timestamp);
            return itemDate.toDateString() === filterDate.toDateString();
        });
    }

    updateMinimap();
    displayCurrentView();
}

function sortData() {
    const sortOrder = document.getElementById('sort-select').value;

    filteredData.sort((a, b) => {
        const dateA = new Date(a.timestamp);
        const dateB = new Date(b.timestamp);

        return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
    });

    displayCurrentView();
}

function updateMinimap() {
    const minimapPoints = document.getElementById('timeline-minimap-points');
    minimapPoints.innerHTML = '';

    if (filteredData.length === 0) return;

    // Get the date range of the data
    const timestamps = filteredData.map(item => new Date(item.timestamp));
    const minTime = Math.min(...timestamps);
    const maxTime = Math.max(...timestamps);
    const timeRange = maxTime - minTime;

    if (timeRange === 0) {
        // All data is from the same time, show a single point
        const point = document.createElement('div');
        point.className = 'timeline-point';
        point.style.left = '50%';
        point.style.opacity = '1';
        point.title = `${filteredData.length} capture${filteredData.length > 1 ? 's' : ''}`;
        minimapPoints.appendChild(point);
        return;
    }

    // Group data by time intervals for better visualization
    const intervalCount = 20; // Number of intervals across the timeline
    const intervalSize = timeRange / intervalCount;
    const intervals = {};

    filteredData.forEach(item => {
        // Convert to IST for proper grouping
        const timestamp = new Date(item.timestamp);
        const istTimestamp = new Date(timestamp.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
        const intervalIndex = Math.floor((istTimestamp.getTime() - minTime) / intervalSize);
        if (!intervals[intervalIndex]) intervals[intervalIndex] = 0;
        intervals[intervalIndex]++;
    });

    // Find max count for scaling
    const maxCount = Math.max(...Object.values(intervals));

    // Create points for each interval with data
    Object.keys(intervals).forEach(intervalIndex => {
        const count = intervals[intervalIndex];
        const point = document.createElement('div');
        point.className = 'timeline-point';
        point.style.left = `${(intervalIndex / intervalCount) * 100}%`;
        point.style.opacity = Math.max(0.3, count / maxCount); // Scale opacity by count
        point.style.height = `${Math.max(20, (count / maxCount) * 100)}%`; // Scale height too
        point.title = `${count} capture${count > 1 ? 's' : ''}`;

        // Make points clickable to jump to that section
        point.addEventListener('click', () => {
            const percentage = (intervalIndex / intervalCount) * 100;
            updateTimelineView(percentage);

            // Update drag handle position
            const dragHandle = document.getElementById('timeline-drag-handle');
            if (dragHandle) {
                dragHandle.style.left = `${percentage}%`;
            }
        });

        minimapPoints.appendChild(point);
    });
}

function displayTimeline() {
    const timeline = document.getElementById('timeline');

    if (filteredData.length === 0) {
        timeline.innerHTML = `
            <div class="timeline-loading">
                <i class="fas fa-calendar-times"></i>
                <p>No captures found for the selected date</p>
            </div>
        `;
        return;
    }

    // Sort data by timestamp first
    const sortedData = [...filteredData].sort((a, b) => {
        return new Date(b.timestamp) - new Date(a.timestamp);
    });

    // Group data by time periods (every 30 minutes for better granularity)
    const timeGroups = groupByTimeDetailed(sortedData);

    let html = '';
    let groupIndex = 0;
    Object.keys(timeGroups).sort().reverse().forEach(timeKey => {
        const group = timeGroups[timeKey];
        const groupTime = formatTimeGroupDetailed(timeKey);
        const firstTimestamp = new Date(group[0].timestamp);
        const groupId = `group-${groupIndex++}`;

        html += `
            <div class="timeline-group" id="${groupId}">
                <div class="timeline-group-header">
                    <div class="timeline-group-time">${groupTime}</div>
                    <div class="timeline-group-count">${group.length} capture${group.length > 1 ? 's' : ''}</div>
                    <div class="timeline-group-date">${firstTimestamp.toLocaleDateString('en-IN', {
                        timeZone: 'Asia/Kolkata',
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                    })}</div>
                    <button class="timeline-group-toggle" onclick="toggleGroup('${groupId}')">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="timeline-images" id="${groupId}-images">
                    ${group.map(item => createTimelineImageCard(item)).join('')}
                </div>
            </div>
        `;
    });

    timeline.innerHTML = html;
}

function groupByTime(data) {
    const groups = {};

    data.forEach(item => {
        const date = new Date(item.timestamp);
        const hourKey = `${date.getHours()}:00`;

        if (!groups[hourKey]) {
            groups[hourKey] = [];
        }
        groups[hourKey].push(item);
    });

    return groups;
}

function groupByTimeDetailed(data) {
    const groups = {};

    data.forEach(item => {
        const date = new Date(item.timestamp);
        // Convert to IST for proper grouping
        const istDate = new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
        const hour = istDate.getHours();
        const minute = istDate.getMinutes();

        // Group by 30-minute intervals
        const intervalMinute = minute < 30 ? 0 : 30;
        const timeKey = `${hour.toString().padStart(2, '0')}:${intervalMinute.toString().padStart(2, '0')}`;

        if (!groups[timeKey]) {
            groups[timeKey] = [];
        }
        groups[timeKey].push(item);
    });

    return groups;
}

function formatTimeGroup(timeKey) {
    const hour = parseInt(timeKey.split(':')[0]);
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
}

function formatTimeGroupDetailed(timeKey) {
    const [hour, minute] = timeKey.split(':').map(Number);

    // Create a date object to use Indian time formatting
    const startTime = new Date();
    startTime.setHours(hour, minute, 0, 0);

    const endTime = new Date();
    const endMinute = minute + 30;
    const endHour = endMinute >= 60 ? hour + 1 : hour;
    endTime.setHours(endHour, endMinute >= 60 ? 0 : endMinute, 0, 0);

    const startFormatted = startTime.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    const endFormatted = endTime.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    return `${startFormatted} - ${endFormatted}`;
}

function createTimelineImageCard(item) {
    const timestamp = new Date(item.timestamp);
    const timeString = timestamp.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });

    const description = item.description || item.summary || 'No description available';
    const imagePath = item.image_path ? `/api/images/${item.image_path.split('/').pop()}` : '';

    return `
        <div class="timeline-image-card" onclick="openImageModal('${imagePath}', '${timestamp.toISOString()}', '${description.replace(/'/g, "\\'")}')">
            <div class="image-container">
                <img src="${imagePath}" alt="Capture" class="timeline-image"
                     onerror="this.src='/static/placeholder.svg'; this.classList.add('error')"
                     onload="this.classList.add('loaded')">
                <div class="image-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
            </div>
            <div class="timeline-image-info">
                <div class="timeline-image-time">${timeString}</div>
                <div class="timeline-image-description">${description}</div>
            </div>
        </div>
    `;
}

function toggleGroup(groupId) {
    const group = document.getElementById(groupId);
    const images = document.getElementById(groupId + '-images');
    const button = group.querySelector('.timeline-group-toggle');
    const icon = button.querySelector('i');

    if (images.style.display === 'none') {
        images.style.display = 'grid';
        icon.className = 'fas fa-chevron-down';
        group.classList.remove('collapsed');
    } else {
        images.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
        group.classList.add('collapsed');
    }
}

// Add function to expand/collapse all groups
function toggleAllGroups(expand = true) {
    const groups = document.querySelectorAll('.timeline-group');
    groups.forEach(group => {
        const groupId = group.id;
        const images = document.getElementById(groupId + '-images');
        const button = group.querySelector('.timeline-group-toggle');
        const icon = button.querySelector('i');

        if (expand) {
            images.style.display = 'grid';
            icon.className = 'fas fa-chevron-down';
            group.classList.remove('collapsed');
        } else {
            images.style.display = 'none';
            icon.className = 'fas fa-chevron-right';
            group.classList.add('collapsed');
        }
    });
}

function openImageModal(imagePath, timestamp, description) {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const modalTimestamp = document.getElementById('modal-timestamp');
    const modalDescription = document.getElementById('modal-description');

    modalImage.src = imagePath;
    modalTimestamp.textContent = new Date(timestamp).toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
    modalDescription.textContent = description;

    modal.style.display = 'block';
}

function closeModal() {
    document.getElementById('image-modal').style.display = 'none';
}

function zoomTimeline(factor) {
    currentZoom *= factor;
    currentZoom = Math.max(0.5, Math.min(3, currentZoom)); // Limit zoom range

    // Apply zoom effect (could be implemented with CSS transforms)
    const timeline = document.getElementById('timeline');
    timeline.style.transform = `scale(${currentZoom})`;
    timeline.style.transformOrigin = 'top left';
}

function resetZoom() {
    currentZoom = 1;
    const timeline = document.getElementById('timeline');
    timeline.style.transform = 'scale(1)';
}

function updateTimelineView(percentage) {
    // Update the visible portion of the timeline based on scrubber position
    currentOffset = percentage;

    // Scroll the timeline based on the scrubber position
    const timelineContainer = document.getElementById('timeline-container');
    const horizontalContainer = document.querySelector('.horizontal-timeline-container');

    if (currentView === 'vertical' && timelineContainer) {
        const maxScroll = timelineContainer.scrollHeight - timelineContainer.clientHeight;
        const scrollPosition = (percentage / 100) * maxScroll;
        timelineContainer.scrollTo({
            top: scrollPosition,
            behavior: 'smooth'
        });
    } else if (currentView === 'horizontal' && horizontalContainer) {
        const maxScroll = horizontalContainer.scrollWidth - horizontalContainer.clientWidth;
        const scrollPosition = (percentage / 100) * maxScroll;
        horizontalContainer.scrollTo({
            left: scrollPosition,
            behavior: 'smooth'
        });
    }
}

function switchView(viewType) {
    currentView = viewType;

    // Update button states
    document.getElementById('horizontal-view-btn').classList.toggle('active', viewType === 'horizontal');
    document.getElementById('vertical-view-btn').classList.toggle('active', viewType === 'vertical');

    // Show/hide appropriate views
    document.getElementById('horizontal-timeline-view').style.display = viewType === 'horizontal' ? 'block' : 'none';
    document.getElementById('timeline-view').style.display = viewType === 'vertical' ? 'block' : 'none';

    // Display the timeline in the current view
    displayCurrentView();
}

function displayCurrentView() {
    if (currentView === 'horizontal') {
        displayHorizontalTimeline();
        setupHorizontalScrollSync();
    } else {
        displayTimeline();
        setupVerticalScrollSync();
    }
}

function setupVerticalScrollSync() {
    const timelineContainer = document.getElementById('timeline-container');
    const dragHandle = document.getElementById('timeline-drag-handle');

    if (timelineContainer && dragHandle) {
        timelineContainer.addEventListener('scroll', () => {
            const scrollPercentage = (timelineContainer.scrollTop / (timelineContainer.scrollHeight - timelineContainer.clientHeight)) * 100;
            dragHandle.style.left = `${Math.min(90, Math.max(10, scrollPercentage))}%`;
        });
    }
}

function setupHorizontalScrollSync() {
    const horizontalContainer = document.querySelector('.horizontal-timeline-container');
    const dragHandle = document.getElementById('timeline-drag-handle');

    if (horizontalContainer && dragHandle) {
        horizontalContainer.addEventListener('scroll', () => {
            const scrollPercentage = (horizontalContainer.scrollLeft / (horizontalContainer.scrollWidth - horizontalContainer.clientWidth)) * 100;
            dragHandle.style.left = `${Math.min(90, Math.max(10, scrollPercentage))}%`;
        });
    }
}

function displayHorizontalTimeline() {
    const timeline = document.getElementById('horizontal-timeline');

    if (filteredData.length === 0) {
        timeline.innerHTML = `
            <div class="timeline-loading">
                <i class="fas fa-calendar-times"></i>
                <p>No captures found for the selected date</p>
            </div>
        `;
        return;
    }

    // Sort data by timestamp first
    const sortedData = [...filteredData].sort((a, b) => {
        return new Date(b.timestamp) - new Date(a.timestamp);
    });

    // Group data by time periods (every 30 minutes for better granularity)
    const timeGroups = groupByTimeDetailed(sortedData);

    let html = '';
    Object.keys(timeGroups).sort().reverse().forEach(timeKey => {
        const group = timeGroups[timeKey];
        const groupTime = formatTimeGroupDetailed(timeKey);

        html += `
            <div class="horizontal-timeline-group">
                <div class="horizontal-timeline-header">
                    <div class="horizontal-timeline-time">${groupTime}</div>
                    <div class="horizontal-timeline-count">${group.length} capture${group.length > 1 ? 's' : ''}</div>
                </div>
                <div class="horizontal-timeline-images">
                    ${group.map(item => createHorizontalImageCard(item)).join('')}
                </div>
            </div>
        `;
    });

    timeline.innerHTML = html;
}

function createHorizontalImageCard(item) {
    const timestamp = new Date(item.timestamp);
    const timeString = timestamp.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });

    const description = item.description || item.summary || 'No description available';
    const imagePath = item.image_path ? `/api/images/${item.image_path.split('/').pop()}` : '';

    return `
        <div class="horizontal-image-card" onclick="openImageModal('${imagePath}', '${timestamp.toISOString()}', '${description.replace(/'/g, "\\'")}')">
            <div class="image-container">
                <img src="${imagePath}" alt="Capture" class="horizontal-image"
                     onerror="this.src='/static/placeholder.svg'; this.classList.add('error')"
                     onload="this.classList.add('loaded')">
                <div class="image-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
            </div>
            <div class="horizontal-image-info">
                <div class="horizontal-image-time">${timeString}</div>
                <div class="horizontal-image-description">${description}</div>
            </div>
        </div>
    `;
}

function scrollToTop() {
    const timelineContainer = document.getElementById('timeline-container');
    timelineContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

function toggleFullView() {
    isFullView = !isFullView;
    const button = document.getElementById('expand-view-btn');
    const body = document.body;

    if (isFullView) {
        // Expand to full view
        button.innerHTML = '<i class="fas fa-compress"></i> Compact View';
        body.classList.add('full-view');

        // Scroll to top when entering full view
        window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
        // Return to compact view
        button.innerHTML = '<i class="fas fa-expand"></i> Full View';
        body.classList.remove('full-view');
    }
}

function refreshTimeline() {
    loadTimelineData();
}

// Add search functionality for vertical view
function searchInTimeline(searchTerm) {
    const groups = document.querySelectorAll('.timeline-group');
    let foundResults = 0;

    groups.forEach(group => {
        const groupId = group.id;
        const images = group.querySelectorAll('.timeline-image-card');
        let groupHasMatch = false;

        images.forEach(imageCard => {
            const description = imageCard.querySelector('.timeline-image-description').textContent.toLowerCase();
            const time = imageCard.querySelector('.timeline-image-time').textContent.toLowerCase();

            if (description.includes(searchTerm.toLowerCase()) || time.includes(searchTerm.toLowerCase())) {
                imageCard.style.display = 'block';
                imageCard.classList.add('search-highlight');
                groupHasMatch = true;
                foundResults++;
            } else {
                imageCard.style.display = searchTerm ? 'none' : 'block';
                imageCard.classList.remove('search-highlight');
            }
        });

        // Show/hide entire group based on matches
        if (searchTerm) {
            group.style.display = groupHasMatch ? 'block' : 'none';
            if (groupHasMatch) {
                // Auto-expand groups with matches
                const imagesContainer = document.getElementById(groupId + '-images');
                const button = group.querySelector('.timeline-group-toggle');
                const icon = button.querySelector('i');

                imagesContainer.style.display = 'grid';
                icon.className = 'fas fa-chevron-down';
                group.classList.remove('collapsed');
            }
        } else {
            group.style.display = 'block';
        }
    });

    return foundResults;
}

function handleTimelineSearch(searchTerm) {
    const resultsCount = searchInTimeline(searchTerm);

    // Show/hide search results indicator
    let resultsInfo = document.querySelector('.search-results-info');
    if (searchTerm) {
        if (!resultsInfo) {
            resultsInfo = document.createElement('div');
            resultsInfo.className = 'search-results-info';
            document.querySelector('.timeline-control-buttons').appendChild(resultsInfo);
        }
        resultsInfo.textContent = `${resultsCount} result${resultsCount !== 1 ? 's' : ''} found`;
        resultsInfo.style.display = 'block';
    } else {
        if (resultsInfo) {
            resultsInfo.style.display = 'none';
        }
    }
}

function showKeyboardShortcuts() {
    const shortcuts = `
        <div style="text-align: left; line-height: 1.6;">
            <h3 style="margin-top: 0; color: var(--primary-color);">Keyboard Shortcuts</h3>
            <div style="margin-bottom: 10px;"><strong>Ctrl + V</strong> - Switch between Horizontal/Vertical views</div>
            <div style="margin-bottom: 10px;"><strong>Ctrl + F</strong> - Focus search (Vertical view)</div>
            <div style="margin-bottom: 10px;"><strong>Ctrl + R</strong> - Refresh timeline</div>
            <div style="margin-bottom: 10px;"><strong>Escape</strong> - Close modal or clear search</div>
            <div style="margin-bottom: 10px;"><strong>Click</strong> - View image in full size</div>
            <div style="margin-bottom: 10px;"><strong>Hover</strong> - Preview image effects</div>
        </div>
    `;

    // Create a temporary modal for shortcuts
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: var(--card-bg);
        padding: 30px;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        max-width: 500px;
        color: var(--text-primary);
    `;

    content.innerHTML = shortcuts + `
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="this.closest('.shortcut-modal').remove()"
                    style="padding: 10px 20px; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                Got it!
            </button>
        </div>
    `;

    modal.className = 'shortcut-modal';
    modal.appendChild(content);
    document.body.appendChild(modal);

    // Close on click outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function exportTimeline() {
    // Create CSV export of timeline data with Indian formatted timestamps
    const csvContent = "data:text/csv;charset=utf-8,"
        + "Timestamp,Description,Image Path\n"
        + filteredData.map(item => {
            const formattedTimestamp = new Date(item.timestamp).toLocaleString('en-IN', {
                timeZone: 'Asia/Kolkata',
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
            return `"${formattedTimestamp}","${item.description || ''}","${item.image_path || ''}"`;
        }).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);

    // Use Indian date format for filename (in IST)
    const today = new Date().toLocaleDateString('en-IN', {
        timeZone: 'Asia/Kolkata',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    }).replace(/\//g, '-');

    link.setAttribute("download", `timeline_export_${today}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
        // Also clear search if active
        const searchInput = document.getElementById('timeline-search-input');
        if (searchInput && searchInput.value) {
            searchInput.value = '';
            handleTimelineSearch('');
        }
    }
    if (e.key === 'r' && e.ctrlKey) {
        e.preventDefault();
        refreshTimeline();
    }
    if (e.key === 'v' && e.ctrlKey) {
        e.preventDefault();
        // Toggle between views
        switchView(currentView === 'horizontal' ? 'vertical' : 'horizontal');
    }
    if (e.key === 'f' && e.ctrlKey && currentView === 'vertical') {
        e.preventDefault();
        // Focus search input
        const searchInput = document.getElementById('timeline-search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }
});
