
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Image as ImageIcon, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";

interface RecentCapturesPreviewProps {
  onSelectImage: (image: any) => void;
}

export function RecentCapturesPreview({ onSelectImage }: RecentCapturesPreviewProps) {
  const navigate = useNavigate();

  // Fetch recent captures from lifeTrack backend
  const { data: recentImages = [], isLoading, error } = useQuery({
    queryKey: ['recentCaptures'],
    queryFn: api.getRecentCaptures,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Show only the 4 most recent captures as preview
  const previewImages = recentImages.slice(0, 4).map((image: any) => ({
    ...image,
    timestamp: new Date(image.timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }),
    description: image.summary?.substring(0, 50) + '...' || 'Activity detected'
  }));

  return (
    <Card className="bg-card border-border">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <ImageIcon className="h-5 w-5 text-primary" />
          Recent Captures
        </CardTitle>
        <Button
          variant="outline"
          size="sm"
          className="border-border text-muted-foreground hover:border-primary hover:text-primary"
          onClick={() => navigate('/all-captures')}
        >
          View All
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading captures...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            <p>Failed to load captures. Please check if the backend is running.</p>
          </div>
        ) : previewImages.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            <p>No captures available yet.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {previewImages.map((image) => (
              <div
                key={image.id}
                className="cursor-pointer transition-all duration-300 hover:scale-105 group"
                onClick={() => onSelectImage(image)}
              >
                <div className="relative">
                  <img
                    src={image.thumbnail}
                    alt={image.description}
                    className="w-full h-24 object-cover rounded-lg"
                    onError={(e) => {
                      // Fallback to a placeholder if image fails to load
                      (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NyA2NUw5MyA3MUwxMDUgNTlMMTIzIDc3SDE2M1Y5N0g0N1Y3N0w2NSA1OUw4NyA2NVoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                    }}
                  />
                  <div className="absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    {image.timestamp}
                  </div>
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg" />
                </div>
                <p className="text-xs text-muted-foreground mt-1 truncate">{image.description}</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
