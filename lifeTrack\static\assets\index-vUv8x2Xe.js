var Hf=e=>{throw TypeError(e)};var Fl=(e,t,n)=>t.has(e)||Hf("Cannot "+n);var E=(e,t,n)=>(Fl(e,t,"read from private field"),n?n.call(e):t.get(e)),Q=(e,t,n)=>t.has(e)?Hf("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),U=(e,t,n,r)=>(Fl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),te=(e,t,n)=>(Fl(e,t,"access private method"),n);var gi=(e,t,n,r)=>({set _(o){U(e,t,o,n)},get _(){return E(e,t,r)}});function Jw(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function im(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var am={exports:{}},Ka={},lm={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qs=Symbol.for("react.element"),e1=Symbol.for("react.portal"),t1=Symbol.for("react.fragment"),n1=Symbol.for("react.strict_mode"),r1=Symbol.for("react.profiler"),o1=Symbol.for("react.provider"),s1=Symbol.for("react.context"),i1=Symbol.for("react.forward_ref"),a1=Symbol.for("react.suspense"),l1=Symbol.for("react.memo"),u1=Symbol.for("react.lazy"),Wf=Symbol.iterator;function c1(e){return e===null||typeof e!="object"?null:(e=Wf&&e[Wf]||e["@@iterator"],typeof e=="function"?e:null)}var um={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cm=Object.assign,dm={};function Fo(e,t,n){this.props=e,this.context=t,this.refs=dm,this.updater=n||um}Fo.prototype.isReactComponent={};Fo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Fo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fm(){}fm.prototype=Fo.prototype;function nd(e,t,n){this.props=e,this.context=t,this.refs=dm,this.updater=n||um}var rd=nd.prototype=new fm;rd.constructor=nd;cm(rd,Fo.prototype);rd.isPureReactComponent=!0;var Kf=Array.isArray,pm=Object.prototype.hasOwnProperty,od={current:null},hm={key:!0,ref:!0,__self:!0,__source:!0};function mm(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)pm.call(t,r)&&!hm.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:qs,type:e,key:s,ref:i,props:o,_owner:od.current}}function d1(e,t){return{$$typeof:qs,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function sd(e){return typeof e=="object"&&e!==null&&e.$$typeof===qs}function f1(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Qf=/\/+/g;function zl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?f1(""+e.key):t.toString(36)}function Vi(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case qs:case e1:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+zl(i,0):r,Kf(o)?(n="",e!=null&&(n=e.replace(Qf,"$&/")+"/"),Vi(o,t,n,"",function(c){return c})):o!=null&&(sd(o)&&(o=d1(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Qf,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",Kf(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+zl(s,a);i+=Vi(s,t,n,l,o)}else if(l=c1(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+zl(s,a++),i+=Vi(s,t,n,l,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function vi(e,t,n){if(e==null)return e;var r=[],o=0;return Vi(e,r,"","",function(s){return t.call(n,s,o++)}),r}function p1(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Qe={current:null},Hi={transition:null},h1={ReactCurrentDispatcher:Qe,ReactCurrentBatchConfig:Hi,ReactCurrentOwner:od};function gm(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:vi,forEach:function(e,t,n){vi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return vi(e,function(){t++}),t},toArray:function(e){return vi(e,function(t){return t})||[]},only:function(e){if(!sd(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=Fo;J.Fragment=t1;J.Profiler=r1;J.PureComponent=nd;J.StrictMode=n1;J.Suspense=a1;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=h1;J.act=gm;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cm({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=od.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)pm.call(t,l)&&!hm.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:qs,type:e.type,key:o,ref:s,props:r,_owner:i}};J.createContext=function(e){return e={$$typeof:s1,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:o1,_context:e},e.Consumer=e};J.createElement=mm;J.createFactory=function(e){var t=mm.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:i1,render:e}};J.isValidElement=sd;J.lazy=function(e){return{$$typeof:u1,_payload:{_status:-1,_result:e},_init:p1}};J.memo=function(e,t){return{$$typeof:l1,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=Hi.transition;Hi.transition={};try{e()}finally{Hi.transition=t}};J.unstable_act=gm;J.useCallback=function(e,t){return Qe.current.useCallback(e,t)};J.useContext=function(e){return Qe.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Qe.current.useDeferredValue(e)};J.useEffect=function(e,t){return Qe.current.useEffect(e,t)};J.useId=function(){return Qe.current.useId()};J.useImperativeHandle=function(e,t,n){return Qe.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Qe.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Qe.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Qe.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Qe.current.useReducer(e,t,n)};J.useRef=function(e){return Qe.current.useRef(e)};J.useState=function(e){return Qe.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Qe.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Qe.current.useTransition()};J.version="18.3.1";lm.exports=J;var p=lm.exports;const I=im(p),vm=Jw({__proto__:null,default:I},[p]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var m1=p,g1=Symbol.for("react.element"),v1=Symbol.for("react.fragment"),y1=Object.prototype.hasOwnProperty,x1=m1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,w1={key:!0,ref:!0,__self:!0,__source:!0};function ym(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)y1.call(t,r)&&!w1.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:g1,type:e,key:s,ref:i,props:o,_owner:x1.current}}Ka.Fragment=v1;Ka.jsx=ym;Ka.jsxs=ym;am.exports=Ka;var u=am.exports,xm={exports:{}},ct={},wm={exports:{}},bm={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(k,j){var F=k.length;k.push(j);e:for(;0<F;){var D=F-1>>>1,H=k[D];if(0<o(H,j))k[D]=j,k[F]=H,F=D;else break e}}function n(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var j=k[0],F=k.pop();if(F!==j){k[0]=F;e:for(var D=0,H=k.length,Z=H>>>1;D<Z;){var ie=2*(D+1)-1,De=k[ie],ee=ie+1,Pe=k[ee];if(0>o(De,F))ee<H&&0>o(Pe,De)?(k[D]=Pe,k[ee]=F,D=ee):(k[D]=De,k[ie]=F,D=ie);else if(ee<H&&0>o(Pe,F))k[D]=Pe,k[ee]=F,D=ee;else break e}}return j}function o(k,j){var F=k.sortIndex-j.sortIndex;return F!==0?F:k.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var l=[],c=[],h=1,f=null,d=3,y=!1,w=!1,m=!1,b=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,v=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(k){for(var j=n(c);j!==null;){if(j.callback===null)r(c);else if(j.startTime<=k)r(c),j.sortIndex=j.expirationTime,t(l,j);else break;j=n(c)}}function S(k){if(m=!1,x(k),!w)if(n(l)!==null)w=!0,B(C);else{var j=n(c);j!==null&&K(S,j.startTime-k)}}function C(k,j){w=!1,m&&(m=!1,g(R),R=-1),y=!0;var F=d;try{for(x(j),f=n(l);f!==null&&(!(f.expirationTime>j)||k&&!L());){var D=f.callback;if(typeof D=="function"){f.callback=null,d=f.priorityLevel;var H=D(f.expirationTime<=j);j=e.unstable_now(),typeof H=="function"?f.callback=H:f===n(l)&&r(l),x(j)}else r(l);f=n(l)}if(f!==null)var Z=!0;else{var ie=n(c);ie!==null&&K(S,ie.startTime-j),Z=!1}return Z}finally{f=null,d=F,y=!1}}var P=!1,N=null,R=-1,M=5,_=-1;function L(){return!(e.unstable_now()-_<M)}function A(){if(N!==null){var k=e.unstable_now();_=k;var j=!0;try{j=N(!0,k)}finally{j?W():(P=!1,N=null)}}else P=!1}var W;if(typeof v=="function")W=function(){v(A)};else if(typeof MessageChannel<"u"){var O=new MessageChannel,G=O.port2;O.port1.onmessage=A,W=function(){G.postMessage(null)}}else W=function(){b(A,0)};function B(k){N=k,P||(P=!0,W())}function K(k,j){R=b(function(){k(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(k){k.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,B(C))},e.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<k?Math.floor(1e3/k):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(k){switch(d){case 1:case 2:case 3:var j=3;break;default:j=d}var F=d;d=j;try{return k()}finally{d=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(k,j){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var F=d;d=k;try{return j()}finally{d=F}},e.unstable_scheduleCallback=function(k,j,F){var D=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?D+F:D):F=D,k){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=F+H,k={id:h++,callback:j,priorityLevel:k,startTime:F,expirationTime:H,sortIndex:-1},F>D?(k.sortIndex=F,t(c,k),n(l)===null&&k===n(c)&&(m?(g(R),R=-1):m=!0,K(S,F-D))):(k.sortIndex=H,t(l,k),w||y||(w=!0,B(C))),k},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(k){var j=d;return function(){var F=d;d=j;try{return k.apply(this,arguments)}finally{d=F}}}})(bm);wm.exports=bm;var b1=wm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var S1=p,ut=b1;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Sm=new Set,Ss={};function jr(e,t){jo(e,t),jo(e+"Capture",t)}function jo(e,t){for(Ss[e]=t,e=0;e<t.length;e++)Sm.add(t[e])}var un=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ru=Object.prototype.hasOwnProperty,C1=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Gf={},Yf={};function E1(e){return Ru.call(Yf,e)?!0:Ru.call(Gf,e)?!1:C1.test(e)?Yf[e]=!0:(Gf[e]=!0,!1)}function N1(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function k1(e,t,n,r){if(t===null||typeof t>"u"||N1(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ge(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Oe[e]=new Ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Oe[t]=new Ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Oe[e]=new Ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Oe[e]=new Ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Oe[e]=new Ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Oe[e]=new Ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Oe[e]=new Ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Oe[e]=new Ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Oe[e]=new Ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var id=/[\-:]([a-z])/g;function ad(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(id,ad);Oe[t]=new Ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(id,ad);Oe[t]=new Ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(id,ad);Oe[t]=new Ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Oe[e]=new Ge(e,1,!1,e.toLowerCase(),null,!1,!1)});Oe.xlinkHref=new Ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Oe[e]=new Ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function ld(e,t,n,r){var o=Oe.hasOwnProperty(t)?Oe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(k1(t,n,o,r)&&(n=null),r||o===null?E1(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var mn=S1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,yi=Symbol.for("react.element"),Kr=Symbol.for("react.portal"),Qr=Symbol.for("react.fragment"),ud=Symbol.for("react.strict_mode"),ju=Symbol.for("react.profiler"),Cm=Symbol.for("react.provider"),Em=Symbol.for("react.context"),cd=Symbol.for("react.forward_ref"),Tu=Symbol.for("react.suspense"),Mu=Symbol.for("react.suspense_list"),dd=Symbol.for("react.memo"),Cn=Symbol.for("react.lazy"),Nm=Symbol.for("react.offscreen"),Xf=Symbol.iterator;function Go(e){return e===null||typeof e!="object"?null:(e=Xf&&e[Xf]||e["@@iterator"],typeof e=="function"?e:null)}var xe=Object.assign,$l;function os(e){if($l===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);$l=t&&t[1]||""}return`
`+$l+e}var Ul=!1;function Bl(e,t){if(!e||Ul)return"";Ul=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,a=s.length-1;1<=i&&0<=a&&o[i]!==s[a];)a--;for(;1<=i&&0<=a;i--,a--)if(o[i]!==s[a]){if(i!==1||a!==1)do if(i--,a--,0>a||o[i]!==s[a]){var l=`
`+o[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=i&&0<=a);break}}}finally{Ul=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?os(e):""}function P1(e){switch(e.tag){case 5:return os(e.type);case 16:return os("Lazy");case 13:return os("Suspense");case 19:return os("SuspenseList");case 0:case 2:case 15:return e=Bl(e.type,!1),e;case 11:return e=Bl(e.type.render,!1),e;case 1:return e=Bl(e.type,!0),e;default:return""}}function _u(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Qr:return"Fragment";case Kr:return"Portal";case ju:return"Profiler";case ud:return"StrictMode";case Tu:return"Suspense";case Mu:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Em:return(e.displayName||"Context")+".Consumer";case Cm:return(e._context.displayName||"Context")+".Provider";case cd:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case dd:return t=e.displayName||null,t!==null?t:_u(e.type)||"Memo";case Cn:t=e._payload,e=e._init;try{return _u(e(t))}catch{}}return null}function R1(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return _u(t);case 8:return t===ud?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function km(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function j1(e){var t=km(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xi(e){e._valueTracker||(e._valueTracker=j1(e))}function Pm(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=km(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ua(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Iu(e,t){var n=t.checked;return xe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function qf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rm(e,t){t=t.checked,t!=null&&ld(e,"checked",t,!1)}function Au(e,t){Rm(e,t);var n=Qn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ou(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ou(e,t.type,Qn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Zf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ou(e,t,n){(t!=="number"||ua(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ss=Array.isArray;function oo(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Du(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return xe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Jf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(ss(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qn(n)}}function jm(e,t){var n=Qn(t.value),r=Qn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ep(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Tm(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Lu(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Tm(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var wi,Mm=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(wi=wi||document.createElement("div"),wi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=wi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Cs(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var fs={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},T1=["Webkit","ms","Moz","O"];Object.keys(fs).forEach(function(e){T1.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fs[t]=fs[e]})});function _m(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||fs.hasOwnProperty(e)&&fs[e]?(""+t).trim():t+"px"}function Im(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=_m(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var M1=xe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Fu(e,t){if(t){if(M1[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function zu(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var $u=null;function fd(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Uu=null,so=null,io=null;function tp(e){if(e=ei(e)){if(typeof Uu!="function")throw Error(T(280));var t=e.stateNode;t&&(t=qa(t),Uu(e.stateNode,e.type,t))}}function Am(e){so?io?io.push(e):io=[e]:so=e}function Om(){if(so){var e=so,t=io;if(io=so=null,tp(e),t)for(e=0;e<t.length;e++)tp(t[e])}}function Dm(e,t){return e(t)}function Lm(){}var Vl=!1;function Fm(e,t,n){if(Vl)return e(t,n);Vl=!0;try{return Dm(e,t,n)}finally{Vl=!1,(so!==null||io!==null)&&(Lm(),Om())}}function Es(e,t){var n=e.stateNode;if(n===null)return null;var r=qa(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Bu=!1;if(un)try{var Yo={};Object.defineProperty(Yo,"passive",{get:function(){Bu=!0}}),window.addEventListener("test",Yo,Yo),window.removeEventListener("test",Yo,Yo)}catch{Bu=!1}function _1(e,t,n,r,o,s,i,a,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(h){this.onError(h)}}var ps=!1,ca=null,da=!1,Vu=null,I1={onError:function(e){ps=!0,ca=e}};function A1(e,t,n,r,o,s,i,a,l){ps=!1,ca=null,_1.apply(I1,arguments)}function O1(e,t,n,r,o,s,i,a,l){if(A1.apply(this,arguments),ps){if(ps){var c=ca;ps=!1,ca=null}else throw Error(T(198));da||(da=!0,Vu=c)}}function Tr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function zm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function np(e){if(Tr(e)!==e)throw Error(T(188))}function D1(e){var t=e.alternate;if(!t){if(t=Tr(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return np(o),e;if(s===r)return np(o),t;s=s.sibling}throw Error(T(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i){for(a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function $m(e){return e=D1(e),e!==null?Um(e):null}function Um(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Um(e);if(t!==null)return t;e=e.sibling}return null}var Bm=ut.unstable_scheduleCallback,rp=ut.unstable_cancelCallback,L1=ut.unstable_shouldYield,F1=ut.unstable_requestPaint,Se=ut.unstable_now,z1=ut.unstable_getCurrentPriorityLevel,pd=ut.unstable_ImmediatePriority,Vm=ut.unstable_UserBlockingPriority,fa=ut.unstable_NormalPriority,$1=ut.unstable_LowPriority,Hm=ut.unstable_IdlePriority,Qa=null,Yt=null;function U1(e){if(Yt&&typeof Yt.onCommitFiberRoot=="function")try{Yt.onCommitFiberRoot(Qa,e,void 0,(e.current.flags&128)===128)}catch{}}var _t=Math.clz32?Math.clz32:H1,B1=Math.log,V1=Math.LN2;function H1(e){return e>>>=0,e===0?32:31-(B1(e)/V1|0)|0}var bi=64,Si=4194304;function is(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pa(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~o;a!==0?r=is(a):(s&=i,s!==0&&(r=is(s)))}else i=n&~o,i!==0?r=is(i):s!==0&&(r=is(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-_t(t),o=1<<n,r|=e[n],t&=~o;return r}function W1(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function K1(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-_t(s),a=1<<i,l=o[i];l===-1?(!(a&n)||a&r)&&(o[i]=W1(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function Hu(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Wm(){var e=bi;return bi<<=1,!(bi&4194240)&&(bi=64),e}function Hl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Zs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-_t(t),e[t]=n}function Q1(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-_t(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function hd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-_t(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var se=0;function Km(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Qm,md,Gm,Ym,Xm,Wu=!1,Ci=[],Ln=null,Fn=null,zn=null,Ns=new Map,ks=new Map,Nn=[],G1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function op(e,t){switch(e){case"focusin":case"focusout":Ln=null;break;case"dragenter":case"dragleave":Fn=null;break;case"mouseover":case"mouseout":zn=null;break;case"pointerover":case"pointerout":Ns.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ks.delete(t.pointerId)}}function Xo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=ei(t),t!==null&&md(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Y1(e,t,n,r,o){switch(t){case"focusin":return Ln=Xo(Ln,e,t,n,r,o),!0;case"dragenter":return Fn=Xo(Fn,e,t,n,r,o),!0;case"mouseover":return zn=Xo(zn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return Ns.set(s,Xo(Ns.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,ks.set(s,Xo(ks.get(s)||null,e,t,n,r,o)),!0}return!1}function qm(e){var t=lr(e.target);if(t!==null){var n=Tr(t);if(n!==null){if(t=n.tag,t===13){if(t=zm(n),t!==null){e.blockedOn=t,Xm(e.priority,function(){Gm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ku(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);$u=r,n.target.dispatchEvent(r),$u=null}else return t=ei(n),t!==null&&md(t),e.blockedOn=n,!1;t.shift()}return!0}function sp(e,t,n){Wi(e)&&n.delete(t)}function X1(){Wu=!1,Ln!==null&&Wi(Ln)&&(Ln=null),Fn!==null&&Wi(Fn)&&(Fn=null),zn!==null&&Wi(zn)&&(zn=null),Ns.forEach(sp),ks.forEach(sp)}function qo(e,t){e.blockedOn===t&&(e.blockedOn=null,Wu||(Wu=!0,ut.unstable_scheduleCallback(ut.unstable_NormalPriority,X1)))}function Ps(e){function t(o){return qo(o,e)}if(0<Ci.length){qo(Ci[0],e);for(var n=1;n<Ci.length;n++){var r=Ci[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ln!==null&&qo(Ln,e),Fn!==null&&qo(Fn,e),zn!==null&&qo(zn,e),Ns.forEach(t),ks.forEach(t),n=0;n<Nn.length;n++)r=Nn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Nn.length&&(n=Nn[0],n.blockedOn===null);)qm(n),n.blockedOn===null&&Nn.shift()}var ao=mn.ReactCurrentBatchConfig,ha=!0;function q1(e,t,n,r){var o=se,s=ao.transition;ao.transition=null;try{se=1,gd(e,t,n,r)}finally{se=o,ao.transition=s}}function Z1(e,t,n,r){var o=se,s=ao.transition;ao.transition=null;try{se=4,gd(e,t,n,r)}finally{se=o,ao.transition=s}}function gd(e,t,n,r){if(ha){var o=Ku(e,t,n,r);if(o===null)eu(e,t,r,ma,n),op(e,r);else if(Y1(o,e,t,n,r))r.stopPropagation();else if(op(e,r),t&4&&-1<G1.indexOf(e)){for(;o!==null;){var s=ei(o);if(s!==null&&Qm(s),s=Ku(e,t,n,r),s===null&&eu(e,t,r,ma,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else eu(e,t,r,null,n)}}var ma=null;function Ku(e,t,n,r){if(ma=null,e=fd(r),e=lr(e),e!==null)if(t=Tr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=zm(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ma=e,null}function Zm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(z1()){case pd:return 1;case Vm:return 4;case fa:case $1:return 16;case Hm:return 536870912;default:return 16}default:return 16}}var An=null,vd=null,Ki=null;function Jm(){if(Ki)return Ki;var e,t=vd,n=t.length,r,o="value"in An?An.value:An.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Ki=o.slice(e,1<r?1-r:void 0)}function Qi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ei(){return!0}function ip(){return!1}function dt(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Ei:ip,this.isPropagationStopped=ip,this}return xe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ei)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ei)},persist:function(){},isPersistent:Ei}),t}var zo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},yd=dt(zo),Js=xe({},zo,{view:0,detail:0}),J1=dt(Js),Wl,Kl,Zo,Ga=xe({},Js,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Zo&&(Zo&&e.type==="mousemove"?(Wl=e.screenX-Zo.screenX,Kl=e.screenY-Zo.screenY):Kl=Wl=0,Zo=e),Wl)},movementY:function(e){return"movementY"in e?e.movementY:Kl}}),ap=dt(Ga),eb=xe({},Ga,{dataTransfer:0}),tb=dt(eb),nb=xe({},Js,{relatedTarget:0}),Ql=dt(nb),rb=xe({},zo,{animationName:0,elapsedTime:0,pseudoElement:0}),ob=dt(rb),sb=xe({},zo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ib=dt(sb),ab=xe({},zo,{data:0}),lp=dt(ab),lb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ub={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},cb={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function db(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=cb[e])?!!t[e]:!1}function xd(){return db}var fb=xe({},Js,{key:function(e){if(e.key){var t=lb[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Qi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ub[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xd,charCode:function(e){return e.type==="keypress"?Qi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),pb=dt(fb),hb=xe({},Ga,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),up=dt(hb),mb=xe({},Js,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xd}),gb=dt(mb),vb=xe({},zo,{propertyName:0,elapsedTime:0,pseudoElement:0}),yb=dt(vb),xb=xe({},Ga,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),wb=dt(xb),bb=[9,13,27,32],wd=un&&"CompositionEvent"in window,hs=null;un&&"documentMode"in document&&(hs=document.documentMode);var Sb=un&&"TextEvent"in window&&!hs,eg=un&&(!wd||hs&&8<hs&&11>=hs),cp=" ",dp=!1;function tg(e,t){switch(e){case"keyup":return bb.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ng(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gr=!1;function Cb(e,t){switch(e){case"compositionend":return ng(t);case"keypress":return t.which!==32?null:(dp=!0,cp);case"textInput":return e=t.data,e===cp&&dp?null:e;default:return null}}function Eb(e,t){if(Gr)return e==="compositionend"||!wd&&tg(e,t)?(e=Jm(),Ki=vd=An=null,Gr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return eg&&t.locale!=="ko"?null:t.data;default:return null}}var Nb={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function fp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Nb[e.type]:t==="textarea"}function rg(e,t,n,r){Am(r),t=ga(t,"onChange"),0<t.length&&(n=new yd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ms=null,Rs=null;function kb(e){hg(e,0)}function Ya(e){var t=qr(e);if(Pm(t))return e}function Pb(e,t){if(e==="change")return t}var og=!1;if(un){var Gl;if(un){var Yl="oninput"in document;if(!Yl){var pp=document.createElement("div");pp.setAttribute("oninput","return;"),Yl=typeof pp.oninput=="function"}Gl=Yl}else Gl=!1;og=Gl&&(!document.documentMode||9<document.documentMode)}function hp(){ms&&(ms.detachEvent("onpropertychange",sg),Rs=ms=null)}function sg(e){if(e.propertyName==="value"&&Ya(Rs)){var t=[];rg(t,Rs,e,fd(e)),Fm(kb,t)}}function Rb(e,t,n){e==="focusin"?(hp(),ms=t,Rs=n,ms.attachEvent("onpropertychange",sg)):e==="focusout"&&hp()}function jb(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ya(Rs)}function Tb(e,t){if(e==="click")return Ya(t)}function Mb(e,t){if(e==="input"||e==="change")return Ya(t)}function _b(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var At=typeof Object.is=="function"?Object.is:_b;function js(e,t){if(At(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ru.call(t,o)||!At(e[o],t[o]))return!1}return!0}function mp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function gp(e,t){var n=mp(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=mp(n)}}function ig(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ig(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ag(){for(var e=window,t=ua();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ua(e.document)}return t}function bd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ib(e){var t=ag(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ig(n.ownerDocument.documentElement,n)){if(r!==null&&bd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=gp(n,s);var i=gp(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ab=un&&"documentMode"in document&&11>=document.documentMode,Yr=null,Qu=null,gs=null,Gu=!1;function vp(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Gu||Yr==null||Yr!==ua(r)||(r=Yr,"selectionStart"in r&&bd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),gs&&js(gs,r)||(gs=r,r=ga(Qu,"onSelect"),0<r.length&&(t=new yd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Yr)))}function Ni(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xr={animationend:Ni("Animation","AnimationEnd"),animationiteration:Ni("Animation","AnimationIteration"),animationstart:Ni("Animation","AnimationStart"),transitionend:Ni("Transition","TransitionEnd")},Xl={},lg={};un&&(lg=document.createElement("div").style,"AnimationEvent"in window||(delete Xr.animationend.animation,delete Xr.animationiteration.animation,delete Xr.animationstart.animation),"TransitionEvent"in window||delete Xr.transitionend.transition);function Xa(e){if(Xl[e])return Xl[e];if(!Xr[e])return e;var t=Xr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in lg)return Xl[e]=t[n];return e}var ug=Xa("animationend"),cg=Xa("animationiteration"),dg=Xa("animationstart"),fg=Xa("transitionend"),pg=new Map,yp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function er(e,t){pg.set(e,t),jr(t,[e])}for(var ql=0;ql<yp.length;ql++){var Zl=yp[ql],Ob=Zl.toLowerCase(),Db=Zl[0].toUpperCase()+Zl.slice(1);er(Ob,"on"+Db)}er(ug,"onAnimationEnd");er(cg,"onAnimationIteration");er(dg,"onAnimationStart");er("dblclick","onDoubleClick");er("focusin","onFocus");er("focusout","onBlur");er(fg,"onTransitionEnd");jo("onMouseEnter",["mouseout","mouseover"]);jo("onMouseLeave",["mouseout","mouseover"]);jo("onPointerEnter",["pointerout","pointerover"]);jo("onPointerLeave",["pointerout","pointerover"]);jr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));jr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));jr("onBeforeInput",["compositionend","keypress","textInput","paste"]);jr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var as="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lb=new Set("cancel close invalid load scroll toggle".split(" ").concat(as));function xp(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,O1(r,t,void 0,e),e.currentTarget=null}function hg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==s&&o.isPropagationStopped())break e;xp(o,a,c),s=l}else for(i=0;i<r.length;i++){if(a=r[i],l=a.instance,c=a.currentTarget,a=a.listener,l!==s&&o.isPropagationStopped())break e;xp(o,a,c),s=l}}}if(da)throw e=Vu,da=!1,Vu=null,e}function pe(e,t){var n=t[Ju];n===void 0&&(n=t[Ju]=new Set);var r=e+"__bubble";n.has(r)||(mg(t,e,2,!1),n.add(r))}function Jl(e,t,n){var r=0;t&&(r|=4),mg(n,e,r,t)}var ki="_reactListening"+Math.random().toString(36).slice(2);function Ts(e){if(!e[ki]){e[ki]=!0,Sm.forEach(function(n){n!=="selectionchange"&&(Lb.has(n)||Jl(n,!1,e),Jl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ki]||(t[ki]=!0,Jl("selectionchange",!1,t))}}function mg(e,t,n,r){switch(Zm(t)){case 1:var o=q1;break;case 4:o=Z1;break;default:o=gd}n=o.bind(null,t,n,e),o=void 0,!Bu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function eu(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var l=i.tag;if((l===3||l===4)&&(l=i.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;i=i.return}for(;a!==null;){if(i=lr(a),i===null)return;if(l=i.tag,l===5||l===6){r=s=i;continue e}a=a.parentNode}}r=r.return}Fm(function(){var c=s,h=fd(n),f=[];e:{var d=pg.get(e);if(d!==void 0){var y=yd,w=e;switch(e){case"keypress":if(Qi(n)===0)break e;case"keydown":case"keyup":y=pb;break;case"focusin":w="focus",y=Ql;break;case"focusout":w="blur",y=Ql;break;case"beforeblur":case"afterblur":y=Ql;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=ap;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=tb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=gb;break;case ug:case cg:case dg:y=ob;break;case fg:y=yb;break;case"scroll":y=J1;break;case"wheel":y=wb;break;case"copy":case"cut":case"paste":y=ib;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=up}var m=(t&4)!==0,b=!m&&e==="scroll",g=m?d!==null?d+"Capture":null:d;m=[];for(var v=c,x;v!==null;){x=v;var S=x.stateNode;if(x.tag===5&&S!==null&&(x=S,g!==null&&(S=Es(v,g),S!=null&&m.push(Ms(v,S,x)))),b)break;v=v.return}0<m.length&&(d=new y(d,w,null,n,h),f.push({event:d,listeners:m}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",d&&n!==$u&&(w=n.relatedTarget||n.fromElement)&&(lr(w)||w[cn]))break e;if((y||d)&&(d=h.window===h?h:(d=h.ownerDocument)?d.defaultView||d.parentWindow:window,y?(w=n.relatedTarget||n.toElement,y=c,w=w?lr(w):null,w!==null&&(b=Tr(w),w!==b||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=c),y!==w)){if(m=ap,S="onMouseLeave",g="onMouseEnter",v="mouse",(e==="pointerout"||e==="pointerover")&&(m=up,S="onPointerLeave",g="onPointerEnter",v="pointer"),b=y==null?d:qr(y),x=w==null?d:qr(w),d=new m(S,v+"leave",y,n,h),d.target=b,d.relatedTarget=x,S=null,lr(h)===c&&(m=new m(g,v+"enter",w,n,h),m.target=x,m.relatedTarget=b,S=m),b=S,y&&w)t:{for(m=y,g=w,v=0,x=m;x;x=Ur(x))v++;for(x=0,S=g;S;S=Ur(S))x++;for(;0<v-x;)m=Ur(m),v--;for(;0<x-v;)g=Ur(g),x--;for(;v--;){if(m===g||g!==null&&m===g.alternate)break t;m=Ur(m),g=Ur(g)}m=null}else m=null;y!==null&&wp(f,d,y,m,!1),w!==null&&b!==null&&wp(f,b,w,m,!0)}}e:{if(d=c?qr(c):window,y=d.nodeName&&d.nodeName.toLowerCase(),y==="select"||y==="input"&&d.type==="file")var C=Pb;else if(fp(d))if(og)C=Mb;else{C=jb;var P=Rb}else(y=d.nodeName)&&y.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=Tb);if(C&&(C=C(e,c))){rg(f,C,n,h);break e}P&&P(e,d,c),e==="focusout"&&(P=d._wrapperState)&&P.controlled&&d.type==="number"&&Ou(d,"number",d.value)}switch(P=c?qr(c):window,e){case"focusin":(fp(P)||P.contentEditable==="true")&&(Yr=P,Qu=c,gs=null);break;case"focusout":gs=Qu=Yr=null;break;case"mousedown":Gu=!0;break;case"contextmenu":case"mouseup":case"dragend":Gu=!1,vp(f,n,h);break;case"selectionchange":if(Ab)break;case"keydown":case"keyup":vp(f,n,h)}var N;if(wd)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else Gr?tg(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(eg&&n.locale!=="ko"&&(Gr||R!=="onCompositionStart"?R==="onCompositionEnd"&&Gr&&(N=Jm()):(An=h,vd="value"in An?An.value:An.textContent,Gr=!0)),P=ga(c,R),0<P.length&&(R=new lp(R,e,null,n,h),f.push({event:R,listeners:P}),N?R.data=N:(N=ng(n),N!==null&&(R.data=N)))),(N=Sb?Cb(e,n):Eb(e,n))&&(c=ga(c,"onBeforeInput"),0<c.length&&(h=new lp("onBeforeInput","beforeinput",null,n,h),f.push({event:h,listeners:c}),h.data=N))}hg(f,t)})}function Ms(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ga(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=Es(e,n),s!=null&&r.unshift(Ms(e,s,o)),s=Es(e,t),s!=null&&r.push(Ms(e,s,o))),e=e.return}return r}function Ur(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function wp(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&c!==null&&(a=c,o?(l=Es(n,s),l!=null&&i.unshift(Ms(n,l,a))):o||(l=Es(n,s),l!=null&&i.push(Ms(n,l,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Fb=/\r\n?/g,zb=/\u0000|\uFFFD/g;function bp(e){return(typeof e=="string"?e:""+e).replace(Fb,`
`).replace(zb,"")}function Pi(e,t,n){if(t=bp(t),bp(e)!==t&&n)throw Error(T(425))}function va(){}var Yu=null,Xu=null;function qu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zu=typeof setTimeout=="function"?setTimeout:void 0,$b=typeof clearTimeout=="function"?clearTimeout:void 0,Sp=typeof Promise=="function"?Promise:void 0,Ub=typeof queueMicrotask=="function"?queueMicrotask:typeof Sp<"u"?function(e){return Sp.resolve(null).then(e).catch(Bb)}:Zu;function Bb(e){setTimeout(function(){throw e})}function tu(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Ps(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Ps(t)}function $n(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Cp(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var $o=Math.random().toString(36).slice(2),Qt="__reactFiber$"+$o,_s="__reactProps$"+$o,cn="__reactContainer$"+$o,Ju="__reactEvents$"+$o,Vb="__reactListeners$"+$o,Hb="__reactHandles$"+$o;function lr(e){var t=e[Qt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[cn]||n[Qt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Cp(e);e!==null;){if(n=e[Qt])return n;e=Cp(e)}return t}e=n,n=e.parentNode}return null}function ei(e){return e=e[Qt]||e[cn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function qr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function qa(e){return e[_s]||null}var ec=[],Zr=-1;function tr(e){return{current:e}}function me(e){0>Zr||(e.current=ec[Zr],ec[Zr]=null,Zr--)}function ce(e,t){Zr++,ec[Zr]=e.current,e.current=t}var Gn={},Ue=tr(Gn),Je=tr(!1),br=Gn;function To(e,t){var n=e.type.contextTypes;if(!n)return Gn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function et(e){return e=e.childContextTypes,e!=null}function ya(){me(Je),me(Ue)}function Ep(e,t,n){if(Ue.current!==Gn)throw Error(T(168));ce(Ue,t),ce(Je,n)}function gg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(T(108,R1(e)||"Unknown",o));return xe({},n,r)}function xa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gn,br=Ue.current,ce(Ue,e),ce(Je,Je.current),!0}function Np(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=gg(e,t,br),r.__reactInternalMemoizedMergedChildContext=e,me(Je),me(Ue),ce(Ue,e)):me(Je),ce(Je,n)}var rn=null,Za=!1,nu=!1;function vg(e){rn===null?rn=[e]:rn.push(e)}function Wb(e){Za=!0,vg(e)}function nr(){if(!nu&&rn!==null){nu=!0;var e=0,t=se;try{var n=rn;for(se=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}rn=null,Za=!1}catch(o){throw rn!==null&&(rn=rn.slice(e+1)),Bm(pd,nr),o}finally{se=t,nu=!1}}return null}var Jr=[],eo=0,wa=null,ba=0,ht=[],mt=0,Sr=null,on=1,sn="";function ir(e,t){Jr[eo++]=ba,Jr[eo++]=wa,wa=e,ba=t}function yg(e,t,n){ht[mt++]=on,ht[mt++]=sn,ht[mt++]=Sr,Sr=e;var r=on;e=sn;var o=32-_t(r)-1;r&=~(1<<o),n+=1;var s=32-_t(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,on=1<<32-_t(t)+o|n<<o|r,sn=s+e}else on=1<<s|n<<o|r,sn=e}function Sd(e){e.return!==null&&(ir(e,1),yg(e,1,0))}function Cd(e){for(;e===wa;)wa=Jr[--eo],Jr[eo]=null,ba=Jr[--eo],Jr[eo]=null;for(;e===Sr;)Sr=ht[--mt],ht[mt]=null,sn=ht[--mt],ht[mt]=null,on=ht[--mt],ht[mt]=null}var at=null,it=null,ge=!1,Tt=null;function xg(e,t){var n=gt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function kp(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,at=e,it=$n(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,at=e,it=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Sr!==null?{id:on,overflow:sn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=gt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,at=e,it=null,!0):!1;default:return!1}}function tc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nc(e){if(ge){var t=it;if(t){var n=t;if(!kp(e,t)){if(tc(e))throw Error(T(418));t=$n(n.nextSibling);var r=at;t&&kp(e,t)?xg(r,n):(e.flags=e.flags&-4097|2,ge=!1,at=e)}}else{if(tc(e))throw Error(T(418));e.flags=e.flags&-4097|2,ge=!1,at=e}}}function Pp(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;at=e}function Ri(e){if(e!==at)return!1;if(!ge)return Pp(e),ge=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!qu(e.type,e.memoizedProps)),t&&(t=it)){if(tc(e))throw wg(),Error(T(418));for(;t;)xg(e,t),t=$n(t.nextSibling)}if(Pp(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){it=$n(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}it=null}}else it=at?$n(e.stateNode.nextSibling):null;return!0}function wg(){for(var e=it;e;)e=$n(e.nextSibling)}function Mo(){it=at=null,ge=!1}function Ed(e){Tt===null?Tt=[e]:Tt.push(e)}var Kb=mn.ReactCurrentBatchConfig;function Jo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var a=o.refs;i===null?delete a[s]:a[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function ji(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Rp(e){var t=e._init;return t(e._payload)}function bg(e){function t(g,v){if(e){var x=g.deletions;x===null?(g.deletions=[v],g.flags|=16):x.push(v)}}function n(g,v){if(!e)return null;for(;v!==null;)t(g,v),v=v.sibling;return null}function r(g,v){for(g=new Map;v!==null;)v.key!==null?g.set(v.key,v):g.set(v.index,v),v=v.sibling;return g}function o(g,v){return g=Hn(g,v),g.index=0,g.sibling=null,g}function s(g,v,x){return g.index=x,e?(x=g.alternate,x!==null?(x=x.index,x<v?(g.flags|=2,v):x):(g.flags|=2,v)):(g.flags|=1048576,v)}function i(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,v,x,S){return v===null||v.tag!==6?(v=uu(x,g.mode,S),v.return=g,v):(v=o(v,x),v.return=g,v)}function l(g,v,x,S){var C=x.type;return C===Qr?h(g,v,x.props.children,S,x.key):v!==null&&(v.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Cn&&Rp(C)===v.type)?(S=o(v,x.props),S.ref=Jo(g,v,x),S.return=g,S):(S=ea(x.type,x.key,x.props,null,g.mode,S),S.ref=Jo(g,v,x),S.return=g,S)}function c(g,v,x,S){return v===null||v.tag!==4||v.stateNode.containerInfo!==x.containerInfo||v.stateNode.implementation!==x.implementation?(v=cu(x,g.mode,S),v.return=g,v):(v=o(v,x.children||[]),v.return=g,v)}function h(g,v,x,S,C){return v===null||v.tag!==7?(v=xr(x,g.mode,S,C),v.return=g,v):(v=o(v,x),v.return=g,v)}function f(g,v,x){if(typeof v=="string"&&v!==""||typeof v=="number")return v=uu(""+v,g.mode,x),v.return=g,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case yi:return x=ea(v.type,v.key,v.props,null,g.mode,x),x.ref=Jo(g,null,v),x.return=g,x;case Kr:return v=cu(v,g.mode,x),v.return=g,v;case Cn:var S=v._init;return f(g,S(v._payload),x)}if(ss(v)||Go(v))return v=xr(v,g.mode,x,null),v.return=g,v;ji(g,v)}return null}function d(g,v,x,S){var C=v!==null?v.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return C!==null?null:a(g,v,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case yi:return x.key===C?l(g,v,x,S):null;case Kr:return x.key===C?c(g,v,x,S):null;case Cn:return C=x._init,d(g,v,C(x._payload),S)}if(ss(x)||Go(x))return C!==null?null:h(g,v,x,S,null);ji(g,x)}return null}function y(g,v,x,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return g=g.get(x)||null,a(v,g,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case yi:return g=g.get(S.key===null?x:S.key)||null,l(v,g,S,C);case Kr:return g=g.get(S.key===null?x:S.key)||null,c(v,g,S,C);case Cn:var P=S._init;return y(g,v,x,P(S._payload),C)}if(ss(S)||Go(S))return g=g.get(x)||null,h(v,g,S,C,null);ji(v,S)}return null}function w(g,v,x,S){for(var C=null,P=null,N=v,R=v=0,M=null;N!==null&&R<x.length;R++){N.index>R?(M=N,N=null):M=N.sibling;var _=d(g,N,x[R],S);if(_===null){N===null&&(N=M);break}e&&N&&_.alternate===null&&t(g,N),v=s(_,v,R),P===null?C=_:P.sibling=_,P=_,N=M}if(R===x.length)return n(g,N),ge&&ir(g,R),C;if(N===null){for(;R<x.length;R++)N=f(g,x[R],S),N!==null&&(v=s(N,v,R),P===null?C=N:P.sibling=N,P=N);return ge&&ir(g,R),C}for(N=r(g,N);R<x.length;R++)M=y(N,g,R,x[R],S),M!==null&&(e&&M.alternate!==null&&N.delete(M.key===null?R:M.key),v=s(M,v,R),P===null?C=M:P.sibling=M,P=M);return e&&N.forEach(function(L){return t(g,L)}),ge&&ir(g,R),C}function m(g,v,x,S){var C=Go(x);if(typeof C!="function")throw Error(T(150));if(x=C.call(x),x==null)throw Error(T(151));for(var P=C=null,N=v,R=v=0,M=null,_=x.next();N!==null&&!_.done;R++,_=x.next()){N.index>R?(M=N,N=null):M=N.sibling;var L=d(g,N,_.value,S);if(L===null){N===null&&(N=M);break}e&&N&&L.alternate===null&&t(g,N),v=s(L,v,R),P===null?C=L:P.sibling=L,P=L,N=M}if(_.done)return n(g,N),ge&&ir(g,R),C;if(N===null){for(;!_.done;R++,_=x.next())_=f(g,_.value,S),_!==null&&(v=s(_,v,R),P===null?C=_:P.sibling=_,P=_);return ge&&ir(g,R),C}for(N=r(g,N);!_.done;R++,_=x.next())_=y(N,g,R,_.value,S),_!==null&&(e&&_.alternate!==null&&N.delete(_.key===null?R:_.key),v=s(_,v,R),P===null?C=_:P.sibling=_,P=_);return e&&N.forEach(function(A){return t(g,A)}),ge&&ir(g,R),C}function b(g,v,x,S){if(typeof x=="object"&&x!==null&&x.type===Qr&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case yi:e:{for(var C=x.key,P=v;P!==null;){if(P.key===C){if(C=x.type,C===Qr){if(P.tag===7){n(g,P.sibling),v=o(P,x.props.children),v.return=g,g=v;break e}}else if(P.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Cn&&Rp(C)===P.type){n(g,P.sibling),v=o(P,x.props),v.ref=Jo(g,P,x),v.return=g,g=v;break e}n(g,P);break}else t(g,P);P=P.sibling}x.type===Qr?(v=xr(x.props.children,g.mode,S,x.key),v.return=g,g=v):(S=ea(x.type,x.key,x.props,null,g.mode,S),S.ref=Jo(g,v,x),S.return=g,g=S)}return i(g);case Kr:e:{for(P=x.key;v!==null;){if(v.key===P)if(v.tag===4&&v.stateNode.containerInfo===x.containerInfo&&v.stateNode.implementation===x.implementation){n(g,v.sibling),v=o(v,x.children||[]),v.return=g,g=v;break e}else{n(g,v);break}else t(g,v);v=v.sibling}v=cu(x,g.mode,S),v.return=g,g=v}return i(g);case Cn:return P=x._init,b(g,v,P(x._payload),S)}if(ss(x))return w(g,v,x,S);if(Go(x))return m(g,v,x,S);ji(g,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,v!==null&&v.tag===6?(n(g,v.sibling),v=o(v,x),v.return=g,g=v):(n(g,v),v=uu(x,g.mode,S),v.return=g,g=v),i(g)):n(g,v)}return b}var _o=bg(!0),Sg=bg(!1),Sa=tr(null),Ca=null,to=null,Nd=null;function kd(){Nd=to=Ca=null}function Pd(e){var t=Sa.current;me(Sa),e._currentValue=t}function rc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function lo(e,t){Ca=e,Nd=to=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ze=!0),e.firstContext=null)}function bt(e){var t=e._currentValue;if(Nd!==e)if(e={context:e,memoizedValue:t,next:null},to===null){if(Ca===null)throw Error(T(308));to=e,Ca.dependencies={lanes:0,firstContext:e}}else to=to.next=e;return t}var ur=null;function Rd(e){ur===null?ur=[e]:ur.push(e)}function Cg(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Rd(t)):(n.next=o.next,o.next=n),t.interleaved=n,dn(e,r)}function dn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var En=!1;function jd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Eg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function an(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Un(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,re&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,dn(e,n)}return o=r.interleaved,o===null?(t.next=t,Rd(r)):(t.next=o.next,o.next=t),r.interleaved=t,dn(e,n)}function Gi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hd(e,n)}}function jp(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ea(e,t,n,r){var o=e.updateQueue;En=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,c=l.next;l.next=null,i===null?s=c:i.next=c,i=l;var h=e.alternate;h!==null&&(h=h.updateQueue,a=h.lastBaseUpdate,a!==i&&(a===null?h.firstBaseUpdate=c:a.next=c,h.lastBaseUpdate=l))}if(s!==null){var f=o.baseState;i=0,h=c=l=null,a=s;do{var d=a.lane,y=a.eventTime;if((r&d)===d){h!==null&&(h=h.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,m=a;switch(d=t,y=n,m.tag){case 1:if(w=m.payload,typeof w=="function"){f=w.call(y,f,d);break e}f=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=m.payload,d=typeof w=="function"?w.call(y,f,d):w,d==null)break e;f=xe({},f,d);break e;case 2:En=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else y={eventTime:y,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},h===null?(c=h=y,l=f):h=h.next=y,i|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(h===null&&(l=f),o.baseState=l,o.firstBaseUpdate=c,o.lastBaseUpdate=h,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);Er|=i,e.lanes=i,e.memoizedState=f}}function Tp(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(T(191,o));o.call(r)}}}var ti={},Xt=tr(ti),Is=tr(ti),As=tr(ti);function cr(e){if(e===ti)throw Error(T(174));return e}function Td(e,t){switch(ce(As,t),ce(Is,e),ce(Xt,ti),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Lu(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Lu(t,e)}me(Xt),ce(Xt,t)}function Io(){me(Xt),me(Is),me(As)}function Ng(e){cr(As.current);var t=cr(Xt.current),n=Lu(t,e.type);t!==n&&(ce(Is,e),ce(Xt,n))}function Md(e){Is.current===e&&(me(Xt),me(Is))}var ve=tr(0);function Na(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ru=[];function _d(){for(var e=0;e<ru.length;e++)ru[e]._workInProgressVersionPrimary=null;ru.length=0}var Yi=mn.ReactCurrentDispatcher,ou=mn.ReactCurrentBatchConfig,Cr=0,ye=null,Ne=null,je=null,ka=!1,vs=!1,Os=0,Qb=0;function Le(){throw Error(T(321))}function Id(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!At(e[n],t[n]))return!1;return!0}function Ad(e,t,n,r,o,s){if(Cr=s,ye=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yi.current=e===null||e.memoizedState===null?qb:Zb,e=n(r,o),vs){s=0;do{if(vs=!1,Os=0,25<=s)throw Error(T(301));s+=1,je=Ne=null,t.updateQueue=null,Yi.current=Jb,e=n(r,o)}while(vs)}if(Yi.current=Pa,t=Ne!==null&&Ne.next!==null,Cr=0,je=Ne=ye=null,ka=!1,t)throw Error(T(300));return e}function Od(){var e=Os!==0;return Os=0,e}function Bt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return je===null?ye.memoizedState=je=e:je=je.next=e,je}function St(){if(Ne===null){var e=ye.alternate;e=e!==null?e.memoizedState:null}else e=Ne.next;var t=je===null?ye.memoizedState:je.next;if(t!==null)je=t,Ne=e;else{if(e===null)throw Error(T(310));Ne=e,e={memoizedState:Ne.memoizedState,baseState:Ne.baseState,baseQueue:Ne.baseQueue,queue:Ne.queue,next:null},je===null?ye.memoizedState=je=e:je=je.next=e}return je}function Ds(e,t){return typeof t=="function"?t(e):t}function su(e){var t=St(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=Ne,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=i=null,l=null,c=s;do{var h=c.lane;if((Cr&h)===h)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:h,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=f,i=r):l=l.next=f,ye.lanes|=h,Er|=h}c=c.next}while(c!==null&&c!==s);l===null?i=r:l.next=a,At(r,t.memoizedState)||(Ze=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ye.lanes|=s,Er|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function iu(e){var t=St(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);At(s,t.memoizedState)||(Ze=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function kg(){}function Pg(e,t){var n=ye,r=St(),o=t(),s=!At(r.memoizedState,o);if(s&&(r.memoizedState=o,Ze=!0),r=r.queue,Dd(Tg.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||je!==null&&je.memoizedState.tag&1){if(n.flags|=2048,Ls(9,jg.bind(null,n,r,o,t),void 0,null),Te===null)throw Error(T(349));Cr&30||Rg(n,t,o)}return o}function Rg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ye.updateQueue,t===null?(t={lastEffect:null,stores:null},ye.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function jg(e,t,n,r){t.value=n,t.getSnapshot=r,Mg(t)&&_g(e)}function Tg(e,t,n){return n(function(){Mg(t)&&_g(e)})}function Mg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!At(e,n)}catch{return!0}}function _g(e){var t=dn(e,1);t!==null&&It(t,e,1,-1)}function Mp(e){var t=Bt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ds,lastRenderedState:e},t.queue=e,e=e.dispatch=Xb.bind(null,ye,e),[t.memoizedState,e]}function Ls(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ye.updateQueue,t===null?(t={lastEffect:null,stores:null},ye.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ig(){return St().memoizedState}function Xi(e,t,n,r){var o=Bt();ye.flags|=e,o.memoizedState=Ls(1|t,n,void 0,r===void 0?null:r)}function Ja(e,t,n,r){var o=St();r=r===void 0?null:r;var s=void 0;if(Ne!==null){var i=Ne.memoizedState;if(s=i.destroy,r!==null&&Id(r,i.deps)){o.memoizedState=Ls(t,n,s,r);return}}ye.flags|=e,o.memoizedState=Ls(1|t,n,s,r)}function _p(e,t){return Xi(8390656,8,e,t)}function Dd(e,t){return Ja(2048,8,e,t)}function Ag(e,t){return Ja(4,2,e,t)}function Og(e,t){return Ja(4,4,e,t)}function Dg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Lg(e,t,n){return n=n!=null?n.concat([e]):null,Ja(4,4,Dg.bind(null,t,e),n)}function Ld(){}function Fg(e,t){var n=St();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Id(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function zg(e,t){var n=St();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Id(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $g(e,t,n){return Cr&21?(At(n,t)||(n=Wm(),ye.lanes|=n,Er|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ze=!0),e.memoizedState=n)}function Gb(e,t){var n=se;se=n!==0&&4>n?n:4,e(!0);var r=ou.transition;ou.transition={};try{e(!1),t()}finally{se=n,ou.transition=r}}function Ug(){return St().memoizedState}function Yb(e,t,n){var r=Vn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Bg(e))Vg(t,n);else if(n=Cg(e,t,n,r),n!==null){var o=We();It(n,e,r,o),Hg(n,t,r)}}function Xb(e,t,n){var r=Vn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Bg(e))Vg(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,a=s(i,n);if(o.hasEagerState=!0,o.eagerState=a,At(a,i)){var l=t.interleaved;l===null?(o.next=o,Rd(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=Cg(e,t,o,r),n!==null&&(o=We(),It(n,e,r,o),Hg(n,t,r))}}function Bg(e){var t=e.alternate;return e===ye||t!==null&&t===ye}function Vg(e,t){vs=ka=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hd(e,n)}}var Pa={readContext:bt,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useInsertionEffect:Le,useLayoutEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useMutableSource:Le,useSyncExternalStore:Le,useId:Le,unstable_isNewReconciler:!1},qb={readContext:bt,useCallback:function(e,t){return Bt().memoizedState=[e,t===void 0?null:t],e},useContext:bt,useEffect:_p,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Xi(4194308,4,Dg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Xi(4,2,e,t)},useMemo:function(e,t){var n=Bt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Bt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Yb.bind(null,ye,e),[r.memoizedState,e]},useRef:function(e){var t=Bt();return e={current:e},t.memoizedState=e},useState:Mp,useDebugValue:Ld,useDeferredValue:function(e){return Bt().memoizedState=e},useTransition:function(){var e=Mp(!1),t=e[0];return e=Gb.bind(null,e[1]),Bt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ye,o=Bt();if(ge){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),Te===null)throw Error(T(349));Cr&30||Rg(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,_p(Tg.bind(null,r,s,e),[e]),r.flags|=2048,Ls(9,jg.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Bt(),t=Te.identifierPrefix;if(ge){var n=sn,r=on;n=(r&~(1<<32-_t(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Os++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Qb++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zb={readContext:bt,useCallback:Fg,useContext:bt,useEffect:Dd,useImperativeHandle:Lg,useInsertionEffect:Ag,useLayoutEffect:Og,useMemo:zg,useReducer:su,useRef:Ig,useState:function(){return su(Ds)},useDebugValue:Ld,useDeferredValue:function(e){var t=St();return $g(t,Ne.memoizedState,e)},useTransition:function(){var e=su(Ds)[0],t=St().memoizedState;return[e,t]},useMutableSource:kg,useSyncExternalStore:Pg,useId:Ug,unstable_isNewReconciler:!1},Jb={readContext:bt,useCallback:Fg,useContext:bt,useEffect:Dd,useImperativeHandle:Lg,useInsertionEffect:Ag,useLayoutEffect:Og,useMemo:zg,useReducer:iu,useRef:Ig,useState:function(){return iu(Ds)},useDebugValue:Ld,useDeferredValue:function(e){var t=St();return Ne===null?t.memoizedState=e:$g(t,Ne.memoizedState,e)},useTransition:function(){var e=iu(Ds)[0],t=St().memoizedState;return[e,t]},useMutableSource:kg,useSyncExternalStore:Pg,useId:Ug,unstable_isNewReconciler:!1};function Pt(e,t){if(e&&e.defaultProps){t=xe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function oc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:xe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var el={isMounted:function(e){return(e=e._reactInternals)?Tr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=We(),o=Vn(e),s=an(r,o);s.payload=t,n!=null&&(s.callback=n),t=Un(e,s,o),t!==null&&(It(t,e,o,r),Gi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=We(),o=Vn(e),s=an(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Un(e,s,o),t!==null&&(It(t,e,o,r),Gi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=We(),r=Vn(e),o=an(n,r);o.tag=2,t!=null&&(o.callback=t),t=Un(e,o,r),t!==null&&(It(t,e,r,n),Gi(t,e,r))}};function Ip(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!js(n,r)||!js(o,s):!0}function Wg(e,t,n){var r=!1,o=Gn,s=t.contextType;return typeof s=="object"&&s!==null?s=bt(s):(o=et(t)?br:Ue.current,r=t.contextTypes,s=(r=r!=null)?To(e,o):Gn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=el,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function Ap(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&el.enqueueReplaceState(t,t.state,null)}function sc(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},jd(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=bt(s):(s=et(t)?br:Ue.current,o.context=To(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(oc(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&el.enqueueReplaceState(o,o.state,null),Ea(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Ao(e,t){try{var n="",r=t;do n+=P1(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function au(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ic(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var eS=typeof WeakMap=="function"?WeakMap:Map;function Kg(e,t,n){n=an(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ja||(ja=!0,gc=r),ic(e,t)},n}function Qg(e,t,n){n=an(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ic(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){ic(e,t),typeof r!="function"&&(Bn===null?Bn=new Set([this]):Bn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Op(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new eS;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=hS.bind(null,e,t,n),t.then(e,e))}function Dp(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Lp(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=an(-1,1),t.tag=2,Un(n,t,1))),n.lanes|=1),e)}var tS=mn.ReactCurrentOwner,Ze=!1;function He(e,t,n,r){t.child=e===null?Sg(t,null,n,r):_o(t,e.child,n,r)}function Fp(e,t,n,r,o){n=n.render;var s=t.ref;return lo(t,o),r=Ad(e,t,n,r,s,o),n=Od(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,fn(e,t,o)):(ge&&n&&Sd(t),t.flags|=1,He(e,t,r,o),t.child)}function zp(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Wd(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Gg(e,t,s,r,o)):(e=ea(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:js,n(i,r)&&e.ref===t.ref)return fn(e,t,o)}return t.flags|=1,e=Hn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Gg(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(js(s,r)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Ze=!0);else return t.lanes=e.lanes,fn(e,t,o)}return ac(e,t,n,r,o)}function Yg(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ce(ro,ot),ot|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ce(ro,ot),ot|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ce(ro,ot),ot|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ce(ro,ot),ot|=r;return He(e,t,o,n),t.child}function Xg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ac(e,t,n,r,o){var s=et(n)?br:Ue.current;return s=To(t,s),lo(t,o),n=Ad(e,t,n,r,s,o),r=Od(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,fn(e,t,o)):(ge&&r&&Sd(t),t.flags|=1,He(e,t,n,o),t.child)}function $p(e,t,n,r,o){if(et(n)){var s=!0;xa(t)}else s=!1;if(lo(t,o),t.stateNode===null)qi(e,t),Wg(t,n,r),sc(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var l=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=bt(c):(c=et(n)?br:Ue.current,c=To(t,c));var h=n.getDerivedStateFromProps,f=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||l!==c)&&Ap(t,i,r,c),En=!1;var d=t.memoizedState;i.state=d,Ea(t,r,i,o),l=t.memoizedState,a!==r||d!==l||Je.current||En?(typeof h=="function"&&(oc(t,n,h,r),l=t.memoizedState),(a=En||Ip(t,n,a,r,d,l,c))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=c,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Eg(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Pt(t.type,a),i.props=c,f=t.pendingProps,d=i.context,l=n.contextType,typeof l=="object"&&l!==null?l=bt(l):(l=et(n)?br:Ue.current,l=To(t,l));var y=n.getDerivedStateFromProps;(h=typeof y=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||d!==l)&&Ap(t,i,r,l),En=!1,d=t.memoizedState,i.state=d,Ea(t,r,i,o);var w=t.memoizedState;a!==f||d!==w||Je.current||En?(typeof y=="function"&&(oc(t,n,y,r),w=t.memoizedState),(c=En||Ip(t,n,c,r,d,w,l)||!1)?(h||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,w,l),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,w,l)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),i.props=r,i.state=w,i.context=l,r=c):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return lc(e,t,n,r,s,o)}function lc(e,t,n,r,o,s){Xg(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&Np(t,n,!1),fn(e,t,s);r=t.stateNode,tS.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=_o(t,e.child,null,s),t.child=_o(t,null,a,s)):He(e,t,a,s),t.memoizedState=r.state,o&&Np(t,n,!0),t.child}function qg(e){var t=e.stateNode;t.pendingContext?Ep(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ep(e,t.context,!1),Td(e,t.containerInfo)}function Up(e,t,n,r,o){return Mo(),Ed(o),t.flags|=256,He(e,t,n,r),t.child}var uc={dehydrated:null,treeContext:null,retryLane:0};function cc(e){return{baseLanes:e,cachePool:null,transitions:null}}function Zg(e,t,n){var r=t.pendingProps,o=ve.current,s=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ce(ve,o&1),e===null)return nc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=rl(i,r,0,null),e=xr(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=cc(n),t.memoizedState=uc,e):Fd(t,i));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return nS(e,t,i,r,a,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Hn(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=Hn(a,s):(s=xr(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?cc(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=uc,r}return s=e.child,e=s.sibling,r=Hn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Fd(e,t){return t=rl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ti(e,t,n,r){return r!==null&&Ed(r),_o(t,e.child,null,n),e=Fd(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function nS(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=au(Error(T(422))),Ti(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=rl({mode:"visible",children:r.children},o,0,null),s=xr(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&_o(t,e.child,null,i),t.child.memoizedState=cc(i),t.memoizedState=uc,s);if(!(t.mode&1))return Ti(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(T(419)),r=au(s,r,void 0),Ti(e,t,i,r)}if(a=(i&e.childLanes)!==0,Ze||a){if(r=Te,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,dn(e,o),It(r,e,o,-1))}return Hd(),r=au(Error(T(421))),Ti(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=mS.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,it=$n(o.nextSibling),at=t,ge=!0,Tt=null,e!==null&&(ht[mt++]=on,ht[mt++]=sn,ht[mt++]=Sr,on=e.id,sn=e.overflow,Sr=t),t=Fd(t,r.children),t.flags|=4096,t)}function Bp(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rc(e.return,t,n)}function lu(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Jg(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(He(e,t,r.children,n),r=ve.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Bp(e,n,t);else if(e.tag===19)Bp(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ce(ve,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Na(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),lu(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Na(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}lu(t,!0,n,null,s);break;case"together":lu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function qi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function fn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Er|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Hn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Hn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function rS(e,t,n){switch(t.tag){case 3:qg(t),Mo();break;case 5:Ng(t);break;case 1:et(t.type)&&xa(t);break;case 4:Td(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ce(Sa,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ce(ve,ve.current&1),t.flags|=128,null):n&t.child.childLanes?Zg(e,t,n):(ce(ve,ve.current&1),e=fn(e,t,n),e!==null?e.sibling:null);ce(ve,ve.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Jg(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ce(ve,ve.current),r)break;return null;case 22:case 23:return t.lanes=0,Yg(e,t,n)}return fn(e,t,n)}var ev,dc,tv,nv;ev=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};dc=function(){};tv=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,cr(Xt.current);var s=null;switch(n){case"input":o=Iu(e,o),r=Iu(e,r),s=[];break;case"select":o=xe({},o,{value:void 0}),r=xe({},r,{value:void 0}),s=[];break;case"textarea":o=Du(e,o),r=Du(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=va)}Fu(n,r);var i;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var a=o[c];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Ss.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var l=r[c];if(a=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(i in a)!a.hasOwnProperty(i)||l&&l.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in l)l.hasOwnProperty(i)&&a[i]!==l[i]&&(n||(n={}),n[i]=l[i])}else n||(s||(s=[]),s.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Ss.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&pe("scroll",e),s||a===l||(s=[])):(s=s||[]).push(c,l))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};nv=function(e,t,n,r){n!==r&&(t.flags|=4)};function es(e,t){if(!ge)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function oS(e,t,n){var r=t.pendingProps;switch(Cd(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Fe(t),null;case 1:return et(t.type)&&ya(),Fe(t),null;case 3:return r=t.stateNode,Io(),me(Je),me(Ue),_d(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ri(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Tt!==null&&(xc(Tt),Tt=null))),dc(e,t),Fe(t),null;case 5:Md(t);var o=cr(As.current);if(n=t.type,e!==null&&t.stateNode!=null)tv(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return Fe(t),null}if(e=cr(Xt.current),Ri(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Qt]=t,r[_s]=s,e=(t.mode&1)!==0,n){case"dialog":pe("cancel",r),pe("close",r);break;case"iframe":case"object":case"embed":pe("load",r);break;case"video":case"audio":for(o=0;o<as.length;o++)pe(as[o],r);break;case"source":pe("error",r);break;case"img":case"image":case"link":pe("error",r),pe("load",r);break;case"details":pe("toggle",r);break;case"input":qf(r,s),pe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},pe("invalid",r);break;case"textarea":Jf(r,s),pe("invalid",r)}Fu(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Pi(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Pi(r.textContent,a,e),o=["children",""+a]):Ss.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&pe("scroll",r)}switch(n){case"input":xi(r),Zf(r,s,!0);break;case"textarea":xi(r),ep(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=va)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Tm(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Qt]=t,e[_s]=r,ev(e,t,!1,!1),t.stateNode=e;e:{switch(i=zu(n,r),n){case"dialog":pe("cancel",e),pe("close",e),o=r;break;case"iframe":case"object":case"embed":pe("load",e),o=r;break;case"video":case"audio":for(o=0;o<as.length;o++)pe(as[o],e);o=r;break;case"source":pe("error",e),o=r;break;case"img":case"image":case"link":pe("error",e),pe("load",e),o=r;break;case"details":pe("toggle",e),o=r;break;case"input":qf(e,r),o=Iu(e,r),pe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=xe({},r,{value:void 0}),pe("invalid",e);break;case"textarea":Jf(e,r),o=Du(e,r),pe("invalid",e);break;default:o=r}Fu(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?Im(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Mm(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Cs(e,l):typeof l=="number"&&Cs(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ss.hasOwnProperty(s)?l!=null&&s==="onScroll"&&pe("scroll",e):l!=null&&ld(e,s,l,i))}switch(n){case"input":xi(e),Zf(e,r,!1);break;case"textarea":xi(e),ep(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?oo(e,!!r.multiple,s,!1):r.defaultValue!=null&&oo(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=va)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Fe(t),null;case 6:if(e&&t.stateNode!=null)nv(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=cr(As.current),cr(Xt.current),Ri(t)){if(r=t.stateNode,n=t.memoizedProps,r[Qt]=t,(s=r.nodeValue!==n)&&(e=at,e!==null))switch(e.tag){case 3:Pi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Pi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Qt]=t,t.stateNode=r}return Fe(t),null;case 13:if(me(ve),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ge&&it!==null&&t.mode&1&&!(t.flags&128))wg(),Mo(),t.flags|=98560,s=!1;else if(s=Ri(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(T(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(T(317));s[Qt]=t}else Mo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Fe(t),s=!1}else Tt!==null&&(xc(Tt),Tt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ve.current&1?ke===0&&(ke=3):Hd())),t.updateQueue!==null&&(t.flags|=4),Fe(t),null);case 4:return Io(),dc(e,t),e===null&&Ts(t.stateNode.containerInfo),Fe(t),null;case 10:return Pd(t.type._context),Fe(t),null;case 17:return et(t.type)&&ya(),Fe(t),null;case 19:if(me(ve),s=t.memoizedState,s===null)return Fe(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)es(s,!1);else{if(ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Na(e),i!==null){for(t.flags|=128,es(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ce(ve,ve.current&1|2),t.child}e=e.sibling}s.tail!==null&&Se()>Oo&&(t.flags|=128,r=!0,es(s,!1),t.lanes=4194304)}else{if(!r)if(e=Na(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),es(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!ge)return Fe(t),null}else 2*Se()-s.renderingStartTime>Oo&&n!==1073741824&&(t.flags|=128,r=!0,es(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Se(),t.sibling=null,n=ve.current,ce(ve,r?n&1|2:n&1),t):(Fe(t),null);case 22:case 23:return Vd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ot&1073741824&&(Fe(t),t.subtreeFlags&6&&(t.flags|=8192)):Fe(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function sS(e,t){switch(Cd(t),t.tag){case 1:return et(t.type)&&ya(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Io(),me(Je),me(Ue),_d(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Md(t),null;case 13:if(me(ve),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));Mo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return me(ve),null;case 4:return Io(),null;case 10:return Pd(t.type._context),null;case 22:case 23:return Vd(),null;case 24:return null;default:return null}}var Mi=!1,$e=!1,iS=typeof WeakSet=="function"?WeakSet:Set,z=null;function no(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){be(e,t,r)}else n.current=null}function fc(e,t,n){try{n()}catch(r){be(e,t,r)}}var Vp=!1;function aS(e,t){if(Yu=ha,e=ag(),bd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,a=-1,l=-1,c=0,h=0,f=e,d=null;t:for(;;){for(var y;f!==n||o!==0&&f.nodeType!==3||(a=i+o),f!==s||r!==0&&f.nodeType!==3||(l=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(y=f.firstChild)!==null;)d=f,f=y;for(;;){if(f===e)break t;if(d===n&&++c===o&&(a=i),d===s&&++h===r&&(l=i),(y=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xu={focusedElem:e,selectionRange:n},ha=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var m=w.memoizedProps,b=w.memoizedState,g=t.stateNode,v=g.getSnapshotBeforeUpdate(t.elementType===t.type?m:Pt(t.type,m),b);g.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var x=t.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(S){be(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return w=Vp,Vp=!1,w}function ys(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&fc(t,n,s)}o=o.next}while(o!==r)}}function tl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pc(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function rv(e){var t=e.alternate;t!==null&&(e.alternate=null,rv(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Qt],delete t[_s],delete t[Ju],delete t[Vb],delete t[Hb])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ov(e){return e.tag===5||e.tag===3||e.tag===4}function Hp(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ov(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=va));else if(r!==4&&(e=e.child,e!==null))for(hc(e,t,n),e=e.sibling;e!==null;)hc(e,t,n),e=e.sibling}function mc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(mc(e,t,n),e=e.sibling;e!==null;)mc(e,t,n),e=e.sibling}var _e=null,jt=!1;function yn(e,t,n){for(n=n.child;n!==null;)sv(e,t,n),n=n.sibling}function sv(e,t,n){if(Yt&&typeof Yt.onCommitFiberUnmount=="function")try{Yt.onCommitFiberUnmount(Qa,n)}catch{}switch(n.tag){case 5:$e||no(n,t);case 6:var r=_e,o=jt;_e=null,yn(e,t,n),_e=r,jt=o,_e!==null&&(jt?(e=_e,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):_e.removeChild(n.stateNode));break;case 18:_e!==null&&(jt?(e=_e,n=n.stateNode,e.nodeType===8?tu(e.parentNode,n):e.nodeType===1&&tu(e,n),Ps(e)):tu(_e,n.stateNode));break;case 4:r=_e,o=jt,_e=n.stateNode.containerInfo,jt=!0,yn(e,t,n),_e=r,jt=o;break;case 0:case 11:case 14:case 15:if(!$e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&fc(n,t,i),o=o.next}while(o!==r)}yn(e,t,n);break;case 1:if(!$e&&(no(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){be(n,t,a)}yn(e,t,n);break;case 21:yn(e,t,n);break;case 22:n.mode&1?($e=(r=$e)||n.memoizedState!==null,yn(e,t,n),$e=r):yn(e,t,n);break;default:yn(e,t,n)}}function Wp(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new iS),t.forEach(function(r){var o=gS.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function kt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:_e=a.stateNode,jt=!1;break e;case 3:_e=a.stateNode.containerInfo,jt=!0;break e;case 4:_e=a.stateNode.containerInfo,jt=!0;break e}a=a.return}if(_e===null)throw Error(T(160));sv(s,i,o),_e=null,jt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(c){be(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)iv(t,e),t=t.sibling}function iv(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(kt(t,e),Ut(e),r&4){try{ys(3,e,e.return),tl(3,e)}catch(m){be(e,e.return,m)}try{ys(5,e,e.return)}catch(m){be(e,e.return,m)}}break;case 1:kt(t,e),Ut(e),r&512&&n!==null&&no(n,n.return);break;case 5:if(kt(t,e),Ut(e),r&512&&n!==null&&no(n,n.return),e.flags&32){var o=e.stateNode;try{Cs(o,"")}catch(m){be(e,e.return,m)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Rm(o,s),zu(a,i);var c=zu(a,s);for(i=0;i<l.length;i+=2){var h=l[i],f=l[i+1];h==="style"?Im(o,f):h==="dangerouslySetInnerHTML"?Mm(o,f):h==="children"?Cs(o,f):ld(o,h,f,c)}switch(a){case"input":Au(o,s);break;case"textarea":jm(o,s);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?oo(o,!!s.multiple,y,!1):d!==!!s.multiple&&(s.defaultValue!=null?oo(o,!!s.multiple,s.defaultValue,!0):oo(o,!!s.multiple,s.multiple?[]:"",!1))}o[_s]=s}catch(m){be(e,e.return,m)}}break;case 6:if(kt(t,e),Ut(e),r&4){if(e.stateNode===null)throw Error(T(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(m){be(e,e.return,m)}}break;case 3:if(kt(t,e),Ut(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ps(t.containerInfo)}catch(m){be(e,e.return,m)}break;case 4:kt(t,e),Ut(e);break;case 13:kt(t,e),Ut(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Ud=Se())),r&4&&Wp(e);break;case 22:if(h=n!==null&&n.memoizedState!==null,e.mode&1?($e=(c=$e)||h,kt(t,e),$e=c):kt(t,e),Ut(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!h&&e.mode&1)for(z=e,h=e.child;h!==null;){for(f=z=h;z!==null;){switch(d=z,y=d.child,d.tag){case 0:case 11:case 14:case 15:ys(4,d,d.return);break;case 1:no(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(m){be(r,n,m)}}break;case 5:no(d,d.return);break;case 22:if(d.memoizedState!==null){Qp(f);continue}}y!==null?(y.return=d,z=y):Qp(f)}h=h.sibling}e:for(h=null,f=e;;){if(f.tag===5){if(h===null){h=f;try{o=f.stateNode,c?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=f.stateNode,l=f.memoizedProps.style,i=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=_m("display",i))}catch(m){be(e,e.return,m)}}}else if(f.tag===6){if(h===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(m){be(e,e.return,m)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;h===f&&(h=null),f=f.return}h===f&&(h=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:kt(t,e),Ut(e),r&4&&Wp(e);break;case 21:break;default:kt(t,e),Ut(e)}}function Ut(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ov(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Cs(o,""),r.flags&=-33);var s=Hp(e);mc(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,a=Hp(e);hc(e,a,i);break;default:throw Error(T(161))}}catch(l){be(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function lS(e,t,n){z=e,av(e)}function av(e,t,n){for(var r=(e.mode&1)!==0;z!==null;){var o=z,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||Mi;if(!i){var a=o.alternate,l=a!==null&&a.memoizedState!==null||$e;a=Mi;var c=$e;if(Mi=i,($e=l)&&!c)for(z=o;z!==null;)i=z,l=i.child,i.tag===22&&i.memoizedState!==null?Gp(o):l!==null?(l.return=i,z=l):Gp(o);for(;s!==null;)z=s,av(s),s=s.sibling;z=o,Mi=a,$e=c}Kp(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,z=s):Kp(e)}}function Kp(e){for(;z!==null;){var t=z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:$e||tl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!$e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Pt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Tp(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Tp(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var h=c.memoizedState;if(h!==null){var f=h.dehydrated;f!==null&&Ps(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}$e||t.flags&512&&pc(t)}catch(d){be(t,t.return,d)}}if(t===e){z=null;break}if(n=t.sibling,n!==null){n.return=t.return,z=n;break}z=t.return}}function Qp(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,z=n;break}z=t.return}}function Gp(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{tl(4,t)}catch(l){be(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){be(t,o,l)}}var s=t.return;try{pc(t)}catch(l){be(t,s,l)}break;case 5:var i=t.return;try{pc(t)}catch(l){be(t,i,l)}}}catch(l){be(t,t.return,l)}if(t===e){z=null;break}var a=t.sibling;if(a!==null){a.return=t.return,z=a;break}z=t.return}}var uS=Math.ceil,Ra=mn.ReactCurrentDispatcher,zd=mn.ReactCurrentOwner,vt=mn.ReactCurrentBatchConfig,re=0,Te=null,Ce=null,Ae=0,ot=0,ro=tr(0),ke=0,Fs=null,Er=0,nl=0,$d=0,xs=null,qe=null,Ud=0,Oo=1/0,nn=null,ja=!1,gc=null,Bn=null,_i=!1,On=null,Ta=0,ws=0,vc=null,Zi=-1,Ji=0;function We(){return re&6?Se():Zi!==-1?Zi:Zi=Se()}function Vn(e){return e.mode&1?re&2&&Ae!==0?Ae&-Ae:Kb.transition!==null?(Ji===0&&(Ji=Wm()),Ji):(e=se,e!==0||(e=window.event,e=e===void 0?16:Zm(e.type)),e):1}function It(e,t,n,r){if(50<ws)throw ws=0,vc=null,Error(T(185));Zs(e,n,r),(!(re&2)||e!==Te)&&(e===Te&&(!(re&2)&&(nl|=n),ke===4&&kn(e,Ae)),tt(e,r),n===1&&re===0&&!(t.mode&1)&&(Oo=Se()+500,Za&&nr()))}function tt(e,t){var n=e.callbackNode;K1(e,t);var r=pa(e,e===Te?Ae:0);if(r===0)n!==null&&rp(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&rp(n),t===1)e.tag===0?Wb(Yp.bind(null,e)):vg(Yp.bind(null,e)),Ub(function(){!(re&6)&&nr()}),n=null;else{switch(Km(r)){case 1:n=pd;break;case 4:n=Vm;break;case 16:n=fa;break;case 536870912:n=Hm;break;default:n=fa}n=mv(n,lv.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function lv(e,t){if(Zi=-1,Ji=0,re&6)throw Error(T(327));var n=e.callbackNode;if(uo()&&e.callbackNode!==n)return null;var r=pa(e,e===Te?Ae:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ma(e,r);else{t=r;var o=re;re|=2;var s=cv();(Te!==e||Ae!==t)&&(nn=null,Oo=Se()+500,yr(e,t));do try{fS();break}catch(a){uv(e,a)}while(!0);kd(),Ra.current=s,re=o,Ce!==null?t=0:(Te=null,Ae=0,t=ke)}if(t!==0){if(t===2&&(o=Hu(e),o!==0&&(r=o,t=yc(e,o))),t===1)throw n=Fs,yr(e,0),kn(e,r),tt(e,Se()),n;if(t===6)kn(e,r);else{if(o=e.current.alternate,!(r&30)&&!cS(o)&&(t=Ma(e,r),t===2&&(s=Hu(e),s!==0&&(r=s,t=yc(e,s))),t===1))throw n=Fs,yr(e,0),kn(e,r),tt(e,Se()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:ar(e,qe,nn);break;case 3:if(kn(e,r),(r&130023424)===r&&(t=Ud+500-Se(),10<t)){if(pa(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){We(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Zu(ar.bind(null,e,qe,nn),t);break}ar(e,qe,nn);break;case 4:if(kn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-_t(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=Se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*uS(r/1960))-r,10<r){e.timeoutHandle=Zu(ar.bind(null,e,qe,nn),r);break}ar(e,qe,nn);break;case 5:ar(e,qe,nn);break;default:throw Error(T(329))}}}return tt(e,Se()),e.callbackNode===n?lv.bind(null,e):null}function yc(e,t){var n=xs;return e.current.memoizedState.isDehydrated&&(yr(e,t).flags|=256),e=Ma(e,t),e!==2&&(t=qe,qe=n,t!==null&&xc(t)),e}function xc(e){qe===null?qe=e:qe.push.apply(qe,e)}function cS(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!At(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kn(e,t){for(t&=~$d,t&=~nl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-_t(t),r=1<<n;e[n]=-1,t&=~r}}function Yp(e){if(re&6)throw Error(T(327));uo();var t=pa(e,0);if(!(t&1))return tt(e,Se()),null;var n=Ma(e,t);if(e.tag!==0&&n===2){var r=Hu(e);r!==0&&(t=r,n=yc(e,r))}if(n===1)throw n=Fs,yr(e,0),kn(e,t),tt(e,Se()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ar(e,qe,nn),tt(e,Se()),null}function Bd(e,t){var n=re;re|=1;try{return e(t)}finally{re=n,re===0&&(Oo=Se()+500,Za&&nr())}}function Nr(e){On!==null&&On.tag===0&&!(re&6)&&uo();var t=re;re|=1;var n=vt.transition,r=se;try{if(vt.transition=null,se=1,e)return e()}finally{se=r,vt.transition=n,re=t,!(re&6)&&nr()}}function Vd(){ot=ro.current,me(ro)}function yr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,$b(n)),Ce!==null)for(n=Ce.return;n!==null;){var r=n;switch(Cd(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ya();break;case 3:Io(),me(Je),me(Ue),_d();break;case 5:Md(r);break;case 4:Io();break;case 13:me(ve);break;case 19:me(ve);break;case 10:Pd(r.type._context);break;case 22:case 23:Vd()}n=n.return}if(Te=e,Ce=e=Hn(e.current,null),Ae=ot=t,ke=0,Fs=null,$d=nl=Er=0,qe=xs=null,ur!==null){for(t=0;t<ur.length;t++)if(n=ur[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}ur=null}return e}function uv(e,t){do{var n=Ce;try{if(kd(),Yi.current=Pa,ka){for(var r=ye.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ka=!1}if(Cr=0,je=Ne=ye=null,vs=!1,Os=0,zd.current=null,n===null||n.return===null){ke=1,Fs=t,Ce=null;break}e:{var s=e,i=n.return,a=n,l=t;if(t=Ae,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,h=a,f=h.tag;if(!(h.mode&1)&&(f===0||f===11||f===15)){var d=h.alternate;d?(h.updateQueue=d.updateQueue,h.memoizedState=d.memoizedState,h.lanes=d.lanes):(h.updateQueue=null,h.memoizedState=null)}var y=Dp(i);if(y!==null){y.flags&=-257,Lp(y,i,a,s,t),y.mode&1&&Op(s,c,t),t=y,l=c;var w=t.updateQueue;if(w===null){var m=new Set;m.add(l),t.updateQueue=m}else w.add(l);break e}else{if(!(t&1)){Op(s,c,t),Hd();break e}l=Error(T(426))}}else if(ge&&a.mode&1){var b=Dp(i);if(b!==null){!(b.flags&65536)&&(b.flags|=256),Lp(b,i,a,s,t),Ed(Ao(l,a));break e}}s=l=Ao(l,a),ke!==4&&(ke=2),xs===null?xs=[s]:xs.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var g=Kg(s,l,t);jp(s,g);break e;case 1:a=l;var v=s.type,x=s.stateNode;if(!(s.flags&128)&&(typeof v.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(Bn===null||!Bn.has(x)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=Qg(s,a,t);jp(s,S);break e}}s=s.return}while(s!==null)}fv(n)}catch(C){t=C,Ce===n&&n!==null&&(Ce=n=n.return);continue}break}while(!0)}function cv(){var e=Ra.current;return Ra.current=Pa,e===null?Pa:e}function Hd(){(ke===0||ke===3||ke===2)&&(ke=4),Te===null||!(Er&268435455)&&!(nl&268435455)||kn(Te,Ae)}function Ma(e,t){var n=re;re|=2;var r=cv();(Te!==e||Ae!==t)&&(nn=null,yr(e,t));do try{dS();break}catch(o){uv(e,o)}while(!0);if(kd(),re=n,Ra.current=r,Ce!==null)throw Error(T(261));return Te=null,Ae=0,ke}function dS(){for(;Ce!==null;)dv(Ce)}function fS(){for(;Ce!==null&&!L1();)dv(Ce)}function dv(e){var t=hv(e.alternate,e,ot);e.memoizedProps=e.pendingProps,t===null?fv(e):Ce=t,zd.current=null}function fv(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=sS(n,t),n!==null){n.flags&=32767,Ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ke=6,Ce=null;return}}else if(n=oS(n,t,ot),n!==null){Ce=n;return}if(t=t.sibling,t!==null){Ce=t;return}Ce=t=e}while(t!==null);ke===0&&(ke=5)}function ar(e,t,n){var r=se,o=vt.transition;try{vt.transition=null,se=1,pS(e,t,n,r)}finally{vt.transition=o,se=r}return null}function pS(e,t,n,r){do uo();while(On!==null);if(re&6)throw Error(T(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Q1(e,s),e===Te&&(Ce=Te=null,Ae=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||_i||(_i=!0,mv(fa,function(){return uo(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=vt.transition,vt.transition=null;var i=se;se=1;var a=re;re|=4,zd.current=null,aS(e,n),iv(n,e),Ib(Xu),ha=!!Yu,Xu=Yu=null,e.current=n,lS(n),F1(),re=a,se=i,vt.transition=s}else e.current=n;if(_i&&(_i=!1,On=e,Ta=o),s=e.pendingLanes,s===0&&(Bn=null),U1(n.stateNode),tt(e,Se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ja)throw ja=!1,e=gc,gc=null,e;return Ta&1&&e.tag!==0&&uo(),s=e.pendingLanes,s&1?e===vc?ws++:(ws=0,vc=e):ws=0,nr(),null}function uo(){if(On!==null){var e=Km(Ta),t=vt.transition,n=se;try{if(vt.transition=null,se=16>e?16:e,On===null)var r=!1;else{if(e=On,On=null,Ta=0,re&6)throw Error(T(331));var o=re;for(re|=4,z=e.current;z!==null;){var s=z,i=s.child;if(z.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(z=c;z!==null;){var h=z;switch(h.tag){case 0:case 11:case 15:ys(8,h,s)}var f=h.child;if(f!==null)f.return=h,z=f;else for(;z!==null;){h=z;var d=h.sibling,y=h.return;if(rv(h),h===c){z=null;break}if(d!==null){d.return=y,z=d;break}z=y}}}var w=s.alternate;if(w!==null){var m=w.child;if(m!==null){w.child=null;do{var b=m.sibling;m.sibling=null,m=b}while(m!==null)}}z=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,z=i;else e:for(;z!==null;){if(s=z,s.flags&2048)switch(s.tag){case 0:case 11:case 15:ys(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,z=g;break e}z=s.return}}var v=e.current;for(z=v;z!==null;){i=z;var x=i.child;if(i.subtreeFlags&2064&&x!==null)x.return=i,z=x;else e:for(i=v;z!==null;){if(a=z,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:tl(9,a)}}catch(C){be(a,a.return,C)}if(a===i){z=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,z=S;break e}z=a.return}}if(re=o,nr(),Yt&&typeof Yt.onPostCommitFiberRoot=="function")try{Yt.onPostCommitFiberRoot(Qa,e)}catch{}r=!0}return r}finally{se=n,vt.transition=t}}return!1}function Xp(e,t,n){t=Ao(n,t),t=Kg(e,t,1),e=Un(e,t,1),t=We(),e!==null&&(Zs(e,1,t),tt(e,t))}function be(e,t,n){if(e.tag===3)Xp(e,e,n);else for(;t!==null;){if(t.tag===3){Xp(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Bn===null||!Bn.has(r))){e=Ao(n,e),e=Qg(t,e,1),t=Un(t,e,1),e=We(),t!==null&&(Zs(t,1,e),tt(t,e));break}}t=t.return}}function hS(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=We(),e.pingedLanes|=e.suspendedLanes&n,Te===e&&(Ae&n)===n&&(ke===4||ke===3&&(Ae&130023424)===Ae&&500>Se()-Ud?yr(e,0):$d|=n),tt(e,t)}function pv(e,t){t===0&&(e.mode&1?(t=Si,Si<<=1,!(Si&130023424)&&(Si=4194304)):t=1);var n=We();e=dn(e,t),e!==null&&(Zs(e,t,n),tt(e,n))}function mS(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pv(e,n)}function gS(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),pv(e,n)}var hv;hv=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Je.current)Ze=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ze=!1,rS(e,t,n);Ze=!!(e.flags&131072)}else Ze=!1,ge&&t.flags&1048576&&yg(t,ba,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;qi(e,t),e=t.pendingProps;var o=To(t,Ue.current);lo(t,n),o=Ad(null,t,r,e,o,n);var s=Od();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,et(r)?(s=!0,xa(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,jd(t),o.updater=el,t.stateNode=o,o._reactInternals=t,sc(t,r,e,n),t=lc(null,t,r,!0,s,n)):(t.tag=0,ge&&s&&Sd(t),He(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(qi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=yS(r),e=Pt(r,e),o){case 0:t=ac(null,t,r,e,n);break e;case 1:t=$p(null,t,r,e,n);break e;case 11:t=Fp(null,t,r,e,n);break e;case 14:t=zp(null,t,r,Pt(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Pt(r,o),ac(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Pt(r,o),$p(e,t,r,o,n);case 3:e:{if(qg(t),e===null)throw Error(T(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Eg(e,t),Ea(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=Ao(Error(T(423)),t),t=Up(e,t,r,n,o);break e}else if(r!==o){o=Ao(Error(T(424)),t),t=Up(e,t,r,n,o);break e}else for(it=$n(t.stateNode.containerInfo.firstChild),at=t,ge=!0,Tt=null,n=Sg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Mo(),r===o){t=fn(e,t,n);break e}He(e,t,r,n)}t=t.child}return t;case 5:return Ng(t),e===null&&nc(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,qu(r,o)?i=null:s!==null&&qu(r,s)&&(t.flags|=32),Xg(e,t),He(e,t,i,n),t.child;case 6:return e===null&&nc(t),null;case 13:return Zg(e,t,n);case 4:return Td(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_o(t,null,r,n):He(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Pt(r,o),Fp(e,t,r,o,n);case 7:return He(e,t,t.pendingProps,n),t.child;case 8:return He(e,t,t.pendingProps.children,n),t.child;case 12:return He(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ce(Sa,r._currentValue),r._currentValue=i,s!==null)if(At(s.value,i)){if(s.children===o.children&&!Je.current){t=fn(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){i=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=an(-1,n&-n),l.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var h=c.pending;h===null?l.next=l:(l.next=h.next,h.next=l),c.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),rc(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(T(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),rc(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}He(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,lo(t,n),o=bt(o),r=r(o),t.flags|=1,He(e,t,r,n),t.child;case 14:return r=t.type,o=Pt(r,t.pendingProps),o=Pt(r.type,o),zp(e,t,r,o,n);case 15:return Gg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Pt(r,o),qi(e,t),t.tag=1,et(r)?(e=!0,xa(t)):e=!1,lo(t,n),Wg(t,r,o),sc(t,r,o,n),lc(null,t,r,!0,e,n);case 19:return Jg(e,t,n);case 22:return Yg(e,t,n)}throw Error(T(156,t.tag))};function mv(e,t){return Bm(e,t)}function vS(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function gt(e,t,n,r){return new vS(e,t,n,r)}function Wd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function yS(e){if(typeof e=="function")return Wd(e)?1:0;if(e!=null){if(e=e.$$typeof,e===cd)return 11;if(e===dd)return 14}return 2}function Hn(e,t){var n=e.alternate;return n===null?(n=gt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ea(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Wd(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Qr:return xr(n.children,o,s,t);case ud:i=8,o|=8;break;case ju:return e=gt(12,n,t,o|2),e.elementType=ju,e.lanes=s,e;case Tu:return e=gt(13,n,t,o),e.elementType=Tu,e.lanes=s,e;case Mu:return e=gt(19,n,t,o),e.elementType=Mu,e.lanes=s,e;case Nm:return rl(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cm:i=10;break e;case Em:i=9;break e;case cd:i=11;break e;case dd:i=14;break e;case Cn:i=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=gt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function xr(e,t,n,r){return e=gt(7,e,r,t),e.lanes=n,e}function rl(e,t,n,r){return e=gt(22,e,r,t),e.elementType=Nm,e.lanes=n,e.stateNode={isHidden:!1},e}function uu(e,t,n){return e=gt(6,e,null,t),e.lanes=n,e}function cu(e,t,n){return t=gt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function xS(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Hl(0),this.expirationTimes=Hl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Kd(e,t,n,r,o,s,i,a,l){return e=new xS(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=gt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},jd(s),e}function wS(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Kr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gv(e){if(!e)return Gn;e=e._reactInternals;e:{if(Tr(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(et(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(et(n))return gg(e,n,t)}return t}function vv(e,t,n,r,o,s,i,a,l){return e=Kd(n,r,!0,e,o,s,i,a,l),e.context=gv(null),n=e.current,r=We(),o=Vn(n),s=an(r,o),s.callback=t??null,Un(n,s,o),e.current.lanes=o,Zs(e,o,r),tt(e,r),e}function ol(e,t,n,r){var o=t.current,s=We(),i=Vn(o);return n=gv(n),t.context===null?t.context=n:t.pendingContext=n,t=an(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Un(o,t,i),e!==null&&(It(e,o,i,s),Gi(e,o,i)),i}function _a(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function qp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Qd(e,t){qp(e,t),(e=e.alternate)&&qp(e,t)}function bS(){return null}var yv=typeof reportError=="function"?reportError:function(e){console.error(e)};function Gd(e){this._internalRoot=e}sl.prototype.render=Gd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));ol(e,t,null,null)};sl.prototype.unmount=Gd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nr(function(){ol(null,e,null,null)}),t[cn]=null}};function sl(e){this._internalRoot=e}sl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ym();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Nn.length&&t!==0&&t<Nn[n].priority;n++);Nn.splice(n,0,e),n===0&&qm(e)}};function Yd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function il(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Zp(){}function SS(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var c=_a(i);s.call(c)}}var i=vv(t,r,e,0,null,!1,!1,"",Zp);return e._reactRootContainer=i,e[cn]=i.current,Ts(e.nodeType===8?e.parentNode:e),Nr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var c=_a(l);a.call(c)}}var l=Kd(e,0,!1,null,null,!1,!1,"",Zp);return e._reactRootContainer=l,e[cn]=l.current,Ts(e.nodeType===8?e.parentNode:e),Nr(function(){ol(t,l,n,r)}),l}function al(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var a=o;o=function(){var l=_a(i);a.call(l)}}ol(t,i,e,o)}else i=SS(n,t,e,o,r);return _a(i)}Qm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=is(t.pendingLanes);n!==0&&(hd(t,n|1),tt(t,Se()),!(re&6)&&(Oo=Se()+500,nr()))}break;case 13:Nr(function(){var r=dn(e,1);if(r!==null){var o=We();It(r,e,1,o)}}),Qd(e,1)}};md=function(e){if(e.tag===13){var t=dn(e,134217728);if(t!==null){var n=We();It(t,e,134217728,n)}Qd(e,134217728)}};Gm=function(e){if(e.tag===13){var t=Vn(e),n=dn(e,t);if(n!==null){var r=We();It(n,e,t,r)}Qd(e,t)}};Ym=function(){return se};Xm=function(e,t){var n=se;try{return se=e,t()}finally{se=n}};Uu=function(e,t,n){switch(t){case"input":if(Au(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=qa(r);if(!o)throw Error(T(90));Pm(r),Au(r,o)}}}break;case"textarea":jm(e,n);break;case"select":t=n.value,t!=null&&oo(e,!!n.multiple,t,!1)}};Dm=Bd;Lm=Nr;var CS={usingClientEntryPoint:!1,Events:[ei,qr,qa,Am,Om,Bd]},ts={findFiberByHostInstance:lr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ES={bundleType:ts.bundleType,version:ts.version,rendererPackageName:ts.rendererPackageName,rendererConfig:ts.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:mn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=$m(e),e===null?null:e.stateNode},findFiberByHostInstance:ts.findFiberByHostInstance||bS,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ii=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ii.isDisabled&&Ii.supportsFiber)try{Qa=Ii.inject(ES),Yt=Ii}catch{}}ct.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=CS;ct.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Yd(t))throw Error(T(200));return wS(e,t,null,n)};ct.createRoot=function(e,t){if(!Yd(e))throw Error(T(299));var n=!1,r="",o=yv;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Kd(e,1,!1,null,null,n,!1,r,o),e[cn]=t.current,Ts(e.nodeType===8?e.parentNode:e),new Gd(t)};ct.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=$m(t),e=e===null?null:e.stateNode,e};ct.flushSync=function(e){return Nr(e)};ct.hydrate=function(e,t,n){if(!il(t))throw Error(T(200));return al(null,e,t,!0,n)};ct.hydrateRoot=function(e,t,n){if(!Yd(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=yv;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=vv(t,null,e,1,n??null,o,!1,s,i),e[cn]=t.current,Ts(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new sl(t)};ct.render=function(e,t,n){if(!il(t))throw Error(T(200));return al(null,e,t,!1,n)};ct.unmountComponentAtNode=function(e){if(!il(e))throw Error(T(40));return e._reactRootContainer?(Nr(function(){al(null,null,e,!1,function(){e._reactRootContainer=null,e[cn]=null})}),!0):!1};ct.unstable_batchedUpdates=Bd;ct.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!il(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return al(e,t,n,!1,r)};ct.version="18.3.1-next-f1338f8080-20240426";function xv(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(xv)}catch(e){console.error(e)}}xv(),xm.exports=ct;var ni=xm.exports;const wv=im(ni);var bv,Jp=ni;bv=Jp.createRoot,Jp.hydrateRoot;const NS=1,kS=1e6;let du=0;function PS(){return du=(du+1)%Number.MAX_SAFE_INTEGER,du.toString()}const fu=new Map,eh=e=>{if(fu.has(e))return;const t=setTimeout(()=>{fu.delete(e),bs({type:"REMOVE_TOAST",toastId:e})},kS);fu.set(e,t)},RS=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,NS)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?eh(n):e.toasts.forEach(r=>{eh(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},ta=[];let na={toasts:[]};function bs(e){na=RS(na,e),ta.forEach(t=>{t(na)})}function jS({...e}){const t=PS(),n=o=>bs({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>bs({type:"DISMISS_TOAST",toastId:t});return bs({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function TS(){const[e,t]=p.useState(na);return p.useEffect(()=>(ta.push(t),()=>{const n=ta.indexOf(t);n>-1&&ta.splice(n,1)}),[e]),{...e,toast:jS,dismiss:n=>bs({type:"DISMISS_TOAST",toastId:n})}}function $(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function MS(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function ll(...e){return t=>e.forEach(n=>MS(n,t))}function ae(...e){return p.useCallback(ll(...e),e)}function _S(e,t=[]){let n=[];function r(s,i){const a=p.createContext(i),l=n.length;n=[...n,i];function c(f){const{scope:d,children:y,...w}=f,m=(d==null?void 0:d[e][l])||a,b=p.useMemo(()=>w,Object.values(w));return u.jsx(m.Provider,{value:b,children:y})}function h(f,d){const y=(d==null?void 0:d[e][l])||a,w=p.useContext(y);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return c.displayName=s+"Provider",[c,h]}const o=()=>{const s=n.map(i=>p.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,IS(o,...t)]}function IS(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(s)[`__scope${c}`];return{...a,...f}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Ct=p.forwardRef((e,t)=>{const{children:n,...r}=e,o=p.Children.toArray(n),s=o.find(AS);if(s){const i=s.props.children,a=o.map(l=>l===s?p.Children.count(i)>1?p.Children.only(null):p.isValidElement(i)?i.props.children:null:l);return u.jsx(wc,{...r,ref:t,children:p.isValidElement(i)?p.cloneElement(i,void 0,a):null})}return u.jsx(wc,{...r,ref:t,children:n})});Ct.displayName="Slot";var wc=p.forwardRef((e,t)=>{const{children:n,...r}=e;if(p.isValidElement(n)){const o=DS(n);return p.cloneElement(n,{...OS(r,n.props),ref:t?ll(t,o):o})}return p.Children.count(n)>1?p.Children.only(null):null});wc.displayName="SlotClone";var Sv=({children:e})=>u.jsx(u.Fragment,{children:e});function AS(e){return p.isValidElement(e)&&e.type===Sv}function OS(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function DS(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function ul(e){const t=e+"CollectionProvider",[n,r]=_S(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=y=>{const{scope:w,children:m}=y,b=I.useRef(null),g=I.useRef(new Map).current;return u.jsx(o,{scope:w,itemMap:g,collectionRef:b,children:m})};i.displayName=t;const a=e+"CollectionSlot",l=I.forwardRef((y,w)=>{const{scope:m,children:b}=y,g=s(a,m),v=ae(w,g.collectionRef);return u.jsx(Ct,{ref:v,children:b})});l.displayName=a;const c=e+"CollectionItemSlot",h="data-radix-collection-item",f=I.forwardRef((y,w)=>{const{scope:m,children:b,...g}=y,v=I.useRef(null),x=ae(w,v),S=s(c,m);return I.useEffect(()=>(S.itemMap.set(v,{ref:v,...g}),()=>void S.itemMap.delete(v))),u.jsx(Ct,{[h]:"",ref:x,children:b})});f.displayName=c;function d(y){const w=s(e+"CollectionConsumer",y);return I.useCallback(()=>{const b=w.collectionRef.current;if(!b)return[];const g=Array.from(b.querySelectorAll(`[${h}]`));return Array.from(w.itemMap.values()).sort((S,C)=>g.indexOf(S.ref.current)-g.indexOf(C.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},d,r]}function LS(e,t){const n=p.createContext(t),r=s=>{const{children:i,...a}=s,l=p.useMemo(()=>a,Object.values(a));return u.jsx(n.Provider,{value:l,children:i})};r.displayName=e+"Provider";function o(s){const i=p.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function rr(e,t=[]){let n=[];function r(s,i){const a=p.createContext(i),l=n.length;n=[...n,i];const c=f=>{var g;const{scope:d,children:y,...w}=f,m=((g=d==null?void 0:d[e])==null?void 0:g[l])||a,b=p.useMemo(()=>w,Object.values(w));return u.jsx(m.Provider,{value:b,children:y})};c.displayName=s+"Provider";function h(f,d){var m;const y=((m=d==null?void 0:d[e])==null?void 0:m[l])||a,w=p.useContext(y);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return[c,h]}const o=()=>{const s=n.map(i=>p.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,FS(o,...t)]}function FS(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(s)[`__scope${c}`];return{...a,...f}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var zS=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Y=zS.reduce((e,t)=>{const n=p.forwardRef((r,o)=>{const{asChild:s,...i}=r,a=s?Ct:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(a,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Xd(e,t){e&&ni.flushSync(()=>e.dispatchEvent(t))}function Ke(e){const t=p.useRef(e);return p.useEffect(()=>{t.current=e}),p.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function $S(e,t=globalThis==null?void 0:globalThis.document){const n=Ke(e);p.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var US="DismissableLayer",bc="dismissableLayer.update",BS="dismissableLayer.pointerDownOutside",VS="dismissableLayer.focusOutside",th,Cv=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ri=p.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,c=p.useContext(Cv),[h,f]=p.useState(null),d=(h==null?void 0:h.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=p.useState({}),w=ae(t,N=>f(N)),m=Array.from(c.layers),[b]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),g=m.indexOf(b),v=h?m.indexOf(h):-1,x=c.layersWithOutsidePointerEventsDisabled.size>0,S=v>=g,C=WS(N=>{const R=N.target,M=[...c.branches].some(_=>_.contains(R));!S||M||(o==null||o(N),i==null||i(N),N.defaultPrevented||a==null||a())},d),P=KS(N=>{const R=N.target;[...c.branches].some(_=>_.contains(R))||(s==null||s(N),i==null||i(N),N.defaultPrevented||a==null||a())},d);return $S(N=>{v===c.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&a&&(N.preventDefault(),a()))},d),p.useEffect(()=>{if(h)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(th=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(h)),c.layers.add(h),nh(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=th)}},[h,d,n,c]),p.useEffect(()=>()=>{h&&(c.layers.delete(h),c.layersWithOutsidePointerEventsDisabled.delete(h),nh())},[h,c]),p.useEffect(()=>{const N=()=>y({});return document.addEventListener(bc,N),()=>document.removeEventListener(bc,N)},[]),u.jsx(Y.div,{...l,ref:w,style:{pointerEvents:x?S?"auto":"none":void 0,...e.style},onFocusCapture:$(e.onFocusCapture,P.onFocusCapture),onBlurCapture:$(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:$(e.onPointerDownCapture,C.onPointerDownCapture)})});ri.displayName=US;var HS="DismissableLayerBranch",Ev=p.forwardRef((e,t)=>{const n=p.useContext(Cv),r=p.useRef(null),o=ae(t,r);return p.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),u.jsx(Y.div,{...e,ref:o})});Ev.displayName=HS;function WS(e,t=globalThis==null?void 0:globalThis.document){const n=Ke(e),r=p.useRef(!1),o=p.useRef(()=>{});return p.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){Nv(BS,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function KS(e,t=globalThis==null?void 0:globalThis.document){const n=Ke(e),r=p.useRef(!1);return p.useEffect(()=>{const o=s=>{s.target&&!r.current&&Nv(VS,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function nh(){const e=new CustomEvent(bc);document.dispatchEvent(e)}function Nv(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Xd(o,s):o.dispatchEvent(s)}var QS=ri,GS=Ev,Yn=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},YS="Portal",cl=p.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=p.useState(!1);Yn(()=>s(!0),[]);const i=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?wv.createPortal(u.jsx(Y.div,{...r,ref:t}),i):null});cl.displayName=YS;function XS(e,t){return p.useReducer((n,r)=>t[n][r]??n,e)}var Lt=e=>{const{present:t,children:n}=e,r=qS(t),o=typeof n=="function"?n({present:r.isPresent}):p.Children.only(n),s=ae(r.ref,ZS(o));return typeof n=="function"||r.isPresent?p.cloneElement(o,{ref:s}):null};Lt.displayName="Presence";function qS(e){const[t,n]=p.useState(),r=p.useRef({}),o=p.useRef(e),s=p.useRef("none"),i=e?"mounted":"unmounted",[a,l]=XS(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const c=Ai(r.current);s.current=a==="mounted"?c:"none"},[a]),Yn(()=>{const c=r.current,h=o.current;if(h!==e){const d=s.current,y=Ai(c);e?l("MOUNT"):y==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(h&&d!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Yn(()=>{if(t){let c;const h=t.ownerDocument.defaultView??window,f=y=>{const m=Ai(r.current).includes(y.animationName);if(y.target===t&&m&&(l("ANIMATION_END"),!o.current)){const b=t.style.animationFillMode;t.style.animationFillMode="forwards",c=h.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=b)})}},d=y=>{y.target===t&&(s.current=Ai(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{h.clearTimeout(c),t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:p.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function Ai(e){return(e==null?void 0:e.animationName)||"none"}function ZS(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function or({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=JS({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,a=Ke(n),l=p.useCallback(c=>{if(s){const f=typeof c=="function"?c(e):c;f!==e&&a(f)}else o(c)},[s,e,o,a]);return[i,l]}function JS({defaultProp:e,onChange:t}){const n=p.useState(e),[r]=n,o=p.useRef(r),s=Ke(t);return p.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var eC="VisuallyHidden",dl=p.forwardRef((e,t)=>u.jsx(Y.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));dl.displayName=eC;var tC=dl,qd="ToastProvider",[Zd,nC,rC]=ul("Toast"),[kv,yM]=rr("Toast",[rC]),[oC,fl]=kv(qd),Pv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[a,l]=p.useState(null),[c,h]=p.useState(0),f=p.useRef(!1),d=p.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${qd}\`. Expected non-empty \`string\`.`),u.jsx(Zd.Provider,{scope:t,children:u.jsx(oC,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:c,viewport:a,onViewportChange:l,onToastAdd:p.useCallback(()=>h(y=>y+1),[]),onToastRemove:p.useCallback(()=>h(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:d,children:i})})};Pv.displayName=qd;var Rv="ToastViewport",sC=["F8"],Sc="toast.viewportPause",Cc="toast.viewportResume",jv=p.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=sC,label:o="Notifications ({hotkey})",...s}=e,i=fl(Rv,n),a=nC(n),l=p.useRef(null),c=p.useRef(null),h=p.useRef(null),f=p.useRef(null),d=ae(t,f,i.onViewportChange),y=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=i.toastCount>0;p.useEffect(()=>{const b=g=>{var x;r.length!==0&&r.every(S=>g[S]||g.code===S)&&((x=f.current)==null||x.focus())};return document.addEventListener("keydown",b),()=>document.removeEventListener("keydown",b)},[r]),p.useEffect(()=>{const b=l.current,g=f.current;if(w&&b&&g){const v=()=>{if(!i.isClosePausedRef.current){const P=new CustomEvent(Sc);g.dispatchEvent(P),i.isClosePausedRef.current=!0}},x=()=>{if(i.isClosePausedRef.current){const P=new CustomEvent(Cc);g.dispatchEvent(P),i.isClosePausedRef.current=!1}},S=P=>{!b.contains(P.relatedTarget)&&x()},C=()=>{b.contains(document.activeElement)||x()};return b.addEventListener("focusin",v),b.addEventListener("focusout",S),b.addEventListener("pointermove",v),b.addEventListener("pointerleave",C),window.addEventListener("blur",v),window.addEventListener("focus",x),()=>{b.removeEventListener("focusin",v),b.removeEventListener("focusout",S),b.removeEventListener("pointermove",v),b.removeEventListener("pointerleave",C),window.removeEventListener("blur",v),window.removeEventListener("focus",x)}}},[w,i.isClosePausedRef]);const m=p.useCallback(({tabbingDirection:b})=>{const v=a().map(x=>{const S=x.ref.current,C=[S,...yC(S)];return b==="forwards"?C:C.reverse()});return(b==="forwards"?v.reverse():v).flat()},[a]);return p.useEffect(()=>{const b=f.current;if(b){const g=v=>{var C,P,N;const x=v.altKey||v.ctrlKey||v.metaKey;if(v.key==="Tab"&&!x){const R=document.activeElement,M=v.shiftKey;if(v.target===b&&M){(C=c.current)==null||C.focus();return}const A=m({tabbingDirection:M?"backwards":"forwards"}),W=A.findIndex(O=>O===R);pu(A.slice(W+1))?v.preventDefault():M?(P=c.current)==null||P.focus():(N=h.current)==null||N.focus()}};return b.addEventListener("keydown",g),()=>b.removeEventListener("keydown",g)}},[a,m]),u.jsxs(GS,{ref:l,role:"region","aria-label":o.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&u.jsx(Ec,{ref:c,onFocusFromOutsideViewport:()=>{const b=m({tabbingDirection:"forwards"});pu(b)}}),u.jsx(Zd.Slot,{scope:n,children:u.jsx(Y.ol,{tabIndex:-1,...s,ref:d})}),w&&u.jsx(Ec,{ref:h,onFocusFromOutsideViewport:()=>{const b=m({tabbingDirection:"backwards"});pu(b)}})]})});jv.displayName=Rv;var Tv="ToastFocusProxy",Ec=p.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=fl(Tv,n);return u.jsx(dl,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var c;const a=i.relatedTarget;!((c=s.viewport)!=null&&c.contains(a))&&r()}})});Ec.displayName=Tv;var pl="Toast",iC="toast.swipeStart",aC="toast.swipeMove",lC="toast.swipeCancel",uC="toast.swipeEnd",Mv=p.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[a=!0,l]=or({prop:r,defaultProp:o,onChange:s});return u.jsx(Lt,{present:n||a,children:u.jsx(fC,{open:a,...i,ref:t,onClose:()=>l(!1),onPause:Ke(e.onPause),onResume:Ke(e.onResume),onSwipeStart:$(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:$(e.onSwipeMove,c=>{const{x:h,y:f}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${h}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:$(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:$(e.onSwipeEnd,c=>{const{x:h,y:f}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${h}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),l(!1)})})})});Mv.displayName=pl;var[cC,dC]=kv(pl,{onClose(){}}),fC=p.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:a,onPause:l,onResume:c,onSwipeStart:h,onSwipeMove:f,onSwipeCancel:d,onSwipeEnd:y,...w}=e,m=fl(pl,n),[b,g]=p.useState(null),v=ae(t,O=>g(O)),x=p.useRef(null),S=p.useRef(null),C=o||m.duration,P=p.useRef(0),N=p.useRef(C),R=p.useRef(0),{onToastAdd:M,onToastRemove:_}=m,L=Ke(()=>{var G;(b==null?void 0:b.contains(document.activeElement))&&((G=m.viewport)==null||G.focus()),i()}),A=p.useCallback(O=>{!O||O===1/0||(window.clearTimeout(R.current),P.current=new Date().getTime(),R.current=window.setTimeout(L,O))},[L]);p.useEffect(()=>{const O=m.viewport;if(O){const G=()=>{A(N.current),c==null||c()},B=()=>{const K=new Date().getTime()-P.current;N.current=N.current-K,window.clearTimeout(R.current),l==null||l()};return O.addEventListener(Sc,B),O.addEventListener(Cc,G),()=>{O.removeEventListener(Sc,B),O.removeEventListener(Cc,G)}}},[m.viewport,C,l,c,A]),p.useEffect(()=>{s&&!m.isClosePausedRef.current&&A(C)},[s,C,m.isClosePausedRef,A]),p.useEffect(()=>(M(),()=>_()),[M,_]);const W=p.useMemo(()=>b?Fv(b):null,[b]);return m.viewport?u.jsxs(u.Fragment,{children:[W&&u.jsx(pC,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:W}),u.jsx(cC,{scope:n,onClose:L,children:ni.createPortal(u.jsx(Zd.ItemSlot,{scope:n,children:u.jsx(QS,{asChild:!0,onEscapeKeyDown:$(a,()=>{m.isFocusedToastEscapeKeyDownRef.current||L(),m.isFocusedToastEscapeKeyDownRef.current=!1}),children:u.jsx(Y.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":m.swipeDirection,...w,ref:v,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:$(e.onKeyDown,O=>{O.key==="Escape"&&(a==null||a(O.nativeEvent),O.nativeEvent.defaultPrevented||(m.isFocusedToastEscapeKeyDownRef.current=!0,L()))}),onPointerDown:$(e.onPointerDown,O=>{O.button===0&&(x.current={x:O.clientX,y:O.clientY})}),onPointerMove:$(e.onPointerMove,O=>{if(!x.current)return;const G=O.clientX-x.current.x,B=O.clientY-x.current.y,K=!!S.current,k=["left","right"].includes(m.swipeDirection),j=["left","up"].includes(m.swipeDirection)?Math.min:Math.max,F=k?j(0,G):0,D=k?0:j(0,B),H=O.pointerType==="touch"?10:2,Z={x:F,y:D},ie={originalEvent:O,delta:Z};K?(S.current=Z,Oi(aC,f,ie,{discrete:!1})):rh(Z,m.swipeDirection,H)?(S.current=Z,Oi(iC,h,ie,{discrete:!1}),O.target.setPointerCapture(O.pointerId)):(Math.abs(G)>H||Math.abs(B)>H)&&(x.current=null)}),onPointerUp:$(e.onPointerUp,O=>{const G=S.current,B=O.target;if(B.hasPointerCapture(O.pointerId)&&B.releasePointerCapture(O.pointerId),S.current=null,x.current=null,G){const K=O.currentTarget,k={originalEvent:O,delta:G};rh(G,m.swipeDirection,m.swipeThreshold)?Oi(uC,y,k,{discrete:!0}):Oi(lC,d,k,{discrete:!0}),K.addEventListener("click",j=>j.preventDefault(),{once:!0})}})})})}),m.viewport)})]}):null}),pC=e=>{const{__scopeToast:t,children:n,...r}=e,o=fl(pl,t),[s,i]=p.useState(!1),[a,l]=p.useState(!1);return gC(()=>i(!0)),p.useEffect(()=>{const c=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:u.jsx(cl,{asChild:!0,children:u.jsx(dl,{...r,children:s&&u.jsxs(u.Fragment,{children:[o.label," ",n]})})})},hC="ToastTitle",_v=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return u.jsx(Y.div,{...r,ref:t})});_v.displayName=hC;var mC="ToastDescription",Iv=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return u.jsx(Y.div,{...r,ref:t})});Iv.displayName=mC;var Av="ToastAction",Ov=p.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?u.jsx(Lv,{altText:n,asChild:!0,children:u.jsx(Jd,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Av}\`. Expected non-empty \`string\`.`),null)});Ov.displayName=Av;var Dv="ToastClose",Jd=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=dC(Dv,n);return u.jsx(Lv,{asChild:!0,children:u.jsx(Y.button,{type:"button",...r,ref:t,onClick:$(e.onClick,o.onClose)})})});Jd.displayName=Dv;var Lv=p.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return u.jsx(Y.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Fv(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),vC(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...Fv(r))}}),t}function Oi(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Xd(o,s):o.dispatchEvent(s)}var rh=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function gC(e=()=>{}){const t=Ke(e);Yn(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function vC(e){return e.nodeType===e.ELEMENT_NODE}function yC(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function pu(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var xC=Pv,zv=jv,$v=Mv,Uv=_v,Bv=Iv,Vv=Ov,Hv=Jd;function Wv(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Wv(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Kv(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Wv(e))&&(r&&(r+=" "),r+=t);return r}const oh=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,sh=Kv,Uo=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return sh(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(c=>{const h=n==null?void 0:n[c],f=s==null?void 0:s[c];if(h===null)return null;const d=oh(h)||oh(f);return o[c][d]}),a=n&&Object.entries(n).reduce((c,h)=>{let[f,d]=h;return d===void 0||(c[f]=d),c},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,h)=>{let{class:f,className:d,...y}=h;return Object.entries(y).every(w=>{let[m,b]=w;return Array.isArray(b)?b.includes({...s,...a}[m]):{...s,...a}[m]===b})?[...c,f,d]:c},[]);return sh(e,i,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wC=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Qv=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var bC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SC=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...a},l)=>p.createElement("svg",{ref:l,...bC,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Qv("lucide",o),...a},[...i.map(([c,h])=>p.createElement(c,h)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=(e,t)=>{const n=p.forwardRef(({className:r,...o},s)=>p.createElement(SC,{ref:s,iconNode:t,className:Qv(`lucide-${wC(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CC=le("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ef=le("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hl=le("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EC=le("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NC=le("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kC=le("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PC=le("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ml=le("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gv=le("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yv=le("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RC=le("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jC=le("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xv=le("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TC=le("Images",[["path",{d:"M18 22H4a2 2 0 0 1-2-2V6",key:"pblm9e"}],["path",{d:"m22 13-1.296-1.296a2.41 2.41 0 0 0-3.408 0L11 18",key:"nf6bnh"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}],["rect",{width:"16",height:"16",x:"6",y:"2",rx:"2",key:"12espp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oi=le("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MC=le("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _C=le("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IC=le("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AC=le("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ia=le("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qv=le("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OC=le("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DC=le("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Aa=le("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ih=le("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LC=le("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const si=le("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),tf="-",FC=e=>{const t=$C(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(tf);return a[0]===""&&a.length!==1&&a.shift(),Zv(a,t)||zC(i)},getConflictingClassGroupIds:(i,a)=>{const l=n[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},Zv=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Zv(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(tf);return(i=t.validators.find(({validator:a})=>a(s)))==null?void 0:i.classGroupId},ah=/^\[(.+)\]$/,zC=e=>{if(ah.test(e)){const t=ah.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},$C=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return BC(Object.entries(e.classGroups),n).forEach(([s,i])=>{Nc(i,r,s,t)}),r},Nc=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:lh(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(UC(o)){Nc(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Nc(i,lh(t,s),n,r)})})},lh=(e,t)=>{let n=e;return t.split(tf).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},UC=e=>e.isThemeGetter,BC=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,a])=>[t+i,a])):s);return[n,o]}):e,VC=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Jv="!",HC=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=a=>{const l=[];let c=0,h=0,f;for(let b=0;b<a.length;b++){let g=a[b];if(c===0){if(g===o&&(r||a.slice(b,b+s)===t)){l.push(a.slice(h,b)),h=b+s;continue}if(g==="/"){f=b;continue}}g==="["?c++:g==="]"&&c--}const d=l.length===0?a:a.substring(h),y=d.startsWith(Jv),w=y?d.substring(1):d,m=f&&f>h?f-h:void 0;return{modifiers:l,hasImportantModifier:y,baseClassName:w,maybePostfixModifierPosition:m}};return n?a=>n({className:a,parseClassName:i}):i},WC=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},KC=e=>({cache:VC(e.cacheSize),parseClassName:HC(e),...FC(e)}),QC=/\s+/,GC=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(QC);let a="";for(let l=i.length-1;l>=0;l-=1){const c=i[l],{modifiers:h,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:y}=n(c);let w=!!y,m=r(w?d.substring(0,y):d);if(!m){if(!w){a=c+(a.length>0?" "+a:a);continue}if(m=r(d),!m){a=c+(a.length>0?" "+a:a);continue}w=!1}const b=WC(h).join(":"),g=f?b+Jv:b,v=g+m;if(s.includes(v))continue;s.push(v);const x=o(m,w);for(let S=0;S<x.length;++S){const C=x[S];s.push(g+C)}a=c+(a.length>0?" "+a:a)}return a};function YC(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=ey(t))&&(r&&(r+=" "),r+=n);return r}const ey=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=ey(e[r]))&&(n&&(n+=" "),n+=t);return n};function XC(e,...t){let n,r,o,s=i;function i(l){const c=t.reduce((h,f)=>f(h),e());return n=KC(c),r=n.cache.get,o=n.cache.set,s=a,a(l)}function a(l){const c=r(l);if(c)return c;const h=GC(l,n);return o(l,h),h}return function(){return s(YC.apply(null,arguments))}}const fe=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},ty=/^\[(?:([a-z-]+):)?(.+)\]$/i,qC=/^\d+\/\d+$/,ZC=new Set(["px","full","screen"]),JC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,eE=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,tE=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,nE=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,rE=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Jt=e=>co(e)||ZC.has(e)||qC.test(e),xn=e=>Bo(e,"length",dE),co=e=>!!e&&!Number.isNaN(Number(e)),hu=e=>Bo(e,"number",co),ns=e=>!!e&&Number.isInteger(Number(e)),oE=e=>e.endsWith("%")&&co(e.slice(0,-1)),q=e=>ty.test(e),wn=e=>JC.test(e),sE=new Set(["length","size","percentage"]),iE=e=>Bo(e,sE,ny),aE=e=>Bo(e,"position",ny),lE=new Set(["image","url"]),uE=e=>Bo(e,lE,pE),cE=e=>Bo(e,"",fE),rs=()=>!0,Bo=(e,t,n)=>{const r=ty.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},dE=e=>eE.test(e)&&!tE.test(e),ny=()=>!1,fE=e=>nE.test(e),pE=e=>rE.test(e),hE=()=>{const e=fe("colors"),t=fe("spacing"),n=fe("blur"),r=fe("brightness"),o=fe("borderColor"),s=fe("borderRadius"),i=fe("borderSpacing"),a=fe("borderWidth"),l=fe("contrast"),c=fe("grayscale"),h=fe("hueRotate"),f=fe("invert"),d=fe("gap"),y=fe("gradientColorStops"),w=fe("gradientColorStopPositions"),m=fe("inset"),b=fe("margin"),g=fe("opacity"),v=fe("padding"),x=fe("saturate"),S=fe("scale"),C=fe("sepia"),P=fe("skew"),N=fe("space"),R=fe("translate"),M=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],L=()=>["auto",q,t],A=()=>[q,t],W=()=>["",Jt,xn],O=()=>["auto",co,q],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],K=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],k=()=>["start","end","center","between","around","evenly","stretch"],j=()=>["","0",q],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[co,q];return{cacheSize:500,separator:":",theme:{colors:[rs],spacing:[Jt,xn],blur:["none","",wn,q],brightness:D(),borderColor:[e],borderRadius:["none","","full",wn,q],borderSpacing:A(),borderWidth:W(),contrast:D(),grayscale:j(),hueRotate:D(),invert:j(),gap:A(),gradientColorStops:[e],gradientColorStopPositions:[oE,xn],inset:L(),margin:L(),opacity:D(),padding:A(),saturate:D(),scale:D(),sepia:j(),skew:D(),space:A(),translate:A()},classGroups:{aspect:[{aspect:["auto","square","video",q]}],container:["container"],columns:[{columns:[wn]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),q]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",ns,q]}],basis:[{basis:L()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",q]}],grow:[{grow:j()}],shrink:[{shrink:j()}],order:[{order:["first","last","none",ns,q]}],"grid-cols":[{"grid-cols":[rs]}],"col-start-end":[{col:["auto",{span:["full",ns,q]},q]}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":[rs]}],"row-start-end":[{row:["auto",{span:[ns,q]},q]}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",q]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...k()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...k(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...k(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",q,t]}],"min-w":[{"min-w":[q,t,"min","max","fit"]}],"max-w":[{"max-w":[q,t,"none","full","min","max","fit","prose",{screen:[wn]},wn]}],h:[{h:[q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",wn,xn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",hu]}],"font-family":[{font:[rs]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",q]}],"line-clamp":[{"line-clamp":["none",co,hu]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Jt,q]}],"list-image":[{"list-image":["none",q]}],"list-style-type":[{list:["none","disc","decimal",q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Jt,xn]}],"underline-offset":[{"underline-offset":["auto",Jt,q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),aE]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",iE]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},uE]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[Jt,q]}],"outline-w":[{outline:[Jt,xn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[Jt,xn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",wn,cE]}],"shadow-color":[{shadow:[rs]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...K(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":K()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",wn,q]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[f]}],saturate:[{saturate:[x]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",q]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",q]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[ns,q]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Jt,xn,hu]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},mE=XC(hE);function V(...e){return mE(Kv(e))}const gE=xC,ry=p.forwardRef(({className:e,...t},n)=>u.jsx(zv,{ref:n,className:V("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));ry.displayName=zv.displayName;const vE=Uo("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),oy=p.forwardRef(({className:e,variant:t,...n},r)=>u.jsx($v,{ref:r,className:V(vE({variant:t}),e),...n}));oy.displayName=$v.displayName;const yE=p.forwardRef(({className:e,...t},n)=>u.jsx(Vv,{ref:n,className:V("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));yE.displayName=Vv.displayName;const sy=p.forwardRef(({className:e,...t},n)=>u.jsx(Hv,{ref:n,className:V("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:u.jsx(si,{className:"h-4 w-4"})}));sy.displayName=Hv.displayName;const iy=p.forwardRef(({className:e,...t},n)=>u.jsx(Uv,{ref:n,className:V("text-sm font-semibold",e),...t}));iy.displayName=Uv.displayName;const ay=p.forwardRef(({className:e,...t},n)=>u.jsx(Bv,{ref:n,className:V("text-sm opacity-90",e),...t}));ay.displayName=Bv.displayName;function xE(){const{toasts:e}=TS();return u.jsxs(gE,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return u.jsxs(oy,{...s,children:[u.jsxs("div",{className:"grid gap-1",children:[n&&u.jsx(iy,{children:n}),r&&u.jsx(ay,{children:r})]}),o,u.jsx(sy,{})]},t)}),u.jsx(ry,{})]})}var uh=["light","dark"],wE="(prefers-color-scheme: dark)",bE=p.createContext(void 0),SE={setTheme:e=>{},themes:[]},CE=()=>{var e;return(e=p.useContext(bE))!=null?e:SE};p.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:s,value:i,attrs:a,nonce:l})=>{let c=s==="system",h=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${a.map(w=>`'${w}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,f=o?uh.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(w,m=!1,b=!0)=>{let g=i?i[w]:w,v=m?w+"|| ''":`'${g}'`,x="";return o&&b&&!m&&uh.includes(w)&&(x+=`d.style.colorScheme = '${w}';`),n==="class"?m||g?x+=`c.add(${v})`:x+="null":g&&(x+=`d[s](n,${v})`),x},y=e?`!function(){${h}${d(e)}}()`:r?`!function(){try{${h}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${c})){var t='${wE}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}${c?"":"else{"+d(s,!1,!1)+"}"}${f}}catch(e){}}()`:`!function(){try{${h}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}else{${d(s,!1,!1)};}${f}}catch(t){}}();`;return p.createElement("script",{nonce:l,dangerouslySetInnerHTML:{__html:y}})});var EE=e=>{switch(e){case"success":return PE;case"info":return jE;case"warning":return RE;case"error":return TE;default:return null}},NE=Array(12).fill(0),kE=({visible:e})=>I.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},I.createElement("div",{className:"sonner-spinner"},NE.map((t,n)=>I.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),PE=I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},I.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),RE=I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},I.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),jE=I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},I.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),TE=I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},I.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ME=()=>{let[e,t]=I.useState(document.hidden);return I.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},kc=1,_E=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:kc++,s=this.toasts.find(a=>a.id===o),i=e.dismissible===void 0?!0:e.dismissible;return s?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:i,title:n}):a):this.addToast({title:n,...r,dismissible:i,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async s=>{if(AE(s)&&!s.ok){o=!1;let i=typeof t.error=="function"?await t.error(`HTTP error! status: ${s.status}`):t.error,a=typeof t.description=="function"?await t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:n,type:"error",message:i,description:a})}else if(t.success!==void 0){o=!1;let i=typeof t.success=="function"?await t.success(s):t.success,a=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"success",message:i,description:a})}}).catch(async s=>{if(t.error!==void 0){o=!1;let i=typeof t.error=="function"?await t.error(s):t.error,a=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"error",message:i,description:a})}}).finally(()=>{var s;o&&(this.dismiss(n),n=void 0),(s=t.finally)==null||s.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||kc++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},rt=new _E,IE=(e,t)=>{let n=(t==null?void 0:t.id)||kc++;return rt.addToast({title:e,...t,id:n}),n},AE=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",OE=IE,DE=()=>rt.toasts;Object.assign(OE,{success:rt.success,info:rt.info,warning:rt.warning,error:rt.error,custom:rt.custom,message:rt.message,promise:rt.promise,dismiss:rt.dismiss,loading:rt.loading},{getHistory:DE});function LE(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}LE(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Di(e){return e.label!==void 0}var FE=3,zE="32px",$E=4e3,UE=356,BE=14,VE=20,HE=200;function WE(...e){return e.filter(Boolean).join(" ")}var KE=e=>{var t,n,r,o,s,i,a,l,c,h;let{invert:f,toast:d,unstyled:y,interacting:w,setHeights:m,visibleToasts:b,heights:g,index:v,toasts:x,expanded:S,removeToast:C,defaultRichColors:P,closeButton:N,style:R,cancelButtonStyle:M,actionButtonStyle:_,className:L="",descriptionClassName:A="",duration:W,position:O,gap:G,loadingIcon:B,expandByDefault:K,classNames:k,icons:j,closeButtonAriaLabel:F="Close toast",pauseWhenPageIsHidden:D,cn:H}=e,[Z,ie]=I.useState(!1),[De,ee]=I.useState(!1),[Pe,Et]=I.useState(!1),[Re,Nt]=I.useState(!1),[fi,Lr]=I.useState(0),[sr,Qo]=I.useState(0),pi=I.useRef(null),gn=I.useRef(null),Al=v===0,Ol=v+1<=b,Me=d.type,Fr=d.dismissible!==!1,Kw=d.className||"",Qw=d.descriptionClassName||"",hi=I.useMemo(()=>g.findIndex(X=>X.toastId===d.id)||0,[g,d.id]),Gw=I.useMemo(()=>{var X;return(X=d.closeButton)!=null?X:N},[d.closeButton,N]),zf=I.useMemo(()=>d.duration||W||$E,[d.duration,W]),Dl=I.useRef(0),zr=I.useRef(0),$f=I.useRef(0),$r=I.useRef(null),[Uf,Yw]=O.split("-"),Bf=I.useMemo(()=>g.reduce((X,de,ue)=>ue>=hi?X:X+de.height,0),[g,hi]),Vf=ME(),Xw=d.invert||f,Ll=Me==="loading";zr.current=I.useMemo(()=>hi*G+Bf,[hi,Bf]),I.useEffect(()=>{ie(!0)},[]),I.useLayoutEffect(()=>{if(!Z)return;let X=gn.current,de=X.style.height;X.style.height="auto";let ue=X.getBoundingClientRect().height;X.style.height=de,Qo(ue),m(zt=>zt.find($t=>$t.toastId===d.id)?zt.map($t=>$t.toastId===d.id?{...$t,height:ue}:$t):[{toastId:d.id,height:ue,position:d.position},...zt])},[Z,d.title,d.description,m,d.id]);let vn=I.useCallback(()=>{ee(!0),Lr(zr.current),m(X=>X.filter(de=>de.toastId!==d.id)),setTimeout(()=>{C(d)},HE)},[d,C,m,zr]);I.useEffect(()=>{if(d.promise&&Me==="loading"||d.duration===1/0||d.type==="loading")return;let X,de=zf;return S||w||D&&Vf?(()=>{if($f.current<Dl.current){let ue=new Date().getTime()-Dl.current;de=de-ue}$f.current=new Date().getTime()})():de!==1/0&&(Dl.current=new Date().getTime(),X=setTimeout(()=>{var ue;(ue=d.onAutoClose)==null||ue.call(d,d),vn()},de)),()=>clearTimeout(X)},[S,w,K,d,zf,vn,d.promise,Me,D,Vf]),I.useEffect(()=>{let X=gn.current;if(X){let de=X.getBoundingClientRect().height;return Qo(de),m(ue=>[{toastId:d.id,height:de,position:d.position},...ue]),()=>m(ue=>ue.filter(zt=>zt.toastId!==d.id))}},[m,d.id]),I.useEffect(()=>{d.delete&&vn()},[vn,d.delete]);function qw(){return j!=null&&j.loading?I.createElement("div",{className:"sonner-loader","data-visible":Me==="loading"},j.loading):B?I.createElement("div",{className:"sonner-loader","data-visible":Me==="loading"},B):I.createElement(kE,{visible:Me==="loading"})}return I.createElement("li",{"aria-live":d.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:gn,className:H(L,Kw,k==null?void 0:k.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,k==null?void 0:k.default,k==null?void 0:k[Me],(n=d==null?void 0:d.classNames)==null?void 0:n[Me]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:P,"data-styled":!(d.jsx||d.unstyled||y),"data-mounted":Z,"data-promise":!!d.promise,"data-removed":De,"data-visible":Ol,"data-y-position":Uf,"data-x-position":Yw,"data-index":v,"data-front":Al,"data-swiping":Pe,"data-dismissible":Fr,"data-type":Me,"data-invert":Xw,"data-swipe-out":Re,"data-expanded":!!(S||K&&Z),style:{"--index":v,"--toasts-before":v,"--z-index":x.length-v,"--offset":`${De?fi:zr.current}px`,"--initial-height":K?"auto":`${sr}px`,...R,...d.style},onPointerDown:X=>{Ll||!Fr||(pi.current=new Date,Lr(zr.current),X.target.setPointerCapture(X.pointerId),X.target.tagName!=="BUTTON"&&(Et(!0),$r.current={x:X.clientX,y:X.clientY}))},onPointerUp:()=>{var X,de,ue,zt;if(Re||!Fr)return;$r.current=null;let $t=Number(((X=gn.current)==null?void 0:X.style.getPropertyValue("--swipe-amount").replace("px",""))||0),mi=new Date().getTime()-((de=pi.current)==null?void 0:de.getTime()),Zw=Math.abs($t)/mi;if(Math.abs($t)>=VE||Zw>.11){Lr(zr.current),(ue=d.onDismiss)==null||ue.call(d,d),vn(),Nt(!0);return}(zt=gn.current)==null||zt.style.setProperty("--swipe-amount","0px"),Et(!1)},onPointerMove:X=>{var de;if(!$r.current||!Fr)return;let ue=X.clientY-$r.current.y,zt=X.clientX-$r.current.x,$t=(Uf==="top"?Math.min:Math.max)(0,ue),mi=X.pointerType==="touch"?10:2;Math.abs($t)>mi?(de=gn.current)==null||de.style.setProperty("--swipe-amount",`${ue}px`):Math.abs(zt)>mi&&($r.current=null)}},Gw&&!d.jsx?I.createElement("button",{"aria-label":F,"data-disabled":Ll,"data-close-button":!0,onClick:Ll||!Fr?()=>{}:()=>{var X;vn(),(X=d.onDismiss)==null||X.call(d,d)},className:H(k==null?void 0:k.closeButton,(o=d==null?void 0:d.classNames)==null?void 0:o.closeButton)},I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},I.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),I.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,d.jsx||I.isValidElement(d.title)?d.jsx||d.title:I.createElement(I.Fragment,null,Me||d.icon||d.promise?I.createElement("div",{"data-icon":"",className:H(k==null?void 0:k.icon,(s=d==null?void 0:d.classNames)==null?void 0:s.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||qw():null,d.type!=="loading"?d.icon||(j==null?void 0:j[Me])||EE(Me):null):null,I.createElement("div",{"data-content":"",className:H(k==null?void 0:k.content,(i=d==null?void 0:d.classNames)==null?void 0:i.content)},I.createElement("div",{"data-title":"",className:H(k==null?void 0:k.title,(a=d==null?void 0:d.classNames)==null?void 0:a.title)},d.title),d.description?I.createElement("div",{"data-description":"",className:H(A,Qw,k==null?void 0:k.description,(l=d==null?void 0:d.classNames)==null?void 0:l.description)},d.description):null),I.isValidElement(d.cancel)?d.cancel:d.cancel&&Di(d.cancel)?I.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||M,onClick:X=>{var de,ue;Di(d.cancel)&&Fr&&((ue=(de=d.cancel).onClick)==null||ue.call(de,X),vn())},className:H(k==null?void 0:k.cancelButton,(c=d==null?void 0:d.classNames)==null?void 0:c.cancelButton)},d.cancel.label):null,I.isValidElement(d.action)?d.action:d.action&&Di(d.action)?I.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||_,onClick:X=>{var de,ue;Di(d.action)&&(X.defaultPrevented||((ue=(de=d.action).onClick)==null||ue.call(de,X),vn()))},className:H(k==null?void 0:k.actionButton,(h=d==null?void 0:d.classNames)==null?void 0:h.actionButton)},d.action.label):null))};function ch(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var QE=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:s,className:i,offset:a,theme:l="light",richColors:c,duration:h,style:f,visibleToasts:d=FE,toastOptions:y,dir:w=ch(),gap:m=BE,loadingIcon:b,icons:g,containerAriaLabel:v="Notifications",pauseWhenPageIsHidden:x,cn:S=WE}=e,[C,P]=I.useState([]),N=I.useMemo(()=>Array.from(new Set([n].concat(C.filter(D=>D.position).map(D=>D.position)))),[C,n]),[R,M]=I.useState([]),[_,L]=I.useState(!1),[A,W]=I.useState(!1),[O,G]=I.useState(l!=="system"?l:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),B=I.useRef(null),K=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),k=I.useRef(null),j=I.useRef(!1),F=I.useCallback(D=>{var H;(H=C.find(Z=>Z.id===D.id))!=null&&H.delete||rt.dismiss(D.id),P(Z=>Z.filter(({id:ie})=>ie!==D.id))},[C]);return I.useEffect(()=>rt.subscribe(D=>{if(D.dismiss){P(H=>H.map(Z=>Z.id===D.id?{...Z,delete:!0}:Z));return}setTimeout(()=>{wv.flushSync(()=>{P(H=>{let Z=H.findIndex(ie=>ie.id===D.id);return Z!==-1?[...H.slice(0,Z),{...H[Z],...D},...H.slice(Z+1)]:[D,...H]})})})}),[]),I.useEffect(()=>{if(l!=="system"){G(l);return}l==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?G("dark"):G("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:D})=>{G(D?"dark":"light")})},[l]),I.useEffect(()=>{C.length<=1&&L(!1)},[C]),I.useEffect(()=>{let D=H=>{var Z,ie;r.every(De=>H[De]||H.code===De)&&(L(!0),(Z=B.current)==null||Z.focus()),H.code==="Escape"&&(document.activeElement===B.current||(ie=B.current)!=null&&ie.contains(document.activeElement))&&L(!1)};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[r]),I.useEffect(()=>{if(B.current)return()=>{k.current&&(k.current.focus({preventScroll:!0}),k.current=null,j.current=!1)}},[B.current]),C.length?I.createElement("section",{"aria-label":`${v} ${K}`,tabIndex:-1},N.map((D,H)=>{var Z;let[ie,De]=D.split("-");return I.createElement("ol",{key:D,dir:w==="auto"?ch():w,tabIndex:-1,ref:B,className:i,"data-sonner-toaster":!0,"data-theme":O,"data-y-position":ie,"data-x-position":De,style:{"--front-toast-height":`${((Z=R[0])==null?void 0:Z.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||zE,"--width":`${UE}px`,"--gap":`${m}px`,...f},onBlur:ee=>{j.current&&!ee.currentTarget.contains(ee.relatedTarget)&&(j.current=!1,k.current&&(k.current.focus({preventScroll:!0}),k.current=null))},onFocus:ee=>{ee.target instanceof HTMLElement&&ee.target.dataset.dismissible==="false"||j.current||(j.current=!0,k.current=ee.relatedTarget)},onMouseEnter:()=>L(!0),onMouseMove:()=>L(!0),onMouseLeave:()=>{A||L(!1)},onPointerDown:ee=>{ee.target instanceof HTMLElement&&ee.target.dataset.dismissible==="false"||W(!0)},onPointerUp:()=>W(!1)},C.filter(ee=>!ee.position&&H===0||ee.position===D).map((ee,Pe)=>{var Et,Re;return I.createElement(KE,{key:ee.id,icons:g,index:Pe,toast:ee,defaultRichColors:c,duration:(Et=y==null?void 0:y.duration)!=null?Et:h,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:t,visibleToasts:d,closeButton:(Re=y==null?void 0:y.closeButton)!=null?Re:s,interacting:A,position:D,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:F,toasts:C.filter(Nt=>Nt.position==ee.position),heights:R.filter(Nt=>Nt.position==ee.position),setHeights:M,expandByDefault:o,gap:m,loadingIcon:b,expanded:_,pauseWhenPageIsHidden:x,cn:S})}))})):null};const GE=({...e})=>{const{theme:t="system"}=CE();return u.jsx(QE,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var YE=vm.useId||(()=>{}),XE=0;function Wn(e){const[t,n]=p.useState(YE());return Yn(()=>{e||n(r=>r??String(XE++))},[e]),e||(t?`radix-${t}`:"")}const qE=["top","right","bottom","left"],Xn=Math.min,st=Math.max,Oa=Math.round,Li=Math.floor,qn=e=>({x:e,y:e}),ZE={left:"right",right:"left",bottom:"top",top:"bottom"},JE={start:"end",end:"start"};function Pc(e,t,n){return st(e,Xn(t,n))}function pn(e,t){return typeof e=="function"?e(t):e}function hn(e){return e.split("-")[0]}function Vo(e){return e.split("-")[1]}function nf(e){return e==="x"?"y":"x"}function rf(e){return e==="y"?"height":"width"}function Zn(e){return["top","bottom"].includes(hn(e))?"y":"x"}function of(e){return nf(Zn(e))}function e2(e,t,n){n===void 0&&(n=!1);const r=Vo(e),o=of(e),s=rf(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Da(i)),[i,Da(i)]}function t2(e){const t=Da(e);return[Rc(e),t,Rc(t)]}function Rc(e){return e.replace(/start|end/g,t=>JE[t])}function n2(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function r2(e,t,n,r){const o=Vo(e);let s=n2(hn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Rc)))),s}function Da(e){return e.replace(/left|right|bottom|top/g,t=>ZE[t])}function o2(e){return{top:0,right:0,bottom:0,left:0,...e}}function ly(e){return typeof e!="number"?o2(e):{top:e,right:e,bottom:e,left:e}}function La(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function dh(e,t,n){let{reference:r,floating:o}=e;const s=Zn(t),i=of(t),a=rf(i),l=hn(t),c=s==="y",h=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,d=r[a]/2-o[a]/2;let y;switch(l){case"top":y={x:h,y:r.y-o.height};break;case"bottom":y={x:h,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:f};break;case"left":y={x:r.x-o.width,y:f};break;default:y={x:r.x,y:r.y}}switch(Vo(t)){case"start":y[i]-=d*(n&&c?-1:1);break;case"end":y[i]+=d*(n&&c?-1:1);break}return y}const s2=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:h,y:f}=dh(c,r,l),d=r,y={},w=0;for(let m=0;m<a.length;m++){const{name:b,fn:g}=a[m],{x:v,y:x,data:S,reset:C}=await g({x:h,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:y,rects:c,platform:i,elements:{reference:e,floating:t}});h=v??h,f=x??f,y={...y,[b]:{...y[b],...S}},C&&w<=50&&(w++,typeof C=="object"&&(C.placement&&(d=C.placement),C.rects&&(c=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:h,y:f}=dh(c,d,l)),m=-1)}return{x:h,y:f,placement:d,strategy:o,middlewareData:y}};async function zs(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:h="viewport",elementContext:f="floating",altBoundary:d=!1,padding:y=0}=pn(t,e),w=ly(y),b=a[d?f==="floating"?"reference":"floating":f],g=La(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(b)))==null||n?b:b.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:c,rootBoundary:h,strategy:l})),v=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),S=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},C=La(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:x,strategy:l}):v);return{top:(g.top-C.top+w.top)/S.y,bottom:(C.bottom-g.bottom+w.bottom)/S.y,left:(g.left-C.left+w.left)/S.x,right:(C.right-g.right+w.right)/S.x}}const i2=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:h=0}=pn(e,t)||{};if(c==null)return{};const f=ly(h),d={x:n,y:r},y=of(o),w=rf(y),m=await i.getDimensions(c),b=y==="y",g=b?"top":"left",v=b?"bottom":"right",x=b?"clientHeight":"clientWidth",S=s.reference[w]+s.reference[y]-d[y]-s.floating[w],C=d[y]-s.reference[y],P=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let N=P?P[x]:0;(!N||!await(i.isElement==null?void 0:i.isElement(P)))&&(N=a.floating[x]||s.floating[w]);const R=S/2-C/2,M=N/2-m[w]/2-1,_=Xn(f[g],M),L=Xn(f[v],M),A=_,W=N-m[w]-L,O=N/2-m[w]/2+R,G=Pc(A,O,W),B=!l.arrow&&Vo(o)!=null&&O!==G&&s.reference[w]/2-(O<A?_:L)-m[w]/2<0,K=B?O<A?O-A:O-W:0;return{[y]:d[y]+K,data:{[y]:G,centerOffset:O-G-K,...B&&{alignmentOffset:K}},reset:B}}}),a2=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:h=!0,crossAxis:f=!0,fallbackPlacements:d,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:m=!0,...b}=pn(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const g=hn(o),v=Zn(a),x=hn(a)===a,S=await(l.isRTL==null?void 0:l.isRTL(c.floating)),C=d||(x||!m?[Da(a)]:t2(a)),P=w!=="none";!d&&P&&C.push(...r2(a,m,w,S));const N=[a,...C],R=await zs(t,b),M=[];let _=((r=s.flip)==null?void 0:r.overflows)||[];if(h&&M.push(R[g]),f){const O=e2(o,i,S);M.push(R[O[0]],R[O[1]])}if(_=[..._,{placement:o,overflows:M}],!M.every(O=>O<=0)){var L,A;const O=(((L=s.flip)==null?void 0:L.index)||0)+1,G=N[O];if(G)return{data:{index:O,overflows:_},reset:{placement:G}};let B=(A=_.filter(K=>K.overflows[0]<=0).sort((K,k)=>K.overflows[1]-k.overflows[1])[0])==null?void 0:A.placement;if(!B)switch(y){case"bestFit":{var W;const K=(W=_.filter(k=>{if(P){const j=Zn(k.placement);return j===v||j==="y"}return!0}).map(k=>[k.placement,k.overflows.filter(j=>j>0).reduce((j,F)=>j+F,0)]).sort((k,j)=>k[1]-j[1])[0])==null?void 0:W[0];K&&(B=K);break}case"initialPlacement":B=a;break}if(o!==B)return{reset:{placement:B}}}return{}}}};function fh(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ph(e){return qE.some(t=>e[t]>=0)}const l2=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=pn(e,t);switch(r){case"referenceHidden":{const s=await zs(t,{...o,elementContext:"reference"}),i=fh(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:ph(i)}}}case"escaped":{const s=await zs(t,{...o,altBoundary:!0}),i=fh(s,n.floating);return{data:{escapedOffsets:i,escaped:ph(i)}}}default:return{}}}}};async function u2(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=hn(n),a=Vo(n),l=Zn(n)==="y",c=["left","top"].includes(i)?-1:1,h=s&&l?-1:1,f=pn(t,e);let{mainAxis:d,crossAxis:y,alignmentAxis:w}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof w=="number"&&(y=a==="end"?w*-1:w),l?{x:y*h,y:d*c}:{x:d*c,y:y*h}}const c2=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await u2(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},d2=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:b=>{let{x:g,y:v}=b;return{x:g,y:v}}},...l}=pn(e,t),c={x:n,y:r},h=await zs(t,l),f=Zn(hn(o)),d=nf(f);let y=c[d],w=c[f];if(s){const b=d==="y"?"top":"left",g=d==="y"?"bottom":"right",v=y+h[b],x=y-h[g];y=Pc(v,y,x)}if(i){const b=f==="y"?"top":"left",g=f==="y"?"bottom":"right",v=w+h[b],x=w-h[g];w=Pc(v,w,x)}const m=a.fn({...t,[d]:y,[f]:w});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:s,[f]:i}}}}}},f2=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=pn(e,t),h={x:n,y:r},f=Zn(o),d=nf(f);let y=h[d],w=h[f];const m=pn(a,t),b=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const x=d==="y"?"height":"width",S=s.reference[d]-s.floating[x]+b.mainAxis,C=s.reference[d]+s.reference[x]-b.mainAxis;y<S?y=S:y>C&&(y=C)}if(c){var g,v;const x=d==="y"?"width":"height",S=["top","left"].includes(hn(o)),C=s.reference[f]-s.floating[x]+(S&&((g=i.offset)==null?void 0:g[f])||0)+(S?0:b.crossAxis),P=s.reference[f]+s.reference[x]+(S?0:((v=i.offset)==null?void 0:v[f])||0)-(S?b.crossAxis:0);w<C?w=C:w>P&&(w=P)}return{[d]:y,[f]:w}}}},p2=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...c}=pn(e,t),h=await zs(t,c),f=hn(o),d=Vo(o),y=Zn(o)==="y",{width:w,height:m}=s.floating;let b,g;f==="top"||f==="bottom"?(b=f,g=d===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(g=f,b=d==="end"?"top":"bottom");const v=m-h.top-h.bottom,x=w-h.left-h.right,S=Xn(m-h[b],v),C=Xn(w-h[g],x),P=!t.middlewareData.shift;let N=S,R=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=x),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=v),P&&!d){const _=st(h.left,0),L=st(h.right,0),A=st(h.top,0),W=st(h.bottom,0);y?R=w-2*(_!==0||L!==0?_+L:st(h.left,h.right)):N=m-2*(A!==0||W!==0?A+W:st(h.top,h.bottom))}await l({...t,availableWidth:R,availableHeight:N});const M=await i.getDimensions(a.floating);return w!==M.width||m!==M.height?{reset:{rects:!0}}:{}}}};function gl(){return typeof window<"u"}function Ho(e){return uy(e)?(e.nodeName||"").toLowerCase():"#document"}function lt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Zt(e){var t;return(t=(uy(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function uy(e){return gl()?e instanceof Node||e instanceof lt(e).Node:!1}function Ot(e){return gl()?e instanceof Element||e instanceof lt(e).Element:!1}function qt(e){return gl()?e instanceof HTMLElement||e instanceof lt(e).HTMLElement:!1}function hh(e){return!gl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof lt(e).ShadowRoot}function ii(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Dt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function h2(e){return["table","td","th"].includes(Ho(e))}function vl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function sf(e){const t=af(),n=Ot(e)?Dt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function m2(e){let t=Jn(e);for(;qt(t)&&!Do(t);){if(sf(t))return t;if(vl(t))return null;t=Jn(t)}return null}function af(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Do(e){return["html","body","#document"].includes(Ho(e))}function Dt(e){return lt(e).getComputedStyle(e)}function yl(e){return Ot(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Jn(e){if(Ho(e)==="html")return e;const t=e.assignedSlot||e.parentNode||hh(e)&&e.host||Zt(e);return hh(t)?t.host:t}function cy(e){const t=Jn(e);return Do(t)?e.ownerDocument?e.ownerDocument.body:e.body:qt(t)&&ii(t)?t:cy(t)}function $s(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=cy(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=lt(o);if(s){const a=jc(i);return t.concat(i,i.visualViewport||[],ii(o)?o:[],a&&n?$s(a):[])}return t.concat(o,$s(o,[],n))}function jc(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function dy(e){const t=Dt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=qt(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=Oa(n)!==s||Oa(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function lf(e){return Ot(e)?e:e.contextElement}function fo(e){const t=lf(e);if(!qt(t))return qn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=dy(t);let i=(s?Oa(n.width):n.width)/r,a=(s?Oa(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const g2=qn(0);function fy(e){const t=lt(e);return!af()||!t.visualViewport?g2:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function v2(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==lt(e)?!1:t}function kr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=lf(e);let i=qn(1);t&&(r?Ot(r)&&(i=fo(r)):i=fo(e));const a=v2(s,n,r)?fy(s):qn(0);let l=(o.left+a.x)/i.x,c=(o.top+a.y)/i.y,h=o.width/i.x,f=o.height/i.y;if(s){const d=lt(s),y=r&&Ot(r)?lt(r):r;let w=d,m=jc(w);for(;m&&r&&y!==w;){const b=fo(m),g=m.getBoundingClientRect(),v=Dt(m),x=g.left+(m.clientLeft+parseFloat(v.paddingLeft))*b.x,S=g.top+(m.clientTop+parseFloat(v.paddingTop))*b.y;l*=b.x,c*=b.y,h*=b.x,f*=b.y,l+=x,c+=S,w=lt(m),m=jc(w)}}return La({width:h,height:f,x:l,y:c})}function y2(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Zt(r),a=t?vl(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},c=qn(1);const h=qn(0),f=qt(r);if((f||!f&&!s)&&((Ho(r)!=="body"||ii(i))&&(l=yl(r)),qt(r))){const d=kr(r);c=fo(r),h.x=d.x+r.clientLeft,h.y=d.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+h.x,y:n.y*c.y-l.scrollTop*c.y+h.y}}function x2(e){return Array.from(e.getClientRects())}function Tc(e,t){const n=yl(e).scrollLeft;return t?t.left+n:kr(Zt(e)).left+n}function w2(e){const t=Zt(e),n=yl(e),r=e.ownerDocument.body,o=st(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=st(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Tc(e);const a=-n.scrollTop;return Dt(r).direction==="rtl"&&(i+=st(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function b2(e,t){const n=lt(e),r=Zt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const c=af();(!c||c&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}function S2(e,t){const n=kr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=qt(e)?fo(e):qn(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,c=r*s.y;return{width:i,height:a,x:l,y:c}}function mh(e,t,n){let r;if(t==="viewport")r=b2(e,n);else if(t==="document")r=w2(Zt(e));else if(Ot(t))r=S2(t,n);else{const o=fy(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return La(r)}function py(e,t){const n=Jn(e);return n===t||!Ot(n)||Do(n)?!1:Dt(n).position==="fixed"||py(n,t)}function C2(e,t){const n=t.get(e);if(n)return n;let r=$s(e,[],!1).filter(a=>Ot(a)&&Ho(a)!=="body"),o=null;const s=Dt(e).position==="fixed";let i=s?Jn(e):e;for(;Ot(i)&&!Do(i);){const a=Dt(i),l=sf(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ii(i)&&!l&&py(e,i))?r=r.filter(h=>h!==i):o=a,i=Jn(i)}return t.set(e,r),r}function E2(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?vl(t)?[]:C2(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((c,h)=>{const f=mh(t,h,o);return c.top=st(f.top,c.top),c.right=Xn(f.right,c.right),c.bottom=Xn(f.bottom,c.bottom),c.left=st(f.left,c.left),c},mh(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function N2(e){const{width:t,height:n}=dy(e);return{width:t,height:n}}function k2(e,t,n){const r=qt(t),o=Zt(t),s=n==="fixed",i=kr(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=qn(0);if(r||!r&&!s)if((Ho(t)!=="body"||ii(o))&&(a=yl(t)),r){const y=kr(t,!0,s,t);l.x=y.x+t.clientLeft,l.y=y.y+t.clientTop}else o&&(l.x=Tc(o));let c=0,h=0;if(o&&!r&&!s){const y=o.getBoundingClientRect();h=y.top+a.scrollTop,c=y.left+a.scrollLeft-Tc(o,y)}const f=i.left+a.scrollLeft-l.x-c,d=i.top+a.scrollTop-l.y-h;return{x:f,y:d,width:i.width,height:i.height}}function mu(e){return Dt(e).position==="static"}function gh(e,t){if(!qt(e)||Dt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Zt(e)===n&&(n=n.ownerDocument.body),n}function hy(e,t){const n=lt(e);if(vl(e))return n;if(!qt(e)){let o=Jn(e);for(;o&&!Do(o);){if(Ot(o)&&!mu(o))return o;o=Jn(o)}return n}let r=gh(e,t);for(;r&&h2(r)&&mu(r);)r=gh(r,t);return r&&Do(r)&&mu(r)&&!sf(r)?n:r||m2(e)||n}const P2=async function(e){const t=this.getOffsetParent||hy,n=this.getDimensions,r=await n(e.floating);return{reference:k2(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function R2(e){return Dt(e).direction==="rtl"}const j2={convertOffsetParentRelativeRectToViewportRelativeRect:y2,getDocumentElement:Zt,getClippingRect:E2,getOffsetParent:hy,getElementRects:P2,getClientRects:x2,getDimensions:N2,getScale:fo,isElement:Ot,isRTL:R2};function T2(e,t){let n=null,r;const o=Zt(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const{left:c,top:h,width:f,height:d}=e.getBoundingClientRect();if(a||t(),!f||!d)return;const y=Li(h),w=Li(o.clientWidth-(c+f)),m=Li(o.clientHeight-(h+d)),b=Li(c),v={rootMargin:-y+"px "+-w+"px "+-m+"px "+-b+"px",threshold:st(0,Xn(1,l))||1};let x=!0;function S(C){const P=C[0].intersectionRatio;if(P!==l){if(!x)return i();P?i(!1,P):r=setTimeout(()=>{i(!1,1e-7)},1e3)}x=!1}try{n=new IntersectionObserver(S,{...v,root:o.ownerDocument})}catch{n=new IntersectionObserver(S,v)}n.observe(e)}return i(!0),s}function M2(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,c=lf(e),h=o||s?[...c?$s(c):[],...$s(t)]:[];h.forEach(g=>{o&&g.addEventListener("scroll",n,{passive:!0}),s&&g.addEventListener("resize",n)});const f=c&&a?T2(c,n):null;let d=-1,y=null;i&&(y=new ResizeObserver(g=>{let[v]=g;v&&v.target===c&&y&&(y.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var x;(x=y)==null||x.observe(t)})),n()}),c&&!l&&y.observe(c),y.observe(t));let w,m=l?kr(e):null;l&&b();function b(){const g=kr(e);m&&(g.x!==m.x||g.y!==m.y||g.width!==m.width||g.height!==m.height)&&n(),m=g,w=requestAnimationFrame(b)}return n(),()=>{var g;h.forEach(v=>{o&&v.removeEventListener("scroll",n),s&&v.removeEventListener("resize",n)}),f==null||f(),(g=y)==null||g.disconnect(),y=null,l&&cancelAnimationFrame(w)}}const _2=c2,I2=d2,A2=a2,O2=p2,D2=l2,vh=i2,L2=f2,F2=(e,t,n)=>{const r=new Map,o={platform:j2,...n},s={...o.platform,_c:r};return s2(e,t,{...o,platform:s})};var ra=typeof document<"u"?p.useLayoutEffect:p.useEffect;function Fa(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Fa(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Fa(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function my(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function yh(e,t){const n=my(e);return Math.round(t*n)/n}function gu(e){const t=p.useRef(e);return ra(()=>{t.current=e}),t}function z2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[h,f]=p.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,y]=p.useState(r);Fa(d,r)||y(r);const[w,m]=p.useState(null),[b,g]=p.useState(null),v=p.useCallback(k=>{k!==P.current&&(P.current=k,m(k))},[]),x=p.useCallback(k=>{k!==N.current&&(N.current=k,g(k))},[]),S=s||w,C=i||b,P=p.useRef(null),N=p.useRef(null),R=p.useRef(h),M=l!=null,_=gu(l),L=gu(o),A=gu(c),W=p.useCallback(()=>{if(!P.current||!N.current)return;const k={placement:t,strategy:n,middleware:d};L.current&&(k.platform=L.current),F2(P.current,N.current,k).then(j=>{const F={...j,isPositioned:A.current!==!1};O.current&&!Fa(R.current,F)&&(R.current=F,ni.flushSync(()=>{f(F)}))})},[d,t,n,L,A]);ra(()=>{c===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,f(k=>({...k,isPositioned:!1})))},[c]);const O=p.useRef(!1);ra(()=>(O.current=!0,()=>{O.current=!1}),[]),ra(()=>{if(S&&(P.current=S),C&&(N.current=C),S&&C){if(_.current)return _.current(S,C,W);W()}},[S,C,W,_,M]);const G=p.useMemo(()=>({reference:P,floating:N,setReference:v,setFloating:x}),[v,x]),B=p.useMemo(()=>({reference:S,floating:C}),[S,C]),K=p.useMemo(()=>{const k={position:n,left:0,top:0};if(!B.floating)return k;const j=yh(B.floating,h.x),F=yh(B.floating,h.y);return a?{...k,transform:"translate("+j+"px, "+F+"px)",...my(B.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:j,top:F}},[n,a,B.floating,h.x,h.y]);return p.useMemo(()=>({...h,update:W,refs:G,elements:B,floatingStyles:K}),[h,W,G,B,K])}const $2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?vh({element:r.current,padding:o}).fn(n):{}:r?vh({element:r,padding:o}).fn(n):{}}}},U2=(e,t)=>({..._2(e),options:[e,t]}),B2=(e,t)=>({...I2(e),options:[e,t]}),V2=(e,t)=>({...L2(e),options:[e,t]}),H2=(e,t)=>({...A2(e),options:[e,t]}),W2=(e,t)=>({...O2(e),options:[e,t]}),K2=(e,t)=>({...D2(e),options:[e,t]}),Q2=(e,t)=>({...$2(e),options:[e,t]});var G2="Arrow",gy=p.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return u.jsx(Y.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});gy.displayName=G2;var Y2=gy;function X2(e,t=[]){let n=[];function r(s,i){const a=p.createContext(i),l=n.length;n=[...n,i];function c(f){const{scope:d,children:y,...w}=f,m=(d==null?void 0:d[e][l])||a,b=p.useMemo(()=>w,Object.values(w));return u.jsx(m.Provider,{value:b,children:y})}function h(f,d){const y=(d==null?void 0:d[e][l])||a,w=p.useContext(y);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return c.displayName=s+"Provider",[c,h]}const o=()=>{const s=n.map(i=>p.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,q2(o,...t)]}function q2(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(s)[`__scope${c}`];return{...a,...f}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function uf(e){const[t,n]=p.useState(void 0);return Yn(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,c=Array.isArray(l)?l[0]:l;i=c.inlineSize,a=c.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var cf="Popper",[vy,xl]=X2(cf),[Z2,yy]=vy(cf),xy=e=>{const{__scopePopper:t,children:n}=e,[r,o]=p.useState(null);return u.jsx(Z2,{scope:t,anchor:r,onAnchorChange:o,children:n})};xy.displayName=cf;var wy="PopperAnchor",by=p.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=yy(wy,n),i=p.useRef(null),a=ae(t,i);return p.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:u.jsx(Y.div,{...o,ref:a})});by.displayName=wy;var df="PopperContent",[J2,eN]=vy(df),Sy=p.forwardRef((e,t)=>{var Pe,Et,Re,Nt,fi,Lr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:c=[],collisionPadding:h=0,sticky:f="partial",hideWhenDetached:d=!1,updatePositionStrategy:y="optimized",onPlaced:w,...m}=e,b=yy(df,n),[g,v]=p.useState(null),x=ae(t,sr=>v(sr)),[S,C]=p.useState(null),P=uf(S),N=(P==null?void 0:P.width)??0,R=(P==null?void 0:P.height)??0,M=r+(s!=="center"?"-"+s:""),_=typeof h=="number"?h:{top:0,right:0,bottom:0,left:0,...h},L=Array.isArray(c)?c:[c],A=L.length>0,W={padding:_,boundary:L.filter(nN),altBoundary:A},{refs:O,floatingStyles:G,placement:B,isPositioned:K,middlewareData:k}=z2({strategy:"fixed",placement:M,whileElementsMounted:(...sr)=>M2(...sr,{animationFrame:y==="always"}),elements:{reference:b.anchor},middleware:[U2({mainAxis:o+R,alignmentAxis:i}),l&&B2({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?V2():void 0,...W}),l&&H2({...W}),W2({...W,apply:({elements:sr,rects:Qo,availableWidth:pi,availableHeight:gn})=>{const{width:Al,height:Ol}=Qo.reference,Me=sr.floating.style;Me.setProperty("--radix-popper-available-width",`${pi}px`),Me.setProperty("--radix-popper-available-height",`${gn}px`),Me.setProperty("--radix-popper-anchor-width",`${Al}px`),Me.setProperty("--radix-popper-anchor-height",`${Ol}px`)}}),S&&Q2({element:S,padding:a}),rN({arrowWidth:N,arrowHeight:R}),d&&K2({strategy:"referenceHidden",...W})]}),[j,F]=Ny(B),D=Ke(w);Yn(()=>{K&&(D==null||D())},[K,D]);const H=(Pe=k.arrow)==null?void 0:Pe.x,Z=(Et=k.arrow)==null?void 0:Et.y,ie=((Re=k.arrow)==null?void 0:Re.centerOffset)!==0,[De,ee]=p.useState();return Yn(()=>{g&&ee(window.getComputedStyle(g).zIndex)},[g]),u.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:K?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:De,"--radix-popper-transform-origin":[(Nt=k.transformOrigin)==null?void 0:Nt.x,(fi=k.transformOrigin)==null?void 0:fi.y].join(" "),...((Lr=k.hide)==null?void 0:Lr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:u.jsx(J2,{scope:n,placedSide:j,onArrowChange:C,arrowX:H,arrowY:Z,shouldHideArrow:ie,children:u.jsx(Y.div,{"data-side":j,"data-align":F,...m,ref:x,style:{...m.style,animation:K?void 0:"none"}})})})});Sy.displayName=df;var Cy="PopperArrow",tN={top:"bottom",right:"left",bottom:"top",left:"right"},Ey=p.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=eN(Cy,r),i=tN[s.placedSide];return u.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:u.jsx(Y2,{...o,ref:n,style:{...o.style,display:"block"}})})});Ey.displayName=Cy;function nN(e){return e!==null}var rN=e=>({name:"transformOrigin",options:e,fn(t){var b,g,v;const{placement:n,rects:r,middlewareData:o}=t,i=((b=o.arrow)==null?void 0:b.centerOffset)!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,h]=Ny(n),f={start:"0%",center:"50%",end:"100%"}[h],d=(((g=o.arrow)==null?void 0:g.x)??0)+a/2,y=(((v=o.arrow)==null?void 0:v.y)??0)+l/2;let w="",m="";return c==="bottom"?(w=i?f:`${d}px`,m=`${-l}px`):c==="top"?(w=i?f:`${d}px`,m=`${r.floating.height+l}px`):c==="right"?(w=`${-l}px`,m=i?f:`${y}px`):c==="left"&&(w=`${r.floating.width+l}px`,m=i?f:`${y}px`),{data:{x:w,y:m}}}});function Ny(e){const[t,n="center"]=e.split("-");return[t,n]}var ky=xy,Py=by,Ry=Sy,jy=Ey,[wl,xM]=rr("Tooltip",[xl]),bl=xl(),Ty="TooltipProvider",oN=700,Mc="tooltip.open",[sN,ff]=wl(Ty),My=e=>{const{__scopeTooltip:t,delayDuration:n=oN,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,a]=p.useState(!0),l=p.useRef(!1),c=p.useRef(0);return p.useEffect(()=>{const h=c.current;return()=>window.clearTimeout(h)},[]),u.jsx(sN,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:p.useCallback(()=>{window.clearTimeout(c.current),a(!1)},[]),onClose:p.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:p.useCallback(h=>{l.current=h},[]),disableHoverableContent:o,children:s})};My.displayName=Ty;var Sl="Tooltip",[iN,Cl]=wl(Sl),_y=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:i,delayDuration:a}=e,l=ff(Sl,e.__scopeTooltip),c=bl(t),[h,f]=p.useState(null),d=Wn(),y=p.useRef(0),w=i??l.disableHoverableContent,m=a??l.delayDuration,b=p.useRef(!1),[g=!1,v]=or({prop:r,defaultProp:o,onChange:N=>{N?(l.onOpen(),document.dispatchEvent(new CustomEvent(Mc))):l.onClose(),s==null||s(N)}}),x=p.useMemo(()=>g?b.current?"delayed-open":"instant-open":"closed",[g]),S=p.useCallback(()=>{window.clearTimeout(y.current),y.current=0,b.current=!1,v(!0)},[v]),C=p.useCallback(()=>{window.clearTimeout(y.current),y.current=0,v(!1)},[v]),P=p.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{b.current=!0,v(!0),y.current=0},m)},[m,v]);return p.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),u.jsx(ky,{...c,children:u.jsx(iN,{scope:t,contentId:d,open:g,stateAttribute:x,trigger:h,onTriggerChange:f,onTriggerEnter:p.useCallback(()=>{l.isOpenDelayed?P():S()},[l.isOpenDelayed,P,S]),onTriggerLeave:p.useCallback(()=>{w?C():(window.clearTimeout(y.current),y.current=0)},[C,w]),onOpen:S,onClose:C,disableHoverableContent:w,children:n})})};_y.displayName=Sl;var _c="TooltipTrigger",Iy=p.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Cl(_c,n),s=ff(_c,n),i=bl(n),a=p.useRef(null),l=ae(t,a,o.onTriggerChange),c=p.useRef(!1),h=p.useRef(!1),f=p.useCallback(()=>c.current=!1,[]);return p.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),u.jsx(Py,{asChild:!0,...i,children:u.jsx(Y.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:$(e.onPointerMove,d=>{d.pointerType!=="touch"&&!h.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),h.current=!0)}),onPointerLeave:$(e.onPointerLeave,()=>{o.onTriggerLeave(),h.current=!1}),onPointerDown:$(e.onPointerDown,()=>{c.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:$(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:$(e.onBlur,o.onClose),onClick:$(e.onClick,o.onClose)})})});Iy.displayName=_c;var aN="TooltipPortal",[wM,lN]=wl(aN,{forceMount:void 0}),Lo="TooltipContent",Ay=p.forwardRef((e,t)=>{const n=lN(Lo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=Cl(Lo,e.__scopeTooltip);return u.jsx(Lt,{present:r||i.open,children:i.disableHoverableContent?u.jsx(Oy,{side:o,...s,ref:t}):u.jsx(uN,{side:o,...s,ref:t})})}),uN=p.forwardRef((e,t)=>{const n=Cl(Lo,e.__scopeTooltip),r=ff(Lo,e.__scopeTooltip),o=p.useRef(null),s=ae(t,o),[i,a]=p.useState(null),{trigger:l,onClose:c}=n,h=o.current,{onPointerInTransitChange:f}=r,d=p.useCallback(()=>{a(null),f(!1)},[f]),y=p.useCallback((w,m)=>{const b=w.currentTarget,g={x:w.clientX,y:w.clientY},v=pN(g,b.getBoundingClientRect()),x=hN(g,v),S=mN(m.getBoundingClientRect()),C=vN([...x,...S]);a(C),f(!0)},[f]);return p.useEffect(()=>()=>d(),[d]),p.useEffect(()=>{if(l&&h){const w=b=>y(b,h),m=b=>y(b,l);return l.addEventListener("pointerleave",w),h.addEventListener("pointerleave",m),()=>{l.removeEventListener("pointerleave",w),h.removeEventListener("pointerleave",m)}}},[l,h,y,d]),p.useEffect(()=>{if(i){const w=m=>{const b=m.target,g={x:m.clientX,y:m.clientY},v=(l==null?void 0:l.contains(b))||(h==null?void 0:h.contains(b)),x=!gN(g,i);v?d():x&&(d(),c())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[l,h,i,c,d]),u.jsx(Oy,{...e,ref:s})}),[cN,dN]=wl(Sl,{isInside:!1}),Oy=p.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=Cl(Lo,n),c=bl(n),{onClose:h}=l;return p.useEffect(()=>(document.addEventListener(Mc,h),()=>document.removeEventListener(Mc,h)),[h]),p.useEffect(()=>{if(l.trigger){const f=d=>{const y=d.target;y!=null&&y.contains(l.trigger)&&h()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,h]),u.jsx(ri,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:h,children:u.jsxs(Ry,{"data-state":l.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[u.jsx(Sv,{children:r}),u.jsx(cN,{scope:n,isInside:!0,children:u.jsx(tC,{id:l.contentId,role:"tooltip",children:o||r})})]})})});Ay.displayName=Lo;var Dy="TooltipArrow",fN=p.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=bl(n);return dN(Dy,n).isInside?null:u.jsx(jy,{...o,...r,ref:t})});fN.displayName=Dy;function pN(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function hN(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function mN(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function gN(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,c=t[i].x,h=t[i].y;l>r!=h>r&&n<(c-a)*(r-l)/(h-l)+a&&(o=!o)}return o}function vN(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),yN(t)}function yN(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var xN=My,wN=_y,bN=Iy,Ly=Ay;const Fy=xN,SN=wN,CN=bN,zy=p.forwardRef(({className:e,sideOffset:t=4,...n},r)=>u.jsx(Ly,{ref:r,sideOffset:t,className:V("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));zy.displayName=Ly.displayName;var ai=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Pr=typeof window>"u"||"Deno"in globalThis;function pt(){}function EN(e,t){return typeof e=="function"?e(t):e}function Ic(e){return typeof e=="number"&&e>=0&&e!==1/0}function $y(e,t){return Math.max(e+(t||0)-Date.now(),0)}function po(e,t){return typeof e=="function"?e(t):e}function Mt(e,t){return typeof e=="function"?e(t):e}function xh(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:a}=e;if(i){if(r){if(t.queryHash!==pf(i,t.options))return!1}else if(!Bs(t.queryKey,i))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||s&&!s(t))}function wh(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(Us(t.options.mutationKey)!==Us(s))return!1}else if(!Bs(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function pf(e,t){return((t==null?void 0:t.queryKeyHashFn)||Us)(e)}function Us(e){return JSON.stringify(e,(t,n)=>Oc(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Bs(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Bs(e[n],t[n])):!1}function Uy(e,t){if(e===t)return e;const n=bh(e)&&bh(t);if(n||Oc(e)&&Oc(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,a=n?[]:{};let l=0;for(let c=0;c<i;c++){const h=n?c:s[c];(!n&&r.includes(h)||n)&&e[h]===void 0&&t[h]===void 0?(a[h]=void 0,l++):(a[h]=Uy(e[h],t[h]),a[h]===e[h]&&e[h]!==void 0&&l++)}return o===i&&l===o?e:a}return t}function Ac(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function bh(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Oc(e){if(!Sh(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Sh(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Sh(e){return Object.prototype.toString.call(e)==="[object Object]"}function NN(e){return new Promise(t=>{setTimeout(t,e)})}function Dc(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Uy(e,t):t}function kN(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function PN(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var hf=Symbol();function By(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===hf?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var dr,Pn,go,qh,RN=(qh=class extends ai{constructor(){super();Q(this,dr);Q(this,Pn);Q(this,go);U(this,go,t=>{if(!Pr&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){E(this,Pn)||this.setEventListener(E(this,go))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,Pn))==null||t.call(this),U(this,Pn,void 0))}setEventListener(t){var n;U(this,go,t),(n=E(this,Pn))==null||n.call(this),U(this,Pn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){E(this,dr)!==t&&(U(this,dr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof E(this,dr)=="boolean"?E(this,dr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},dr=new WeakMap,Pn=new WeakMap,go=new WeakMap,qh),mf=new RN,vo,Rn,yo,Zh,jN=(Zh=class extends ai{constructor(){super();Q(this,vo,!0);Q(this,Rn);Q(this,yo);U(this,yo,t=>{if(!Pr&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){E(this,Rn)||this.setEventListener(E(this,yo))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,Rn))==null||t.call(this),U(this,Rn,void 0))}setEventListener(t){var n;U(this,yo,t),(n=E(this,Rn))==null||n.call(this),U(this,Rn,t(this.setOnline.bind(this)))}setOnline(t){E(this,vo)!==t&&(U(this,vo,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return E(this,vo)}},vo=new WeakMap,Rn=new WeakMap,yo=new WeakMap,Zh),za=new jN;function Lc(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function TN(e){return Math.min(1e3*2**e,3e4)}function Vy(e){return(e??"online")==="online"?za.isOnline():!0}var Hy=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function vu(e){return e instanceof Hy}function Wy(e){let t=!1,n=0,r=!1,o;const s=Lc(),i=m=>{var b;r||(d(new Hy(m)),(b=e.abort)==null||b.call(e))},a=()=>{t=!0},l=()=>{t=!1},c=()=>mf.isFocused()&&(e.networkMode==="always"||za.isOnline())&&e.canRun(),h=()=>Vy(e.networkMode)&&e.canRun(),f=m=>{var b;r||(r=!0,(b=e.onSuccess)==null||b.call(e,m),o==null||o(),s.resolve(m))},d=m=>{var b;r||(r=!0,(b=e.onError)==null||b.call(e,m),o==null||o(),s.reject(m))},y=()=>new Promise(m=>{var b;o=g=>{(r||c())&&m(g)},(b=e.onPause)==null||b.call(e)}).then(()=>{var m;o=void 0,r||(m=e.onContinue)==null||m.call(e)}),w=()=>{if(r)return;let m;const b=n===0?e.initialPromise:void 0;try{m=b??e.fn()}catch(g){m=Promise.reject(g)}Promise.resolve(m).then(f).catch(g=>{var P;if(r)return;const v=e.retry??(Pr?0:3),x=e.retryDelay??TN,S=typeof x=="function"?x(n,g):x,C=v===!0||typeof v=="number"&&n<v||typeof v=="function"&&v(n,g);if(t||!C){d(g);return}n++,(P=e.onFail)==null||P.call(e,n,g),NN(S).then(()=>c()?void 0:y()).then(()=>{t?d(g):w()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:a,continueRetry:l,canStart:h,start:()=>(h()?w():y().then(w),s)}}function MN(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const s=a=>{t?e.push(a):o(()=>{n(a)})},i=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||i()}return l},batchCalls:a=>(...l)=>{s(()=>{a(...l)})},schedule:s,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var Ie=MN(),fr,Jh,Ky=(Jh=class{constructor(){Q(this,fr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Ic(this.gcTime)&&U(this,fr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Pr?1/0:5*60*1e3))}clearGcTimeout(){E(this,fr)&&(clearTimeout(E(this,fr)),U(this,fr,void 0))}},fr=new WeakMap,Jh),xo,wo,ft,ze,Qs,pr,Rt,en,em,_N=(em=class extends Ky{constructor(t){super();Q(this,Rt);Q(this,xo);Q(this,wo);Q(this,ft);Q(this,ze);Q(this,Qs);Q(this,pr);U(this,pr,!1),U(this,Qs,t.defaultOptions),this.setOptions(t.options),this.observers=[],U(this,ft,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,U(this,xo,IN(this.options)),this.state=t.state??E(this,xo),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=E(this,ze))==null?void 0:t.promise}setOptions(t){this.options={...E(this,Qs),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&E(this,ft).remove(this)}setData(t,n){const r=Dc(this.state.data,t,this.options);return te(this,Rt,en).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){te(this,Rt,en).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=E(this,ze))==null?void 0:r.promise;return(o=E(this,ze))==null||o.cancel(t),n?n.then(pt).catch(pt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(E(this,xo))}isActive(){return this.observers.some(t=>Mt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===hf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!$y(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,ze))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,ze))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),E(this,ft).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(E(this,ze)&&(E(this,pr)?E(this,ze).cancel({revert:!0}):E(this,ze).cancelRetry()),this.scheduleGc()),E(this,ft).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||te(this,Rt,en).call(this,{type:"invalidate"})}fetch(t,n){var l,c,h;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(E(this,ze))return E(this,ze).continueRetry(),E(this,ze).promise}if(t&&this.setOptions(t),!this.options.queryFn){const f=this.observers.find(d=>d.options.queryFn);f&&this.setOptions(f.options)}const r=new AbortController,o=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(U(this,pr,!0),r.signal)})},s=()=>{const f=By(this.options,n),d={queryKey:this.queryKey,meta:this.meta};return o(d),U(this,pr,!1),this.options.persister?this.options.persister(f,d,this):f(d)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};o(i),(l=this.options.behavior)==null||l.onFetch(i,this),U(this,wo,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&te(this,Rt,en).call(this,{type:"fetch",meta:(h=i.fetchOptions)==null?void 0:h.meta});const a=f=>{var d,y,w,m;vu(f)&&f.silent||te(this,Rt,en).call(this,{type:"error",error:f}),vu(f)||((y=(d=E(this,ft).config).onError)==null||y.call(d,f,this),(m=(w=E(this,ft).config).onSettled)==null||m.call(w,this.state.data,f,this)),this.scheduleGc()};return U(this,ze,Wy({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:f=>{var d,y,w,m;if(f===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(b){a(b);return}(y=(d=E(this,ft).config).onSuccess)==null||y.call(d,f,this),(m=(w=E(this,ft).config).onSettled)==null||m.call(w,f,this.state.error,this),this.scheduleGc()},onError:a,onFail:(f,d)=>{te(this,Rt,en).call(this,{type:"failed",failureCount:f,error:d})},onPause:()=>{te(this,Rt,en).call(this,{type:"pause"})},onContinue:()=>{te(this,Rt,en).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),E(this,ze).start()}},xo=new WeakMap,wo=new WeakMap,ft=new WeakMap,ze=new WeakMap,Qs=new WeakMap,pr=new WeakMap,Rt=new WeakSet,en=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Qy(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return vu(o)&&o.revert&&E(this,wo)?{...E(this,wo),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Ie.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),E(this,ft).notify({query:this,type:"updated",action:t})})},em);function Qy(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Vy(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function IN(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Vt,tm,AN=(tm=class extends ai{constructor(t={}){super();Q(this,Vt);this.config=t,U(this,Vt,new Map)}build(t,n,r){const o=n.queryKey,s=n.queryHash??pf(o,n);let i=this.get(s);return i||(i=new _N({cache:this,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){E(this,Vt).has(t.queryHash)||(E(this,Vt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=E(this,Vt).get(t.queryHash);n&&(t.destroy(),n===t&&E(this,Vt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ie.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return E(this,Vt).get(t)}getAll(){return[...E(this,Vt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>xh(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>xh(t,r)):n}notify(t){Ie.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Ie.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ie.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Vt=new WeakMap,tm),Ht,Be,hr,Wt,bn,nm,ON=(nm=class extends Ky{constructor(t){super();Q(this,Wt);Q(this,Ht);Q(this,Be);Q(this,hr);this.mutationId=t.mutationId,U(this,Be,t.mutationCache),U(this,Ht,[]),this.state=t.state||DN(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){E(this,Ht).includes(t)||(E(this,Ht).push(t),this.clearGcTimeout(),E(this,Be).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){U(this,Ht,E(this,Ht).filter(n=>n!==t)),this.scheduleGc(),E(this,Be).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){E(this,Ht).length||(this.state.status==="pending"?this.scheduleGc():E(this,Be).remove(this))}continue(){var t;return((t=E(this,hr))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,s,i,a,l,c,h,f,d,y,w,m,b,g,v,x,S,C,P,N;U(this,hr,Wy({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(R,M)=>{te(this,Wt,bn).call(this,{type:"failed",failureCount:R,error:M})},onPause:()=>{te(this,Wt,bn).call(this,{type:"pause"})},onContinue:()=>{te(this,Wt,bn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>E(this,Be).canRun(this)}));const n=this.state.status==="pending",r=!E(this,hr).canStart();try{if(!n){te(this,Wt,bn).call(this,{type:"pending",variables:t,isPaused:r}),await((s=(o=E(this,Be).config).onMutate)==null?void 0:s.call(o,t,this));const M=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,t));M!==this.state.context&&te(this,Wt,bn).call(this,{type:"pending",context:M,variables:t,isPaused:r})}const R=await E(this,hr).start();return await((c=(l=E(this,Be).config).onSuccess)==null?void 0:c.call(l,R,t,this.state.context,this)),await((f=(h=this.options).onSuccess)==null?void 0:f.call(h,R,t,this.state.context)),await((y=(d=E(this,Be).config).onSettled)==null?void 0:y.call(d,R,null,this.state.variables,this.state.context,this)),await((m=(w=this.options).onSettled)==null?void 0:m.call(w,R,null,t,this.state.context)),te(this,Wt,bn).call(this,{type:"success",data:R}),R}catch(R){try{throw await((g=(b=E(this,Be).config).onError)==null?void 0:g.call(b,R,t,this.state.context,this)),await((x=(v=this.options).onError)==null?void 0:x.call(v,R,t,this.state.context)),await((C=(S=E(this,Be).config).onSettled)==null?void 0:C.call(S,void 0,R,this.state.variables,this.state.context,this)),await((N=(P=this.options).onSettled)==null?void 0:N.call(P,void 0,R,t,this.state.context)),R}finally{te(this,Wt,bn).call(this,{type:"error",error:R})}}finally{E(this,Be).runNext(this)}}},Ht=new WeakMap,Be=new WeakMap,hr=new WeakMap,Wt=new WeakSet,bn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Ie.batch(()=>{E(this,Ht).forEach(r=>{r.onMutationUpdate(t)}),E(this,Be).notify({mutation:this,type:"updated",action:t})})},nm);function DN(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var nt,Gs,rm,LN=(rm=class extends ai{constructor(t={}){super();Q(this,nt);Q(this,Gs);this.config=t,U(this,nt,new Map),U(this,Gs,Date.now())}build(t,n,r){const o=new ON({mutationCache:this,mutationId:++gi(this,Gs)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=Fi(t),r=E(this,nt).get(n)??[];r.push(t),E(this,nt).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Fi(t);if(E(this,nt).has(n)){const o=(r=E(this,nt).get(n))==null?void 0:r.filter(s=>s!==t);o&&(o.length===0?E(this,nt).delete(n):E(this,nt).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=E(this,nt).get(Fi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=E(this,nt).get(Fi(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Ie.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...E(this,nt).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>wh(n,r))}findAll(t={}){return this.getAll().filter(n=>wh(t,n))}notify(t){Ie.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Ie.batch(()=>Promise.all(t.map(n=>n.continue().catch(pt))))}},nt=new WeakMap,Gs=new WeakMap,rm);function Fi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Ch(e){return{onFetch:(t,n)=>{var h,f,d,y,w;const r=t.options,o=(d=(f=(h=t.fetchOptions)==null?void 0:h.meta)==null?void 0:f.fetchMore)==null?void 0:d.direction,s=((y=t.state.data)==null?void 0:y.pages)||[],i=((w=t.state.data)==null?void 0:w.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const c=async()=>{let m=!1;const b=x=>{Object.defineProperty(x,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},g=By(t.options,t.fetchOptions),v=async(x,S,C)=>{if(m)return Promise.reject();if(S==null&&x.pages.length)return Promise.resolve(x);const P={queryKey:t.queryKey,pageParam:S,direction:C?"backward":"forward",meta:t.options.meta};b(P);const N=await g(P),{maxPages:R}=t.options,M=C?PN:kN;return{pages:M(x.pages,N,R),pageParams:M(x.pageParams,S,R)}};if(o&&s.length){const x=o==="backward",S=x?FN:Eh,C={pages:s,pageParams:i},P=S(r,C);a=await v(C,P,x)}else{const x=e??s.length;do{const S=l===0?i[0]??r.initialPageParam:Eh(r,a);if(l>0&&S==null)break;a=await v(a,S),l++}while(l<x)}return a};t.options.persister?t.fetchFn=()=>{var m,b;return(b=(m=t.options).persister)==null?void 0:b.call(m,c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=c}}}function Eh(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function FN(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var we,jn,Tn,bo,So,Mn,Co,Eo,om,zN=(om=class{constructor(e={}){Q(this,we);Q(this,jn);Q(this,Tn);Q(this,bo);Q(this,So);Q(this,Mn);Q(this,Co);Q(this,Eo);U(this,we,e.queryCache||new AN),U(this,jn,e.mutationCache||new LN),U(this,Tn,e.defaultOptions||{}),U(this,bo,new Map),U(this,So,new Map),U(this,Mn,0)}mount(){gi(this,Mn)._++,E(this,Mn)===1&&(U(this,Co,mf.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,we).onFocus())})),U(this,Eo,za.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,we).onOnline())})))}unmount(){var e,t;gi(this,Mn)._--,E(this,Mn)===0&&((e=E(this,Co))==null||e.call(this),U(this,Co,void 0),(t=E(this,Eo))==null||t.call(this),U(this,Eo,void 0))}isFetching(e){return E(this,we).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return E(this,jn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,we).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=E(this,we).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(po(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return E(this,we).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=E(this,we).get(r.queryHash),s=o==null?void 0:o.state.data,i=EN(t,s);if(i!==void 0)return E(this,we).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Ie.batch(()=>E(this,we).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,we).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=E(this,we);Ie.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=E(this,we),r={type:"active",...e};return Ie.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Ie.batch(()=>E(this,we).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(pt).catch(pt)}invalidateQueries(e={},t={}){return Ie.batch(()=>{if(E(this,we).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Ie.batch(()=>E(this,we).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let s=o.fetch(void 0,n);return n.throwOnError||(s=s.catch(pt)),o.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(pt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=E(this,we).build(this,t);return n.isStaleByTime(po(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(pt).catch(pt)}fetchInfiniteQuery(e){return e.behavior=Ch(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(pt).catch(pt)}ensureInfiniteQueryData(e){return e.behavior=Ch(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return za.isOnline()?E(this,jn).resumePausedMutations():Promise.resolve()}getQueryCache(){return E(this,we)}getMutationCache(){return E(this,jn)}getDefaultOptions(){return E(this,Tn)}setDefaultOptions(e){U(this,Tn,e)}setQueryDefaults(e,t){E(this,bo).set(Us(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...E(this,bo).values()];let n={};return t.forEach(r=>{Bs(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){E(this,So).set(Us(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...E(this,So).values()];let n={};return t.forEach(r=>{Bs(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...E(this,Tn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=pf(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===hf&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...E(this,Tn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){E(this,we).clear(),E(this,jn).clear()}},we=new WeakMap,jn=new WeakMap,Tn=new WeakMap,bo=new WeakMap,So=new WeakMap,Mn=new WeakMap,Co=new WeakMap,Eo=new WeakMap,om),Xe,ne,Ys,Ve,mr,No,_n,Kt,Xs,ko,Po,gr,vr,In,Ro,oe,ls,Fc,zc,$c,Uc,Bc,Vc,Hc,Gy,sm,$N=(sm=class extends ai{constructor(t,n){super();Q(this,oe);Q(this,Xe);Q(this,ne);Q(this,Ys);Q(this,Ve);Q(this,mr);Q(this,No);Q(this,_n);Q(this,Kt);Q(this,Xs);Q(this,ko);Q(this,Po);Q(this,gr);Q(this,vr);Q(this,In);Q(this,Ro,new Set);this.options=n,U(this,Xe,t),U(this,Kt,null),U(this,_n,Lc()),this.options.experimental_prefetchInRender||E(this,_n).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(E(this,ne).addObserver(this),Nh(E(this,ne),this.options)?te(this,oe,ls).call(this):this.updateResult(),te(this,oe,Uc).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Wc(E(this,ne),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Wc(E(this,ne),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,te(this,oe,Bc).call(this),te(this,oe,Vc).call(this),E(this,ne).removeObserver(this)}setOptions(t,n){const r=this.options,o=E(this,ne);if(this.options=E(this,Xe).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Mt(this.options.enabled,E(this,ne))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");te(this,oe,Hc).call(this),E(this,ne).setOptions(this.options),r._defaulted&&!Ac(this.options,r)&&E(this,Xe).getQueryCache().notify({type:"observerOptionsUpdated",query:E(this,ne),observer:this});const s=this.hasListeners();s&&kh(E(this,ne),o,this.options,r)&&te(this,oe,ls).call(this),this.updateResult(n),s&&(E(this,ne)!==o||Mt(this.options.enabled,E(this,ne))!==Mt(r.enabled,E(this,ne))||po(this.options.staleTime,E(this,ne))!==po(r.staleTime,E(this,ne)))&&te(this,oe,Fc).call(this);const i=te(this,oe,zc).call(this);s&&(E(this,ne)!==o||Mt(this.options.enabled,E(this,ne))!==Mt(r.enabled,E(this,ne))||i!==E(this,In))&&te(this,oe,$c).call(this,i)}getOptimisticResult(t){const n=E(this,Xe).getQueryCache().build(E(this,Xe),t),r=this.createResult(n,t);return BN(this,r)&&(U(this,Ve,r),U(this,No,this.options),U(this,mr,E(this,ne).state)),r}getCurrentResult(){return E(this,Ve)}trackResult(t,n){const r={};return Object.keys(t).forEach(o=>{Object.defineProperty(r,o,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(o),n==null||n(o),t[o])})}),r}trackProp(t){E(this,Ro).add(t)}getCurrentQuery(){return E(this,ne)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=E(this,Xe).defaultQueryOptions(t),r=E(this,Xe).getQueryCache().build(E(this,Xe),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return te(this,oe,ls).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),E(this,Ve)))}createResult(t,n){var R;const r=E(this,ne),o=this.options,s=E(this,Ve),i=E(this,mr),a=E(this,No),c=t!==r?t.state:E(this,Ys),{state:h}=t;let f={...h},d=!1,y;if(n._optimisticResults){const M=this.hasListeners(),_=!M&&Nh(t,n),L=M&&kh(t,r,n,o);(_||L)&&(f={...f,...Qy(h.data,t.options)}),n._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:w,errorUpdatedAt:m,status:b}=f;if(n.select&&f.data!==void 0)if(s&&f.data===(i==null?void 0:i.data)&&n.select===E(this,Xs))y=E(this,ko);else try{U(this,Xs,n.select),y=n.select(f.data),y=Dc(s==null?void 0:s.data,y,n),U(this,ko,y),U(this,Kt,null)}catch(M){U(this,Kt,M)}else y=f.data;if(n.placeholderData!==void 0&&y===void 0&&b==="pending"){let M;if(s!=null&&s.isPlaceholderData&&n.placeholderData===(a==null?void 0:a.placeholderData))M=s.data;else if(M=typeof n.placeholderData=="function"?n.placeholderData((R=E(this,Po))==null?void 0:R.state.data,E(this,Po)):n.placeholderData,n.select&&M!==void 0)try{M=n.select(M),U(this,Kt,null)}catch(_){U(this,Kt,_)}M!==void 0&&(b="success",y=Dc(s==null?void 0:s.data,M,n),d=!0)}E(this,Kt)&&(w=E(this,Kt),y=E(this,ko),m=Date.now(),b="error");const g=f.fetchStatus==="fetching",v=b==="pending",x=b==="error",S=v&&g,C=y!==void 0,N={status:b,fetchStatus:f.fetchStatus,isPending:v,isSuccess:b==="success",isError:x,isInitialLoading:S,isLoading:S,data:y,dataUpdatedAt:f.dataUpdatedAt,error:w,errorUpdatedAt:m,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>c.dataUpdateCount||f.errorUpdateCount>c.errorUpdateCount,isFetching:g,isRefetching:g&&!v,isLoadingError:x&&!C,isPaused:f.fetchStatus==="paused",isPlaceholderData:d,isRefetchError:x&&C,isStale:gf(t,n),refetch:this.refetch,promise:E(this,_n)};if(this.options.experimental_prefetchInRender){const M=A=>{N.status==="error"?A.reject(N.error):N.data!==void 0&&A.resolve(N.data)},_=()=>{const A=U(this,_n,N.promise=Lc());M(A)},L=E(this,_n);switch(L.status){case"pending":t.queryHash===r.queryHash&&M(L);break;case"fulfilled":(N.status==="error"||N.data!==L.value)&&_();break;case"rejected":(N.status!=="error"||N.error!==L.reason)&&_();break}}return N}updateResult(t){const n=E(this,Ve),r=this.createResult(E(this,ne),this.options);if(U(this,mr,E(this,ne).state),U(this,No,this.options),E(this,mr).data!==void 0&&U(this,Po,E(this,ne)),Ac(r,n))return;U(this,Ve,r);const o={},s=()=>{if(!n)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!E(this,Ro).size)return!0;const l=new Set(a??E(this,Ro));return this.options.throwOnError&&l.add("error"),Object.keys(E(this,Ve)).some(c=>{const h=c;return E(this,Ve)[h]!==n[h]&&l.has(h)})};(t==null?void 0:t.listeners)!==!1&&s()&&(o.listeners=!0),te(this,oe,Gy).call(this,{...o,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&te(this,oe,Uc).call(this)}},Xe=new WeakMap,ne=new WeakMap,Ys=new WeakMap,Ve=new WeakMap,mr=new WeakMap,No=new WeakMap,_n=new WeakMap,Kt=new WeakMap,Xs=new WeakMap,ko=new WeakMap,Po=new WeakMap,gr=new WeakMap,vr=new WeakMap,In=new WeakMap,Ro=new WeakMap,oe=new WeakSet,ls=function(t){te(this,oe,Hc).call(this);let n=E(this,ne).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(pt)),n},Fc=function(){te(this,oe,Bc).call(this);const t=po(this.options.staleTime,E(this,ne));if(Pr||E(this,Ve).isStale||!Ic(t))return;const r=$y(E(this,Ve).dataUpdatedAt,t)+1;U(this,gr,setTimeout(()=>{E(this,Ve).isStale||this.updateResult()},r))},zc=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(E(this,ne)):this.options.refetchInterval)??!1},$c=function(t){te(this,oe,Vc).call(this),U(this,In,t),!(Pr||Mt(this.options.enabled,E(this,ne))===!1||!Ic(E(this,In))||E(this,In)===0)&&U(this,vr,setInterval(()=>{(this.options.refetchIntervalInBackground||mf.isFocused())&&te(this,oe,ls).call(this)},E(this,In)))},Uc=function(){te(this,oe,Fc).call(this),te(this,oe,$c).call(this,te(this,oe,zc).call(this))},Bc=function(){E(this,gr)&&(clearTimeout(E(this,gr)),U(this,gr,void 0))},Vc=function(){E(this,vr)&&(clearInterval(E(this,vr)),U(this,vr,void 0))},Hc=function(){const t=E(this,Xe).getQueryCache().build(E(this,Xe),this.options);if(t===E(this,ne))return;const n=E(this,ne);U(this,ne,t),U(this,Ys,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},Gy=function(t){Ie.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(E(this,Ve))}),E(this,Xe).getQueryCache().notify({query:E(this,ne),type:"observerResultsUpdated"})})},sm);function UN(e,t){return Mt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Nh(e,t){return UN(e,t)||e.state.data!==void 0&&Wc(e,t,t.refetchOnMount)}function Wc(e,t,n){if(Mt(t.enabled,e)!==!1){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&gf(e,t)}return!1}function kh(e,t,n,r){return(e!==t||Mt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&gf(e,n)}function gf(e,t){return Mt(t.enabled,e)!==!1&&e.isStaleByTime(po(t.staleTime,e))}function BN(e,t){return!Ac(e.getCurrentResult(),t)}var Yy=p.createContext(void 0),VN=e=>{const t=p.useContext(Yy);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},HN=({client:e,children:t})=>(p.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),u.jsx(Yy.Provider,{value:e,children:t})),Xy=p.createContext(!1),WN=()=>p.useContext(Xy);Xy.Provider;function KN(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var QN=p.createContext(KN()),GN=()=>p.useContext(QN);function YN(e,t){return typeof e=="function"?e(...t):!!e}function XN(){}var qN=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},ZN=e=>{p.useEffect(()=>{e.clearReset()},[e])},JN=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&YN(n,[e.error,r]),ek=e=>{e.suspense&&(e.staleTime===void 0&&(e.staleTime=1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},tk=(e,t)=>e.isLoading&&e.isFetching&&!t,nk=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Ph=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function rk(e,t,n){var h,f,d,y,w;const r=VN(),o=WN(),s=GN(),i=r.defaultQueryOptions(e);(f=(h=r.getDefaultOptions().queries)==null?void 0:h._experimental_beforeQuery)==null||f.call(h,i),i._optimisticResults=o?"isRestoring":"optimistic",ek(i),qN(i,s),ZN(s);const a=!r.getQueryCache().get(i.queryHash),[l]=p.useState(()=>new t(r,i)),c=l.getOptimisticResult(i);if(p.useSyncExternalStore(p.useCallback(m=>{const b=o?()=>{}:l.subscribe(Ie.batchCalls(m));return l.updateResult(),b},[l,o]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),p.useEffect(()=>{l.setOptions(i,{listeners:!1})},[i,l]),nk(i,c))throw Ph(i,l,s);if(JN({result:c,errorResetBoundary:s,throwOnError:i.throwOnError,query:r.getQueryCache().get(i.queryHash)}))throw c.error;if((y=(d=r.getDefaultOptions().queries)==null?void 0:d._experimental_afterQuery)==null||y.call(d,i,c),i.experimental_prefetchInRender&&!Pr&&tk(c,o)){const m=a?Ph(i,l,s):(w=r.getQueryCache().get(i.queryHash))==null?void 0:w.promise;m==null||m.catch(XN).finally(()=>{l.updateResult()})}return i.notifyOnChangeProps?c:l.trackResult(c)}function ho(e,t){return rk(e,$N)}/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Vs(){return Vs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vs.apply(this,arguments)}var Dn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Dn||(Dn={}));const Rh="popstate";function ok(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:i,hash:a}=r.location;return Kc("",{pathname:s,search:i,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:$a(o)}return ik(t,n,null,e)}function Ee(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function qy(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function sk(){return Math.random().toString(36).substr(2,8)}function jh(e,t){return{usr:e.state,key:e.key,idx:t}}function Kc(e,t,n,r){return n===void 0&&(n=null),Vs({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Wo(t):t,{state:n,key:t&&t.key||r||sk()})}function $a(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Wo(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function ik(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,i=o.history,a=Dn.Pop,l=null,c=h();c==null&&(c=0,i.replaceState(Vs({},i.state,{idx:c}),""));function h(){return(i.state||{idx:null}).idx}function f(){a=Dn.Pop;let b=h(),g=b==null?null:b-c;c=b,l&&l({action:a,location:m.location,delta:g})}function d(b,g){a=Dn.Push;let v=Kc(m.location,b,g);c=h()+1;let x=jh(v,c),S=m.createHref(v);try{i.pushState(x,"",S)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(S)}s&&l&&l({action:a,location:m.location,delta:1})}function y(b,g){a=Dn.Replace;let v=Kc(m.location,b,g);c=h();let x=jh(v,c),S=m.createHref(v);i.replaceState(x,"",S),s&&l&&l({action:a,location:m.location,delta:0})}function w(b){let g=o.location.origin!=="null"?o.location.origin:o.location.href,v=typeof b=="string"?b:$a(b);return v=v.replace(/ $/,"%20"),Ee(g,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,g)}let m={get action(){return a},get location(){return e(o,i)},listen(b){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(Rh,f),l=b,()=>{o.removeEventListener(Rh,f),l=null}},createHref(b){return t(o,b)},createURL:w,encodeLocation(b){let g=w(b);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:d,replace:y,go(b){return i.go(b)}};return m}var Th;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Th||(Th={}));function ak(e,t,n){return n===void 0&&(n="/"),lk(e,t,n,!1)}function lk(e,t,n,r){let o=typeof t=="string"?Wo(t):t,s=vf(o.pathname||"/",n);if(s==null)return null;let i=Zy(e);uk(i);let a=null;for(let l=0;a==null&&l<i.length;++l){let c=wk(s);a=yk(i[l],c,r)}return a}function Zy(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,i,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:i,route:s};l.relativePath.startsWith("/")&&(Ee(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let c=Kn([r,l.relativePath]),h=n.concat(l);s.children&&s.children.length>0&&(Ee(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Zy(s.children,t,h,c)),!(s.path==null&&!s.index)&&t.push({path:c,score:gk(c,s.index),routesMeta:h})};return e.forEach((s,i)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))o(s,i);else for(let l of Jy(s.path))o(s,i,l)}),t}function Jy(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let i=Jy(r.join("/")),a=[];return a.push(...i.map(l=>l===""?s:[s,l].join("/"))),o&&a.push(...i),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function uk(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:vk(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ck=/^:[\w-]+$/,dk=3,fk=2,pk=1,hk=10,mk=-2,Mh=e=>e==="*";function gk(e,t){let n=e.split("/"),r=n.length;return n.some(Mh)&&(r+=mk),t&&(r+=fk),n.filter(o=>!Mh(o)).reduce((o,s)=>o+(ck.test(s)?dk:s===""?pk:hk),r)}function vk(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function yk(e,t,n){let{routesMeta:r}=e,o={},s="/",i=[];for(let a=0;a<r.length;++a){let l=r[a],c=a===r.length-1,h=s==="/"?t:t.slice(s.length)||"/",f=_h({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},h),d=l.route;if(!f&&c&&n&&!r[r.length-1].route.index&&(f=_h({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!f)return null;Object.assign(o,f.params),i.push({params:o,pathname:Kn([s,f.pathname]),pathnameBase:Ek(Kn([s,f.pathnameBase])),route:d}),f.pathnameBase!=="/"&&(s=Kn([s,f.pathnameBase]))}return i}function _h(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=xk(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],i=s.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((c,h,f)=>{let{paramName:d,isOptional:y}=h;if(d==="*"){let m=a[f]||"";i=s.slice(0,s.length-m.length).replace(/(.)\/+$/,"$1")}const w=a[f];return y&&!w?c[d]=void 0:c[d]=(w||"").replace(/%2F/g,"/"),c},{}),pathname:s,pathnameBase:i,pattern:e}}function xk(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),qy(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function wk(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return qy(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function vf(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function bk(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Wo(e):e;return{pathname:n?n.startsWith("/")?n:Sk(n,t):t,search:Nk(r),hash:kk(o)}}function Sk(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function yu(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ck(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function e0(e,t){let n=Ck(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function t0(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Wo(e):(o=Vs({},e),Ee(!o.pathname||!o.pathname.includes("?"),yu("?","pathname","search",o)),Ee(!o.pathname||!o.pathname.includes("#"),yu("#","pathname","hash",o)),Ee(!o.search||!o.search.includes("#"),yu("#","search","hash",o)));let s=e===""||o.pathname==="",i=s?"/":o.pathname,a;if(i==null)a=n;else{let f=t.length-1;if(!r&&i.startsWith("..")){let d=i.split("/");for(;d[0]==="..";)d.shift(),f-=1;o.pathname=d.join("/")}a=f>=0?t[f]:"/"}let l=bk(o,a),c=i&&i!=="/"&&i.endsWith("/"),h=(s||i===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(c||h)&&(l.pathname+="/"),l}const Kn=e=>e.join("/").replace(/\/\/+/g,"/"),Ek=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Nk=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,kk=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Pk(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const n0=["post","put","patch","delete"];new Set(n0);const Rk=["get",...n0];new Set(Rk);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Hs(){return Hs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hs.apply(this,arguments)}const yf=p.createContext(null),jk=p.createContext(null),Mr=p.createContext(null),El=p.createContext(null),_r=p.createContext({outlet:null,matches:[],isDataRoute:!1}),r0=p.createContext(null);function Tk(e,t){let{relative:n}=t===void 0?{}:t;li()||Ee(!1);let{basename:r,navigator:o}=p.useContext(Mr),{hash:s,pathname:i,search:a}=i0(e,{relative:n}),l=i;return r!=="/"&&(l=i==="/"?r:Kn([r,i])),o.createHref({pathname:l,search:a,hash:s})}function li(){return p.useContext(El)!=null}function ui(){return li()||Ee(!1),p.useContext(El).location}function o0(e){p.useContext(Mr).static||p.useLayoutEffect(e)}function s0(){let{isDataRoute:e}=p.useContext(_r);return e?Vk():Mk()}function Mk(){li()||Ee(!1);let e=p.useContext(yf),{basename:t,future:n,navigator:r}=p.useContext(Mr),{matches:o}=p.useContext(_r),{pathname:s}=ui(),i=JSON.stringify(e0(o,n.v7_relativeSplatPath)),a=p.useRef(!1);return o0(()=>{a.current=!0}),p.useCallback(function(c,h){if(h===void 0&&(h={}),!a.current)return;if(typeof c=="number"){r.go(c);return}let f=t0(c,JSON.parse(i),s,h.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Kn([t,f.pathname])),(h.replace?r.replace:r.push)(f,h.state,h)},[t,r,i,s,e])}function i0(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=p.useContext(Mr),{matches:o}=p.useContext(_r),{pathname:s}=ui(),i=JSON.stringify(e0(o,r.v7_relativeSplatPath));return p.useMemo(()=>t0(e,JSON.parse(i),s,n==="path"),[e,i,s,n])}function _k(e,t){return Ik(e,t)}function Ik(e,t,n,r){li()||Ee(!1);let{navigator:o}=p.useContext(Mr),{matches:s}=p.useContext(_r),i=s[s.length-1],a=i?i.params:{};i&&i.pathname;let l=i?i.pathnameBase:"/";i&&i.route;let c=ui(),h;if(t){var f;let b=typeof t=="string"?Wo(t):t;l==="/"||(f=b.pathname)!=null&&f.startsWith(l)||Ee(!1),h=b}else h=c;let d=h.pathname||"/",y=d;if(l!=="/"){let b=l.replace(/^\//,"").split("/");y="/"+d.replace(/^\//,"").split("/").slice(b.length).join("/")}let w=ak(e,{pathname:y}),m=Fk(w&&w.map(b=>Object.assign({},b,{params:Object.assign({},a,b.params),pathname:Kn([l,o.encodeLocation?o.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?l:Kn([l,o.encodeLocation?o.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),s,n,r);return t&&m?p.createElement(El.Provider,{value:{location:Hs({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:Dn.Pop}},m):m}function Ak(){let e=Bk(),t=Pk(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return p.createElement(p.Fragment,null,p.createElement("h2",null,"Unexpected Application Error!"),p.createElement("h3",{style:{fontStyle:"italic"}},t),n?p.createElement("pre",{style:o},n):null,null)}const Ok=p.createElement(Ak,null);class Dk extends p.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?p.createElement(_r.Provider,{value:this.props.routeContext},p.createElement(r0.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Lk(e){let{routeContext:t,match:n,children:r}=e,o=p.useContext(yf);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),p.createElement(_r.Provider,{value:t},r)}function Fk(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let h=i.findIndex(f=>f.route.id&&(a==null?void 0:a[f.route.id])!==void 0);h>=0||Ee(!1),i=i.slice(0,Math.min(i.length,h+1))}let l=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let f=i[h];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(c=h),f.route.id){let{loaderData:d,errors:y}=n,w=f.route.loader&&d[f.route.id]===void 0&&(!y||y[f.route.id]===void 0);if(f.route.lazy||w){l=!0,c>=0?i=i.slice(0,c+1):i=[i[0]];break}}}return i.reduceRight((h,f,d)=>{let y,w=!1,m=null,b=null;n&&(y=a&&f.route.id?a[f.route.id]:void 0,m=f.route.errorElement||Ok,l&&(c<0&&d===0?(w=!0,b=null):c===d&&(w=!0,b=f.route.hydrateFallbackElement||null)));let g=t.concat(i.slice(0,d+1)),v=()=>{let x;return y?x=m:w?x=b:f.route.Component?x=p.createElement(f.route.Component,null):f.route.element?x=f.route.element:x=h,p.createElement(Lk,{match:f,routeContext:{outlet:h,matches:g,isDataRoute:n!=null},children:x})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?p.createElement(Dk,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:v(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):v()},null)}var a0=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(a0||{}),Ua=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ua||{});function zk(e){let t=p.useContext(yf);return t||Ee(!1),t}function $k(e){let t=p.useContext(jk);return t||Ee(!1),t}function Uk(e){let t=p.useContext(_r);return t||Ee(!1),t}function l0(e){let t=Uk(),n=t.matches[t.matches.length-1];return n.route.id||Ee(!1),n.route.id}function Bk(){var e;let t=p.useContext(r0),n=$k(Ua.UseRouteError),r=l0(Ua.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Vk(){let{router:e}=zk(a0.UseNavigateStable),t=l0(Ua.UseNavigateStable),n=p.useRef(!1);return o0(()=>{n.current=!0}),p.useCallback(function(o,s){s===void 0&&(s={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,Hs({fromRouteId:t},s)))},[e,t])}function oa(e){Ee(!1)}function Hk(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Dn.Pop,navigator:s,static:i=!1,future:a}=e;li()&&Ee(!1);let l=t.replace(/^\/*/,"/"),c=p.useMemo(()=>({basename:l,navigator:s,static:i,future:Hs({v7_relativeSplatPath:!1},a)}),[l,a,s,i]);typeof r=="string"&&(r=Wo(r));let{pathname:h="/",search:f="",hash:d="",state:y=null,key:w="default"}=r,m=p.useMemo(()=>{let b=vf(h,l);return b==null?null:{location:{pathname:b,search:f,hash:d,state:y,key:w},navigationType:o}},[l,h,f,d,y,w,o]);return m==null?null:p.createElement(Mr.Provider,{value:c},p.createElement(El.Provider,{children:n,value:m}))}function Wk(e){let{children:t,location:n}=e;return _k(Qc(t),n)}new Promise(()=>{});function Qc(e,t){t===void 0&&(t=[]);let n=[];return p.Children.forEach(e,(r,o)=>{if(!p.isValidElement(r))return;let s=[...t,o];if(r.type===p.Fragment){n.push.apply(n,Qc(r.props.children,s));return}r.type!==oa&&Ee(!1),!r.props.index||!r.props.children||Ee(!1);let i={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=Qc(r.props.children,s)),n.push(i)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Gc(){return Gc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gc.apply(this,arguments)}function Kk(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Qk(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Gk(e,t){return e.button===0&&(!t||t==="_self")&&!Qk(e)}const Yk=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Xk="6";try{window.__reactRouterVersion=Xk}catch{}const qk="startTransition",Ih=vm[qk];function Zk(e){let{basename:t,children:n,future:r,window:o}=e,s=p.useRef();s.current==null&&(s.current=ok({window:o,v5Compat:!0}));let i=s.current,[a,l]=p.useState({action:i.action,location:i.location}),{v7_startTransition:c}=r||{},h=p.useCallback(f=>{c&&Ih?Ih(()=>l(f)):l(f)},[l,c]);return p.useLayoutEffect(()=>i.listen(h),[i,h]),p.createElement(Hk,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:i,future:r})}const Jk=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",eP=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,tP=p.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:s,replace:i,state:a,target:l,to:c,preventScrollReset:h,viewTransition:f}=t,d=Kk(t,Yk),{basename:y}=p.useContext(Mr),w,m=!1;if(typeof c=="string"&&eP.test(c)&&(w=c,Jk))try{let x=new URL(window.location.href),S=c.startsWith("//")?new URL(x.protocol+c):new URL(c),C=vf(S.pathname,y);S.origin===x.origin&&C!=null?c=C+S.search+S.hash:m=!0}catch{}let b=Tk(c,{relative:o}),g=nP(c,{replace:i,state:a,target:l,preventScrollReset:h,relative:o,viewTransition:f});function v(x){r&&r(x),x.defaultPrevented||g(x)}return p.createElement("a",Gc({},d,{href:w||b,onClick:m||s?r:v,ref:n,target:l}))});var Ah;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ah||(Ah={}));var Oh;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Oh||(Oh={}));function nP(e,t){let{target:n,replace:r,state:o,preventScrollReset:s,relative:i,viewTransition:a}=t===void 0?{}:t,l=s0(),c=ui(),h=i0(e,{relative:i});return p.useCallback(f=>{if(Gk(f,n)){f.preventDefault();let d=r!==void 0?r:$a(c)===$a(h);l(e,{replace:d,state:o,preventScrollReset:s,relative:i,viewTransition:a})}},[c,l,h,r,o,n,e,s,i,a])}const xu=768;function rP(){const[e,t]=p.useState(void 0);return p.useEffect(()=>{const n=window.matchMedia(`(max-width: ${xu-1}px)`),r=()=>{t(window.innerWidth<xu)};return n.addEventListener("change",r),t(window.innerWidth<xu),()=>n.removeEventListener("change",r)},[]),!!e}const oP=Uo("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),he=p.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?Ct:"button";return u.jsx(i,{className:V(oP({variant:t,size:n,className:e})),ref:s,...o})});he.displayName="Button";const Nl=p.forwardRef(({className:e,type:t,...n},r)=>u.jsx("input",{type:t,className:V("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));Nl.displayName="Input";var sP="Separator",Dh="horizontal",iP=["horizontal","vertical"],u0=p.forwardRef((e,t)=>{const{decorative:n,orientation:r=Dh,...o}=e,s=aP(r)?r:Dh,a=n?{role:"none"}:{"aria-orientation":s==="vertical"?s:void 0,role:"separator"};return u.jsx(Y.div,{"data-orientation":s,...a,...o,ref:t})});u0.displayName=sP;function aP(e){return iP.includes(e)}var c0=u0;const d0=p.forwardRef(({className:e,orientation:t="horizontal",decorative:n=!0,...r},o)=>u.jsx(c0,{ref:o,decorative:n,orientation:t,className:V("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...r}));d0.displayName=c0.displayName;var wu="focusScope.autoFocusOnMount",bu="focusScope.autoFocusOnUnmount",Lh={bubbles:!1,cancelable:!0},lP="FocusScope",xf=p.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,l]=p.useState(null),c=Ke(o),h=Ke(s),f=p.useRef(null),d=ae(t,m=>l(m)),y=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(r){let m=function(x){if(y.paused||!a)return;const S=x.target;a.contains(S)?f.current=S:Sn(f.current,{select:!0})},b=function(x){if(y.paused||!a)return;const S=x.relatedTarget;S!==null&&(a.contains(S)||Sn(f.current,{select:!0}))},g=function(x){if(document.activeElement===document.body)for(const C of x)C.removedNodes.length>0&&Sn(a)};document.addEventListener("focusin",m),document.addEventListener("focusout",b);const v=new MutationObserver(g);return a&&v.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",b),v.disconnect()}}},[r,a,y.paused]),p.useEffect(()=>{if(a){zh.add(y);const m=document.activeElement;if(!a.contains(m)){const g=new CustomEvent(wu,Lh);a.addEventListener(wu,c),a.dispatchEvent(g),g.defaultPrevented||(uP(hP(f0(a)),{select:!0}),document.activeElement===m&&Sn(a))}return()=>{a.removeEventListener(wu,c),setTimeout(()=>{const g=new CustomEvent(bu,Lh);a.addEventListener(bu,h),a.dispatchEvent(g),g.defaultPrevented||Sn(m??document.body,{select:!0}),a.removeEventListener(bu,h),zh.remove(y)},0)}}},[a,c,h,y]);const w=p.useCallback(m=>{if(!n&&!r||y.paused)return;const b=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,g=document.activeElement;if(b&&g){const v=m.currentTarget,[x,S]=cP(v);x&&S?!m.shiftKey&&g===S?(m.preventDefault(),n&&Sn(x,{select:!0})):m.shiftKey&&g===x&&(m.preventDefault(),n&&Sn(S,{select:!0})):g===v&&m.preventDefault()}},[n,r,y.paused]);return u.jsx(Y.div,{tabIndex:-1,...i,ref:d,onKeyDown:w})});xf.displayName=lP;function uP(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Sn(r,{select:t}),document.activeElement!==n)return}function cP(e){const t=f0(e),n=Fh(t,e),r=Fh(t.reverse(),e);return[n,r]}function f0(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Fh(e,t){for(const n of e)if(!dP(n,{upTo:t}))return n}function dP(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function fP(e){return e instanceof HTMLInputElement&&"select"in e}function Sn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&fP(e)&&t&&e.select()}}var zh=pP();function pP(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=$h(e,t),e.unshift(t)},remove(t){var n;e=$h(e,t),(n=e[0])==null||n.resume()}}}function $h(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function hP(e){return e.filter(t=>t.tagName!=="A")}var Su=0;function p0(){p.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Uh()),document.body.insertAdjacentElement("beforeend",e[1]??Uh()),Su++,()=>{Su===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Su--}},[])}function Uh(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Gt=function(){return Gt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Gt.apply(this,arguments)};function h0(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function mP(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var sa="right-scroll-bar-position",ia="width-before-scroll-bar",gP="with-scroll-bars-hidden",vP="--removed-body-scroll-bar-size";function Cu(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function yP(e,t){var n=p.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var xP=typeof window<"u"?p.useLayoutEffect:p.useEffect,Bh=new WeakMap;function wP(e,t){var n=yP(null,function(r){return e.forEach(function(o){return Cu(o,r)})});return xP(function(){var r=Bh.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||Cu(a,null)}),s.forEach(function(a){o.has(a)||Cu(a,i)})}Bh.set(n,e)},[e]),n}function bP(e){return e}function SP(e,t){t===void 0&&(t=bP);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var h=i;i=[],h.forEach(s)},c=function(){return Promise.resolve().then(l)};c(),n={push:function(h){i.push(h),c()},filter:function(h){return i=i.filter(h),n}}}};return o}function CP(e){e===void 0&&(e={});var t=SP(null);return t.options=Gt({async:!0,ssr:!1},e),t}var m0=function(e){var t=e.sideCar,n=h0(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return p.createElement(r,Gt({},n))};m0.isSideCarExport=!0;function EP(e,t){return e.useMedium(t),m0}var g0=CP(),Eu=function(){},kl=p.forwardRef(function(e,t){var n=p.useRef(null),r=p.useState({onScrollCapture:Eu,onWheelCapture:Eu,onTouchMoveCapture:Eu}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,l=e.className,c=e.removeScrollBar,h=e.enabled,f=e.shards,d=e.sideCar,y=e.noIsolation,w=e.inert,m=e.allowPinchZoom,b=e.as,g=b===void 0?"div":b,v=e.gapMode,x=h0(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=d,C=wP([n,t]),P=Gt(Gt({},x),o);return p.createElement(p.Fragment,null,h&&p.createElement(S,{sideCar:g0,removeScrollBar:c,shards:f,noIsolation:y,inert:w,setCallbacks:s,allowPinchZoom:!!m,lockRef:n,gapMode:v}),i?p.cloneElement(p.Children.only(a),Gt(Gt({},P),{ref:C})):p.createElement(g,Gt({},P,{className:l,ref:C}),a))});kl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};kl.classNames={fullWidth:ia,zeroRight:sa};var NP=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function kP(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=NP();return t&&e.setAttribute("nonce",t),e}function PP(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function RP(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var jP=function(){var e=0,t=null;return{add:function(n){e==0&&(t=kP())&&(PP(t,n),RP(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},TP=function(){var e=jP();return function(t,n){p.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},v0=function(){var e=TP(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},MP={left:0,top:0,right:0,gap:0},Nu=function(e){return parseInt(e||"",10)||0},_P=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Nu(n),Nu(r),Nu(o)]},IP=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return MP;var t=_P(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},AP=v0(),mo="data-scroll-locked",OP=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(gP,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(mo,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(sa,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ia,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(sa," .").concat(sa,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ia," .").concat(ia,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(mo,`] {
    `).concat(vP,": ").concat(a,`px;
  }
`)},Vh=function(){var e=parseInt(document.body.getAttribute(mo)||"0",10);return isFinite(e)?e:0},DP=function(){p.useEffect(function(){return document.body.setAttribute(mo,(Vh()+1).toString()),function(){var e=Vh()-1;e<=0?document.body.removeAttribute(mo):document.body.setAttribute(mo,e.toString())}},[])},LP=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;DP();var s=p.useMemo(function(){return IP(o)},[o]);return p.createElement(AP,{styles:OP(s,!t,o,n?"":"!important")})},Yc=!1;if(typeof window<"u")try{var zi=Object.defineProperty({},"passive",{get:function(){return Yc=!0,!0}});window.addEventListener("test",zi,zi),window.removeEventListener("test",zi,zi)}catch{Yc=!1}var Br=Yc?{passive:!1}:!1,FP=function(e){return e.tagName==="TEXTAREA"},y0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!FP(e)&&n[t]==="visible")},zP=function(e){return y0(e,"overflowY")},$P=function(e){return y0(e,"overflowX")},Hh=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=x0(e,r);if(o){var s=w0(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},UP=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},BP=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},x0=function(e,t){return e==="v"?zP(t):$P(t)},w0=function(e,t){return e==="v"?UP(t):BP(t)},VP=function(e,t){return e==="h"&&t==="rtl"?-1:1},HP=function(e,t,n,r,o){var s=VP(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,l=t.contains(a),c=!1,h=i>0,f=0,d=0;do{var y=w0(e,a),w=y[0],m=y[1],b=y[2],g=m-b-s*w;(w||g)&&x0(e,a)&&(f+=g,d+=w),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(h&&(Math.abs(f)<1||!o)||!h&&(Math.abs(d)<1||!o))&&(c=!0),c},$i=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Wh=function(e){return[e.deltaX,e.deltaY]},Kh=function(e){return e&&"current"in e?e.current:e},WP=function(e,t){return e[0]===t[0]&&e[1]===t[1]},KP=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},QP=0,Vr=[];function GP(e){var t=p.useRef([]),n=p.useRef([0,0]),r=p.useRef(),o=p.useState(QP++)[0],s=p.useState(v0)[0],i=p.useRef(e);p.useEffect(function(){i.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=mP([e.lockRef.current],(e.shards||[]).map(Kh),!0).filter(Boolean);return m.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=p.useCallback(function(m,b){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!i.current.allowPinchZoom;var g=$i(m),v=n.current,x="deltaX"in m?m.deltaX:v[0]-g[0],S="deltaY"in m?m.deltaY:v[1]-g[1],C,P=m.target,N=Math.abs(x)>Math.abs(S)?"h":"v";if("touches"in m&&N==="h"&&P.type==="range")return!1;var R=Hh(N,P);if(!R)return!0;if(R?C=N:(C=N==="v"?"h":"v",R=Hh(N,P)),!R)return!1;if(!r.current&&"changedTouches"in m&&(x||S)&&(r.current=C),!C)return!0;var M=r.current||C;return HP(M,b,m,M==="h"?x:S,!0)},[]),l=p.useCallback(function(m){var b=m;if(!(!Vr.length||Vr[Vr.length-1]!==s)){var g="deltaY"in b?Wh(b):$i(b),v=t.current.filter(function(C){return C.name===b.type&&(C.target===b.target||b.target===C.shadowParent)&&WP(C.delta,g)})[0];if(v&&v.should){b.cancelable&&b.preventDefault();return}if(!v){var x=(i.current.shards||[]).map(Kh).filter(Boolean).filter(function(C){return C.contains(b.target)}),S=x.length>0?a(b,x[0]):!i.current.noIsolation;S&&b.cancelable&&b.preventDefault()}}},[]),c=p.useCallback(function(m,b,g,v){var x={name:m,delta:b,target:g,should:v,shadowParent:YP(g)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(S){return S!==x})},1)},[]),h=p.useCallback(function(m){n.current=$i(m),r.current=void 0},[]),f=p.useCallback(function(m){c(m.type,Wh(m),m.target,a(m,e.lockRef.current))},[]),d=p.useCallback(function(m){c(m.type,$i(m),m.target,a(m,e.lockRef.current))},[]);p.useEffect(function(){return Vr.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:d}),document.addEventListener("wheel",l,Br),document.addEventListener("touchmove",l,Br),document.addEventListener("touchstart",h,Br),function(){Vr=Vr.filter(function(m){return m!==s}),document.removeEventListener("wheel",l,Br),document.removeEventListener("touchmove",l,Br),document.removeEventListener("touchstart",h,Br)}},[]);var y=e.removeScrollBar,w=e.inert;return p.createElement(p.Fragment,null,w?p.createElement(s,{styles:KP(o)}):null,y?p.createElement(LP,{gapMode:e.gapMode}):null)}function YP(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const XP=EP(g0,GP);var wf=p.forwardRef(function(e,t){return p.createElement(kl,Gt({},e,{ref:t,sideCar:XP}))});wf.classNames=kl.classNames;var qP=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Hr=new WeakMap,Ui=new WeakMap,Bi={},ku=0,b0=function(e){return e&&(e.host||b0(e.parentNode))},ZP=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=b0(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},JP=function(e,t,n,r){var o=ZP(t,Array.isArray(e)?e:[e]);Bi[n]||(Bi[n]=new WeakMap);var s=Bi[n],i=[],a=new Set,l=new Set(o),c=function(f){!f||a.has(f)||(a.add(f),c(f.parentNode))};o.forEach(c);var h=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(d){if(a.has(d))h(d);else try{var y=d.getAttribute(r),w=y!==null&&y!=="false",m=(Hr.get(d)||0)+1,b=(s.get(d)||0)+1;Hr.set(d,m),s.set(d,b),i.push(d),m===1&&w&&Ui.set(d,!0),b===1&&d.setAttribute(n,"true"),w||d.setAttribute(r,"true")}catch(g){console.error("aria-hidden: cannot operate on ",d,g)}})};return h(t),a.clear(),ku++,function(){i.forEach(function(f){var d=Hr.get(f)-1,y=s.get(f)-1;Hr.set(f,d),s.set(f,y),d||(Ui.has(f)||f.removeAttribute(r),Ui.delete(f)),y||f.removeAttribute(n)}),ku--,ku||(Hr=new WeakMap,Hr=new WeakMap,Ui=new WeakMap,Bi={})}},S0=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=qP(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),JP(r,o,n,"aria-hidden")):function(){return null}},bf="Dialog",[C0,bM]=rr(bf),[eR,Ft]=C0(bf),E0=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,a=p.useRef(null),l=p.useRef(null),[c=!1,h]=or({prop:r,defaultProp:o,onChange:s});return u.jsx(eR,{scope:t,triggerRef:a,contentRef:l,contentId:Wn(),titleId:Wn(),descriptionId:Wn(),open:c,onOpenChange:h,onOpenToggle:p.useCallback(()=>h(f=>!f),[h]),modal:i,children:n})};E0.displayName=bf;var N0="DialogTrigger",tR=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ft(N0,n),s=ae(t,o.triggerRef);return u.jsx(Y.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Ef(o.open),...r,ref:s,onClick:$(e.onClick,o.onOpenToggle)})});tR.displayName=N0;var Sf="DialogPortal",[nR,k0]=C0(Sf,{forceMount:void 0}),P0=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Ft(Sf,t);return u.jsx(nR,{scope:t,forceMount:n,children:p.Children.map(r,i=>u.jsx(Lt,{present:n||s.open,children:u.jsx(cl,{asChild:!0,container:o,children:i})}))})};P0.displayName=Sf;var Ba="DialogOverlay",R0=p.forwardRef((e,t)=>{const n=k0(Ba,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ft(Ba,e.__scopeDialog);return s.modal?u.jsx(Lt,{present:r||s.open,children:u.jsx(rR,{...o,ref:t})}):null});R0.displayName=Ba;var rR=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ft(Ba,n);return u.jsx(wf,{as:Ct,allowPinchZoom:!0,shards:[o.contentRef],children:u.jsx(Y.div,{"data-state":Ef(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Rr="DialogContent",j0=p.forwardRef((e,t)=>{const n=k0(Rr,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ft(Rr,e.__scopeDialog);return u.jsx(Lt,{present:r||s.open,children:s.modal?u.jsx(oR,{...o,ref:t}):u.jsx(sR,{...o,ref:t})})});j0.displayName=Rr;var oR=p.forwardRef((e,t)=>{const n=Ft(Rr,e.__scopeDialog),r=p.useRef(null),o=ae(t,n.contentRef,r);return p.useEffect(()=>{const s=r.current;if(s)return S0(s)},[]),u.jsx(T0,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:$(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:$(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:$(e.onFocusOutside,s=>s.preventDefault())})}),sR=p.forwardRef((e,t)=>{const n=Ft(Rr,e.__scopeDialog),r=p.useRef(!1),o=p.useRef(!1);return u.jsx(T0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var l,c;(l=e.onInteractOutside)==null||l.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((c=n.triggerRef.current)==null?void 0:c.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),T0=p.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,a=Ft(Rr,n),l=p.useRef(null),c=ae(t,l);return p0(),u.jsxs(u.Fragment,{children:[u.jsx(xf,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:u.jsx(ri,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Ef(a.open),...i,ref:c,onDismiss:()=>a.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(iR,{titleId:a.titleId}),u.jsx(lR,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Cf="DialogTitle",M0=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ft(Cf,n);return u.jsx(Y.h2,{id:o.titleId,...r,ref:t})});M0.displayName=Cf;var _0="DialogDescription",I0=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ft(_0,n);return u.jsx(Y.p,{id:o.descriptionId,...r,ref:t})});I0.displayName=_0;var A0="DialogClose",O0=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ft(A0,n);return u.jsx(Y.button,{type:"button",...r,ref:t,onClick:$(e.onClick,()=>o.onOpenChange(!1))})});O0.displayName=A0;function Ef(e){return e?"open":"closed"}var D0="DialogTitleWarning",[SM,L0]=LS(D0,{contentName:Rr,titleName:Cf,docsSlug:"dialog"}),iR=({titleId:e})=>{const t=L0(D0),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return p.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},aR="DialogDescriptionWarning",lR=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${L0(aR).contentName}}.`;return p.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},uR=E0,cR=P0,F0=R0,z0=j0,$0=M0,U0=I0,dR=O0;const fR=uR,pR=cR,B0=p.forwardRef(({className:e,...t},n)=>u.jsx(F0,{className:V("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));B0.displayName=F0.displayName;const hR=Uo("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),V0=p.forwardRef(({side:e="right",className:t,children:n,...r},o)=>u.jsxs(pR,{children:[u.jsx(B0,{}),u.jsxs(z0,{ref:o,className:V(hR({side:e}),t),...r,children:[n,u.jsxs(dR,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[u.jsx(si,{className:"h-4 w-4"}),u.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));V0.displayName=z0.displayName;const mR=p.forwardRef(({className:e,...t},n)=>u.jsx($0,{ref:n,className:V("text-lg font-semibold text-foreground",e),...t}));mR.displayName=$0.displayName;const gR=p.forwardRef(({className:e,...t},n)=>u.jsx(U0,{ref:n,className:V("text-sm text-muted-foreground",e),...t}));gR.displayName=U0.displayName;function Qh({className:e,...t}){return u.jsx("div",{className:V("animate-pulse rounded-md bg-muted",e),...t})}const vR="sidebar:state",yR=60*60*24*7,xR="16rem",wR="18rem",bR="3rem",SR="b",H0=p.createContext(null);function Pl(){const e=p.useContext(H0);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const W0=p.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:s,...i},a)=>{const l=rP(),[c,h]=p.useState(!1),[f,d]=p.useState(e),y=t??f,w=p.useCallback(v=>{const x=typeof v=="function"?v(y):v;n?n(x):d(x),document.cookie=`${vR}=${x}; path=/; max-age=${yR}`},[n,y]),m=p.useCallback(()=>l?h(v=>!v):w(v=>!v),[l,w,h]);p.useEffect(()=>{const v=x=>{x.key===SR&&(x.metaKey||x.ctrlKey)&&(x.preventDefault(),m())};return window.addEventListener("keydown",v),()=>window.removeEventListener("keydown",v)},[m]);const b=y?"expanded":"collapsed",g=p.useMemo(()=>({state:b,open:y,setOpen:w,isMobile:l,openMobile:c,setOpenMobile:h,toggleSidebar:m}),[b,y,w,l,c,h,m]);return u.jsx(H0.Provider,{value:g,children:u.jsx(Fy,{delayDuration:0,children:u.jsx("div",{style:{"--sidebar-width":xR,"--sidebar-width-icon":bR,...o},className:V("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:a,...i,children:s})})})});W0.displayName="SidebarProvider";const K0=p.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...s},i)=>{const{isMobile:a,state:l,openMobile:c,setOpenMobile:h}=Pl();return n==="none"?u.jsx("div",{className:V("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...s,children:o}):a?u.jsx(fR,{open:c,onOpenChange:h,...s,children:u.jsx(V0,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":wR},side:e,children:u.jsx("div",{className:"flex h-full w-full flex-col",children:o})})}):u.jsxs("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":l,"data-collapsible":l==="collapsed"?n:"","data-variant":t,"data-side":e,children:[u.jsx("div",{className:V("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),u.jsx("div",{className:V("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...s,children:u.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:o})})]})});K0.displayName="Sidebar";const Q0=p.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:o}=Pl();return u.jsxs(he,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:V("h-7 w-7",e),onClick:s=>{t==null||t(s),o()},...n,children:[u.jsx(_C,{}),u.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});Q0.displayName="SidebarTrigger";const CR=p.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=Pl();return u.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:V("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});CR.displayName="SidebarRail";const ER=p.forwardRef(({className:e,...t},n)=>u.jsx("main",{ref:n,className:V("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));ER.displayName="SidebarInset";const NR=p.forwardRef(({className:e,...t},n)=>u.jsx(Nl,{ref:n,"data-sidebar":"input",className:V("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));NR.displayName="SidebarInput";const kR=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,"data-sidebar":"header",className:V("flex flex-col gap-2 p-2",e),...t}));kR.displayName="SidebarHeader";const PR=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,"data-sidebar":"footer",className:V("flex flex-col gap-2 p-2",e),...t}));PR.displayName="SidebarFooter";const RR=p.forwardRef(({className:e,...t},n)=>u.jsx(d0,{ref:n,"data-sidebar":"separator",className:V("mx-2 w-auto bg-sidebar-border",e),...t}));RR.displayName="SidebarSeparator";const G0=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,"data-sidebar":"content",className:V("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));G0.displayName="SidebarContent";const Y0=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,"data-sidebar":"group",className:V("relative flex w-full min-w-0 flex-col p-2",e),...t}));Y0.displayName="SidebarGroup";const X0=p.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Ct:"div";return u.jsx(o,{ref:r,"data-sidebar":"group-label",className:V("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});X0.displayName="SidebarGroupLabel";const jR=p.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Ct:"button";return u.jsx(o,{ref:r,"data-sidebar":"group-action",className:V("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});jR.displayName="SidebarGroupAction";const q0=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,"data-sidebar":"group-content",className:V("w-full text-sm",e),...t}));q0.displayName="SidebarGroupContent";const Z0=p.forwardRef(({className:e,...t},n)=>u.jsx("ul",{ref:n,"data-sidebar":"menu",className:V("flex w-full min-w-0 flex-col gap-1",e),...t}));Z0.displayName="SidebarMenu";const J0=p.forwardRef(({className:e,...t},n)=>u.jsx("li",{ref:n,"data-sidebar":"menu-item",className:V("group/menu-item relative",e),...t}));J0.displayName="SidebarMenuItem";const TR=Uo("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),ex=p.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:s,...i},a)=>{const l=e?Ct:"button",{isMobile:c,state:h}=Pl(),f=u.jsx(l,{ref:a,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:V(TR({variant:n,size:r}),s),...i});return o?(typeof o=="string"&&(o={children:o}),u.jsxs(SN,{children:[u.jsx(CN,{asChild:!0,children:f}),u.jsx(zy,{side:"right",align:"center",hidden:h!=="collapsed"||c,...o})]})):f});ex.displayName="SidebarMenuButton";const MR=p.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},o)=>{const s=t?Ct:"button";return u.jsx(s,{ref:o,"data-sidebar":"menu-action",className:V("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});MR.displayName="SidebarMenuAction";const _R=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:V("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));_R.displayName="SidebarMenuBadge";const IR=p.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const o=p.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return u.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:V("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&u.jsx(Qh,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),u.jsx(Qh,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})});IR.displayName="SidebarMenuSkeleton";const AR=p.forwardRef(({className:e,...t},n)=>u.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:V("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));AR.displayName="SidebarMenuSub";const OR=p.forwardRef(({...e},t)=>u.jsx("li",{ref:t,...e}));OR.displayName="SidebarMenuSubItem";const DR=p.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...o},s)=>{const i=e?Ct:"a";return u.jsx(i,{ref:s,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:V("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...o})});DR.displayName="SidebarMenuSubButton";const LR=[{title:"Home",url:"/",icon:jC},{title:"Live View",url:"#",icon:hl},{title:"All Captures",url:"/all-captures",icon:TC},{title:"History Timeline",url:"#",icon:RC},{title:"Sensor Events",url:"#",icon:ef},{title:"Object Search",url:"#",icon:Ia},{title:"Alerts",url:"#",icon:Aa},{title:"Settings",url:"#",icon:qv}];function FR(){return u.jsx(K0,{className:"border-r border-sidebar-border",children:u.jsx(G0,{className:"bg-sidebar",children:u.jsxs(Y0,{children:[u.jsx(X0,{className:"text-sidebar-primary font-semibold text-sm mb-4",children:"AI Security System"}),u.jsx(q0,{children:u.jsx(Z0,{children:LR.map(e=>u.jsx(J0,{children:u.jsx(ex,{asChild:!0,className:"hover:bg-sidebar-accent hover:text-sidebar-primary transition-colors group",children:e.url.startsWith("/")?u.jsxs(tP,{to:e.url,className:"flex items-center gap-3 p-3",children:[u.jsx(e.icon,{className:"h-5 w-5 group-hover:text-sidebar-primary"}),u.jsx("span",{className:"font-medium",children:e.title})]}):u.jsxs("a",{href:e.url,className:"flex items-center gap-3 p-3",children:[u.jsx(e.icon,{className:"h-5 w-5 group-hover:text-sidebar-primary"}),u.jsx("span",{className:"font-medium",children:e.title})]})})},e.title))})})]})})})}var zR=p.createContext(void 0);function Rl(e){const t=p.useContext(zR);return e||t||"ltr"}function $R(e,t=[]){let n=[];function r(s,i){const a=p.createContext(i),l=n.length;n=[...n,i];function c(f){const{scope:d,children:y,...w}=f,m=(d==null?void 0:d[e][l])||a,b=p.useMemo(()=>w,Object.values(w));return u.jsx(m.Provider,{value:b,children:y})}function h(f,d){const y=(d==null?void 0:d[e][l])||a,w=p.useContext(y);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return c.displayName=s+"Provider",[c,h]}const o=()=>{const s=n.map(i=>p.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,UR(o,...t)]}function UR(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(s)[`__scope${c}`];return{...a,...f}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Pu="rovingFocusGroup.onEntryFocus",BR={bubbles:!1,cancelable:!0},jl="RovingFocusGroup",[Xc,tx,VR]=ul(jl),[HR,Tl]=$R(jl,[VR]),[WR,KR]=HR(jl),nx=p.forwardRef((e,t)=>u.jsx(Xc.Provider,{scope:e.__scopeRovingFocusGroup,children:u.jsx(Xc.Slot,{scope:e.__scopeRovingFocusGroup,children:u.jsx(QR,{...e,ref:t})})}));nx.displayName=jl;var QR=p.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:c,preventScrollOnEntryFocus:h=!1,...f}=e,d=p.useRef(null),y=ae(t,d),w=Rl(s),[m=null,b]=or({prop:i,defaultProp:a,onChange:l}),[g,v]=p.useState(!1),x=Ke(c),S=tx(n),C=p.useRef(!1),[P,N]=p.useState(0);return p.useEffect(()=>{const R=d.current;if(R)return R.addEventListener(Pu,x),()=>R.removeEventListener(Pu,x)},[x]),u.jsx(WR,{scope:n,orientation:r,dir:w,loop:o,currentTabStopId:m,onItemFocus:p.useCallback(R=>b(R),[b]),onItemShiftTab:p.useCallback(()=>v(!0),[]),onFocusableItemAdd:p.useCallback(()=>N(R=>R+1),[]),onFocusableItemRemove:p.useCallback(()=>N(R=>R-1),[]),children:u.jsx(Y.div,{tabIndex:g||P===0?-1:0,"data-orientation":r,...f,ref:y,style:{outline:"none",...e.style},onMouseDown:$(e.onMouseDown,()=>{C.current=!0}),onFocus:$(e.onFocus,R=>{const M=!C.current;if(R.target===R.currentTarget&&M&&!g){const _=new CustomEvent(Pu,BR);if(R.currentTarget.dispatchEvent(_),!_.defaultPrevented){const L=S().filter(B=>B.focusable),A=L.find(B=>B.active),W=L.find(B=>B.id===m),G=[A,W,...L].filter(Boolean).map(B=>B.ref.current);sx(G,h)}}C.current=!1}),onBlur:$(e.onBlur,()=>v(!1))})})}),rx="RovingFocusGroupItem",ox=p.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...i}=e,a=Wn(),l=s||a,c=KR(rx,n),h=c.currentTabStopId===l,f=tx(n),{onFocusableItemAdd:d,onFocusableItemRemove:y}=c;return p.useEffect(()=>{if(r)return d(),()=>y()},[r,d,y]),u.jsx(Xc.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:u.jsx(Y.span,{tabIndex:h?0:-1,"data-orientation":c.orientation,...i,ref:t,onMouseDown:$(e.onMouseDown,w=>{r?c.onItemFocus(l):w.preventDefault()}),onFocus:$(e.onFocus,()=>c.onItemFocus(l)),onKeyDown:$(e.onKeyDown,w=>{if(w.key==="Tab"&&w.shiftKey){c.onItemShiftTab();return}if(w.target!==w.currentTarget)return;const m=XR(w,c.orientation,c.dir);if(m!==void 0){if(w.metaKey||w.ctrlKey||w.altKey||w.shiftKey)return;w.preventDefault();let g=f().filter(v=>v.focusable).map(v=>v.ref.current);if(m==="last")g.reverse();else if(m==="prev"||m==="next"){m==="prev"&&g.reverse();const v=g.indexOf(w.currentTarget);g=c.loop?qR(g,v+1):g.slice(v+1)}setTimeout(()=>sx(g))}})})})});ox.displayName=rx;var GR={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function YR(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function XR(e,t,n){const r=YR(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return GR[r]}function sx(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function qR(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var ix=nx,ax=ox,qc=["Enter"," "],ZR=["ArrowDown","PageUp","Home"],lx=["ArrowUp","PageDown","End"],JR=[...ZR,...lx],ej={ltr:[...qc,"ArrowRight"],rtl:[...qc,"ArrowLeft"]},tj={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ci="Menu",[Ws,nj,rj]=ul(ci),[Ir,ux]=rr(ci,[rj,xl,Tl]),Ml=xl(),cx=Tl(),[oj,Ar]=Ir(ci),[sj,di]=Ir(ci),dx=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:i=!0}=e,a=Ml(t),[l,c]=p.useState(null),h=p.useRef(!1),f=Ke(s),d=Rl(o);return p.useEffect(()=>{const y=()=>{h.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>h.current=!1;return document.addEventListener("keydown",y,{capture:!0}),()=>{document.removeEventListener("keydown",y,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),u.jsx(ky,{...a,children:u.jsx(oj,{scope:t,open:n,onOpenChange:f,content:l,onContentChange:c,children:u.jsx(sj,{scope:t,onClose:p.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:h,dir:d,modal:i,children:r})})})};dx.displayName=ci;var ij="MenuAnchor",Nf=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Ml(n);return u.jsx(Py,{...o,...r,ref:t})});Nf.displayName=ij;var kf="MenuPortal",[aj,fx]=Ir(kf,{forceMount:void 0}),px=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,s=Ar(kf,t);return u.jsx(aj,{scope:t,forceMount:n,children:u.jsx(Lt,{present:n||s.open,children:u.jsx(cl,{asChild:!0,container:o,children:r})})})};px.displayName=kf;var yt="MenuContent",[lj,Pf]=Ir(yt),hx=p.forwardRef((e,t)=>{const n=fx(yt,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=Ar(yt,e.__scopeMenu),i=di(yt,e.__scopeMenu);return u.jsx(Ws.Provider,{scope:e.__scopeMenu,children:u.jsx(Lt,{present:r||s.open,children:u.jsx(Ws.Slot,{scope:e.__scopeMenu,children:i.modal?u.jsx(uj,{...o,ref:t}):u.jsx(cj,{...o,ref:t})})})})}),uj=p.forwardRef((e,t)=>{const n=Ar(yt,e.__scopeMenu),r=p.useRef(null),o=ae(t,r);return p.useEffect(()=>{const s=r.current;if(s)return S0(s)},[]),u.jsx(Rf,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:$(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),cj=p.forwardRef((e,t)=>{const n=Ar(yt,e.__scopeMenu);return u.jsx(Rf,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Rf=p.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:l,onEscapeKeyDown:c,onPointerDownOutside:h,onFocusOutside:f,onInteractOutside:d,onDismiss:y,disableOutsideScroll:w,...m}=e,b=Ar(yt,n),g=di(yt,n),v=Ml(n),x=cx(n),S=nj(n),[C,P]=p.useState(null),N=p.useRef(null),R=ae(t,N,b.onContentChange),M=p.useRef(0),_=p.useRef(""),L=p.useRef(0),A=p.useRef(null),W=p.useRef("right"),O=p.useRef(0),G=w?wf:p.Fragment,B=w?{as:Ct,allowPinchZoom:!0}:void 0,K=j=>{var Pe,Et;const F=_.current+j,D=S().filter(Re=>!Re.disabled),H=document.activeElement,Z=(Pe=D.find(Re=>Re.ref.current===H))==null?void 0:Pe.textValue,ie=D.map(Re=>Re.textValue),De=Sj(ie,F,Z),ee=(Et=D.find(Re=>Re.textValue===De))==null?void 0:Et.ref.current;(function Re(Nt){_.current=Nt,window.clearTimeout(M.current),Nt!==""&&(M.current=window.setTimeout(()=>Re(""),1e3))})(F),ee&&setTimeout(()=>ee.focus())};p.useEffect(()=>()=>window.clearTimeout(M.current),[]),p0();const k=p.useCallback(j=>{var D,H;return W.current===((D=A.current)==null?void 0:D.side)&&Ej(j,(H=A.current)==null?void 0:H.area)},[]);return u.jsx(lj,{scope:n,searchRef:_,onItemEnter:p.useCallback(j=>{k(j)&&j.preventDefault()},[k]),onItemLeave:p.useCallback(j=>{var F;k(j)||((F=N.current)==null||F.focus(),P(null))},[k]),onTriggerLeave:p.useCallback(j=>{k(j)&&j.preventDefault()},[k]),pointerGraceTimerRef:L,onPointerGraceIntentChange:p.useCallback(j=>{A.current=j},[]),children:u.jsx(G,{...B,children:u.jsx(xf,{asChild:!0,trapped:o,onMountAutoFocus:$(s,j=>{var F;j.preventDefault(),(F=N.current)==null||F.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:u.jsx(ri,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:c,onPointerDownOutside:h,onFocusOutside:f,onInteractOutside:d,onDismiss:y,children:u.jsx(ix,{asChild:!0,...x,dir:g.dir,orientation:"vertical",loop:r,currentTabStopId:C,onCurrentTabStopIdChange:P,onEntryFocus:$(l,j=>{g.isUsingKeyboardRef.current||j.preventDefault()}),preventScrollOnEntryFocus:!0,children:u.jsx(Ry,{role:"menu","aria-orientation":"vertical","data-state":Tx(b.open),"data-radix-menu-content":"",dir:g.dir,...v,...m,ref:R,style:{outline:"none",...m.style},onKeyDown:$(m.onKeyDown,j=>{const D=j.target.closest("[data-radix-menu-content]")===j.currentTarget,H=j.ctrlKey||j.altKey||j.metaKey,Z=j.key.length===1;D&&(j.key==="Tab"&&j.preventDefault(),!H&&Z&&K(j.key));const ie=N.current;if(j.target!==ie||!JR.includes(j.key))return;j.preventDefault();const ee=S().filter(Pe=>!Pe.disabled).map(Pe=>Pe.ref.current);lx.includes(j.key)&&ee.reverse(),wj(ee)}),onBlur:$(e.onBlur,j=>{j.currentTarget.contains(j.target)||(window.clearTimeout(M.current),_.current="")}),onPointerMove:$(e.onPointerMove,Ks(j=>{const F=j.target,D=O.current!==j.clientX;if(j.currentTarget.contains(F)&&D){const H=j.clientX>O.current?"right":"left";W.current=H,O.current=j.clientX}}))})})})})})})});hx.displayName=yt;var dj="MenuGroup",jf=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return u.jsx(Y.div,{role:"group",...r,ref:t})});jf.displayName=dj;var fj="MenuLabel",mx=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return u.jsx(Y.div,{...r,ref:t})});mx.displayName=fj;var Va="MenuItem",Gh="menu.itemSelect",_l=p.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,s=p.useRef(null),i=di(Va,e.__scopeMenu),a=Pf(Va,e.__scopeMenu),l=ae(t,s),c=p.useRef(!1),h=()=>{const f=s.current;if(!n&&f){const d=new CustomEvent(Gh,{bubbles:!0,cancelable:!0});f.addEventListener(Gh,y=>r==null?void 0:r(y),{once:!0}),Xd(f,d),d.defaultPrevented?c.current=!1:i.onClose()}};return u.jsx(gx,{...o,ref:l,disabled:n,onClick:$(e.onClick,h),onPointerDown:f=>{var d;(d=e.onPointerDown)==null||d.call(e,f),c.current=!0},onPointerUp:$(e.onPointerUp,f=>{var d;c.current||(d=f.currentTarget)==null||d.click()}),onKeyDown:$(e.onKeyDown,f=>{const d=a.searchRef.current!=="";n||d&&f.key===" "||qc.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})})});_l.displayName=Va;var gx=p.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...s}=e,i=Pf(Va,n),a=cx(n),l=p.useRef(null),c=ae(t,l),[h,f]=p.useState(!1),[d,y]=p.useState("");return p.useEffect(()=>{const w=l.current;w&&y((w.textContent??"").trim())},[s.children]),u.jsx(Ws.ItemSlot,{scope:n,disabled:r,textValue:o??d,children:u.jsx(ax,{asChild:!0,...a,focusable:!r,children:u.jsx(Y.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:c,onPointerMove:$(e.onPointerMove,Ks(w=>{r?i.onItemLeave(w):(i.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:$(e.onPointerLeave,Ks(w=>i.onItemLeave(w))),onFocus:$(e.onFocus,()=>f(!0)),onBlur:$(e.onBlur,()=>f(!1))})})})}),pj="MenuCheckboxItem",vx=p.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return u.jsx(Sx,{scope:e.__scopeMenu,checked:n,children:u.jsx(_l,{role:"menuitemcheckbox","aria-checked":Ha(n)?"mixed":n,...o,ref:t,"data-state":Mf(n),onSelect:$(o.onSelect,()=>r==null?void 0:r(Ha(n)?!0:!n),{checkForDefaultPrevented:!1})})})});vx.displayName=pj;var yx="MenuRadioGroup",[hj,mj]=Ir(yx,{value:void 0,onValueChange:()=>{}}),xx=p.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,s=Ke(r);return u.jsx(hj,{scope:e.__scopeMenu,value:n,onValueChange:s,children:u.jsx(jf,{...o,ref:t})})});xx.displayName=yx;var wx="MenuRadioItem",bx=p.forwardRef((e,t)=>{const{value:n,...r}=e,o=mj(wx,e.__scopeMenu),s=n===o.value;return u.jsx(Sx,{scope:e.__scopeMenu,checked:s,children:u.jsx(_l,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":Mf(s),onSelect:$(r.onSelect,()=>{var i;return(i=o.onValueChange)==null?void 0:i.call(o,n)},{checkForDefaultPrevented:!1})})})});bx.displayName=wx;var Tf="MenuItemIndicator",[Sx,gj]=Ir(Tf,{checked:!1}),Cx=p.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,s=gj(Tf,n);return u.jsx(Lt,{present:r||Ha(s.checked)||s.checked===!0,children:u.jsx(Y.span,{...o,ref:t,"data-state":Mf(s.checked)})})});Cx.displayName=Tf;var vj="MenuSeparator",Ex=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return u.jsx(Y.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});Ex.displayName=vj;var yj="MenuArrow",Nx=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Ml(n);return u.jsx(jy,{...o,...r,ref:t})});Nx.displayName=yj;var xj="MenuSub",[CM,kx]=Ir(xj),us="MenuSubTrigger",Px=p.forwardRef((e,t)=>{const n=Ar(us,e.__scopeMenu),r=di(us,e.__scopeMenu),o=kx(us,e.__scopeMenu),s=Pf(us,e.__scopeMenu),i=p.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:l}=s,c={__scopeMenu:e.__scopeMenu},h=p.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return p.useEffect(()=>h,[h]),p.useEffect(()=>{const f=a.current;return()=>{window.clearTimeout(f),l(null)}},[a,l]),u.jsx(Nf,{asChild:!0,...c,children:u.jsx(gx,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":Tx(n.open),...e,ref:ll(t,o.onTriggerChange),onClick:f=>{var d;(d=e.onClick)==null||d.call(e,f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:$(e.onPointerMove,Ks(f=>{s.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(s.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100))})),onPointerLeave:$(e.onPointerLeave,Ks(f=>{var y,w;h();const d=(y=n.content)==null?void 0:y.getBoundingClientRect();if(d){const m=(w=n.content)==null?void 0:w.dataset.side,b=m==="right",g=b?-5:5,v=d[b?"left":"right"],x=d[b?"right":"left"];s.onPointerGraceIntentChange({area:[{x:f.clientX+g,y:f.clientY},{x:v,y:d.top},{x,y:d.top},{x,y:d.bottom},{x:v,y:d.bottom}],side:m}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(f),f.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:$(e.onKeyDown,f=>{var y;const d=s.searchRef.current!=="";e.disabled||d&&f.key===" "||ej[r.dir].includes(f.key)&&(n.onOpenChange(!0),(y=n.content)==null||y.focus(),f.preventDefault())})})})});Px.displayName=us;var Rx="MenuSubContent",jx=p.forwardRef((e,t)=>{const n=fx(yt,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=Ar(yt,e.__scopeMenu),i=di(yt,e.__scopeMenu),a=kx(Rx,e.__scopeMenu),l=p.useRef(null),c=ae(t,l);return u.jsx(Ws.Provider,{scope:e.__scopeMenu,children:u.jsx(Lt,{present:r||s.open,children:u.jsx(Ws.Slot,{scope:e.__scopeMenu,children:u.jsx(Rf,{id:a.contentId,"aria-labelledby":a.triggerId,...o,ref:c,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:h=>{var f;i.isUsingKeyboardRef.current&&((f=l.current)==null||f.focus()),h.preventDefault()},onCloseAutoFocus:h=>h.preventDefault(),onFocusOutside:$(e.onFocusOutside,h=>{h.target!==a.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:$(e.onEscapeKeyDown,h=>{i.onClose(),h.preventDefault()}),onKeyDown:$(e.onKeyDown,h=>{var y;const f=h.currentTarget.contains(h.target),d=tj[i.dir].includes(h.key);f&&d&&(s.onOpenChange(!1),(y=a.trigger)==null||y.focus(),h.preventDefault())})})})})})});jx.displayName=Rx;function Tx(e){return e?"open":"closed"}function Ha(e){return e==="indeterminate"}function Mf(e){return Ha(e)?"indeterminate":e?"checked":"unchecked"}function wj(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function bj(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Sj(e,t,n){const o=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=bj(e,Math.max(s,0));o.length===1&&(i=i.filter(c=>c!==n));const l=i.find(c=>c.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function Cj(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,c=t[i].x,h=t[i].y;l>r!=h>r&&n<(c-a)*(r-l)/(h-l)+a&&(o=!o)}return o}function Ej(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Cj(n,t)}function Ks(e){return t=>t.pointerType==="mouse"?e(t):void 0}var Nj=dx,kj=Nf,Pj=px,Rj=hx,jj=jf,Tj=mx,Mj=_l,_j=vx,Ij=xx,Aj=bx,Oj=Cx,Dj=Ex,Lj=Nx,Fj=Px,zj=jx,_f="DropdownMenu",[$j,EM]=rr(_f,[ux]),Ye=ux(),[Uj,Mx]=$j(_f),_x=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:i,modal:a=!0}=e,l=Ye(t),c=p.useRef(null),[h=!1,f]=or({prop:o,defaultProp:s,onChange:i});return u.jsx(Uj,{scope:t,triggerId:Wn(),triggerRef:c,contentId:Wn(),open:h,onOpenChange:f,onOpenToggle:p.useCallback(()=>f(d=>!d),[f]),modal:a,children:u.jsx(Nj,{...l,open:h,onOpenChange:f,dir:r,modal:a,children:n})})};_x.displayName=_f;var Ix="DropdownMenuTrigger",Ax=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,s=Mx(Ix,n),i=Ye(n);return u.jsx(kj,{asChild:!0,...i,children:u.jsx(Y.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:ll(t,s.triggerRef),onPointerDown:$(e.onPointerDown,a=>{!r&&a.button===0&&a.ctrlKey===!1&&(s.onOpenToggle(),s.open||a.preventDefault())}),onKeyDown:$(e.onKeyDown,a=>{r||(["Enter"," "].includes(a.key)&&s.onOpenToggle(),a.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});Ax.displayName=Ix;var Bj="DropdownMenuPortal",Ox=e=>{const{__scopeDropdownMenu:t,...n}=e,r=Ye(t);return u.jsx(Pj,{...r,...n})};Ox.displayName=Bj;var Dx="DropdownMenuContent",Lx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Mx(Dx,n),s=Ye(n),i=p.useRef(!1);return u.jsx(Rj,{id:o.contentId,"aria-labelledby":o.triggerId,...s,...r,ref:t,onCloseAutoFocus:$(e.onCloseAutoFocus,a=>{var l;i.current||(l=o.triggerRef.current)==null||l.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:$(e.onInteractOutside,a=>{const l=a.detail.originalEvent,c=l.button===0&&l.ctrlKey===!0,h=l.button===2||c;(!o.modal||h)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Lx.displayName=Dx;var Vj="DropdownMenuGroup",Hj=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(jj,{...o,...r,ref:t})});Hj.displayName=Vj;var Wj="DropdownMenuLabel",Fx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Tj,{...o,...r,ref:t})});Fx.displayName=Wj;var Kj="DropdownMenuItem",zx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Mj,{...o,...r,ref:t})});zx.displayName=Kj;var Qj="DropdownMenuCheckboxItem",$x=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(_j,{...o,...r,ref:t})});$x.displayName=Qj;var Gj="DropdownMenuRadioGroup",Yj=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Ij,{...o,...r,ref:t})});Yj.displayName=Gj;var Xj="DropdownMenuRadioItem",Ux=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Aj,{...o,...r,ref:t})});Ux.displayName=Xj;var qj="DropdownMenuItemIndicator",Bx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Oj,{...o,...r,ref:t})});Bx.displayName=qj;var Zj="DropdownMenuSeparator",Vx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Dj,{...o,...r,ref:t})});Vx.displayName=Zj;var Jj="DropdownMenuArrow",eT=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Lj,{...o,...r,ref:t})});eT.displayName=Jj;var tT="DropdownMenuSubTrigger",Hx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(Fj,{...o,...r,ref:t})});Hx.displayName=tT;var nT="DropdownMenuSubContent",Wx=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ye(n);return u.jsx(zj,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Wx.displayName=nT;var rT=_x,oT=Ax,sT=Ox,Kx=Lx,Qx=Fx,Gx=zx,Yx=$x,Xx=Ux,qx=Bx,Zx=Vx,Jx=Hx,ew=Wx;const iT=rT,aT=oT,lT=p.forwardRef(({className:e,inset:t,children:n,...r},o)=>u.jsxs(Jx,{ref:o,className:V("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,u.jsx(kC,{className:"ml-auto h-4 w-4"})]}));lT.displayName=Jx.displayName;const uT=p.forwardRef(({className:e,...t},n)=>u.jsx(ew,{ref:n,className:V("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));uT.displayName=ew.displayName;const tw=p.forwardRef(({className:e,sideOffset:t=4,...n},r)=>u.jsx(sT,{children:u.jsx(Kx,{ref:r,sideOffset:t,className:V("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));tw.displayName=Kx.displayName;const Zc=p.forwardRef(({className:e,inset:t,...n},r)=>u.jsx(Gx,{ref:r,className:V("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));Zc.displayName=Gx.displayName;const cT=p.forwardRef(({className:e,children:t,checked:n,...r},o)=>u.jsxs(Yx,{ref:o,className:V("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(qx,{children:u.jsx(NC,{className:"h-4 w-4"})})}),t]}));cT.displayName=Yx.displayName;const dT=p.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(Xx,{ref:r,className:V("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(qx,{children:u.jsx(PC,{className:"h-2 w-2 fill-current"})})}),t]}));dT.displayName=Xx.displayName;const fT=p.forwardRef(({className:e,inset:t,...n},r)=>u.jsx(Qx,{ref:r,className:V("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));fT.displayName=Qx.displayName;const pT=p.forwardRef(({className:e,...t},n)=>u.jsx(Zx,{ref:n,className:V("-mx-1 my-1 h-px bg-muted",e),...t}));pT.displayName=Zx.displayName;const hT=Uo("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function ln({className:e,variant:t,...n}){return u.jsx("div",{className:V(hT({variant:t}),e),...n})}function mT({onShowAlerts:e,onShowSettings:t,title:n="Dashboard Home"}){return u.jsx("header",{className:"border-b border-border bg-card p-4",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center gap-4",children:[u.jsx(Q0,{className:"text-muted-foreground hover:text-primary"}),u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx(OC,{className:"h-6 w-6 text-primary"}),u.jsx("h1",{className:"text-xl font-bold text-foreground",children:"AI Security Hub"})]})]}),u.jsx("div",{className:"flex-1 text-center",children:u.jsx("h2",{className:"text-lg font-semibold text-foreground",children:n})}),u.jsxs("div",{className:"flex items-center gap-4",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("div",{className:"w-3 h-3 bg-green-400 rounded-full animate-pulse-blue"}),u.jsx(ln,{variant:"outline",className:"border-green-400 text-green-400",children:"System Online"})]}),u.jsxs(he,{variant:"ghost",size:"icon",onClick:e,className:"text-muted-foreground hover:text-primary relative",children:[u.jsx(ef,{className:"h-5 w-5"}),u.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"})]}),u.jsxs(iT,{children:[u.jsx(aT,{asChild:!0,children:u.jsx(he,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:text-primary",children:u.jsx(ih,{className:"h-5 w-5"})})}),u.jsxs(tw,{className:"bg-popover border-border",align:"end",children:[u.jsxs(Zc,{onClick:t,className:"text-popover-foreground hover:bg-accent hover:text-primary cursor-pointer",children:[u.jsx(qv,{className:"mr-2 h-4 w-4"}),"Settings"]}),u.jsxs(Zc,{className:"text-popover-foreground hover:bg-accent hover:text-primary",children:[u.jsx(ih,{className:"mr-2 h-4 w-4"}),"Profile"]})]})]})]})]})})}const xt=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:V("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));xt.displayName="Card";const Or=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:V("flex flex-col space-y-1.5 p-6",e),...t}));Or.displayName="CardHeader";const Dr=p.forwardRef(({className:e,...t},n)=>u.jsx("h3",{ref:n,className:V("text-2xl font-semibold leading-none tracking-tight",e),...t}));Dr.displayName="CardTitle";const gT=p.forwardRef(({className:e,...t},n)=>u.jsx("p",{ref:n,className:V("text-sm text-muted-foreground",e),...t}));gT.displayName="CardDescription";const wt=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:V("p-6 pt-0",e),...t}));wt.displayName="CardContent";const vT=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:V("flex items-center p-6 pt-0",e),...t}));vT.displayName="CardFooter";function yT(e,t=[]){let n=[];function r(s,i){const a=p.createContext(i),l=n.length;n=[...n,i];function c(f){const{scope:d,children:y,...w}=f,m=(d==null?void 0:d[e][l])||a,b=p.useMemo(()=>w,Object.values(w));return u.jsx(m.Provider,{value:b,children:y})}function h(f,d){const y=(d==null?void 0:d[e][l])||a,w=p.useContext(y);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return c.displayName=s+"Provider",[c,h]}const o=()=>{const s=n.map(i=>p.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,xT(o,...t)]}function xT(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(s)[`__scope${c}`];return{...a,...f}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var If="Progress",Af=100,[wT,NM]=yT(If),[bT,ST]=wT(If),nw=p.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:s=CT,...i}=e;(o||o===0)&&!Yh(o)&&console.error(ET(`${o}`,"Progress"));const a=Yh(o)?o:Af;r!==null&&!Xh(r,a)&&console.error(NT(`${r}`,"Progress"));const l=Xh(r,a)?r:null,c=Wa(l)?s(l,a):void 0;return u.jsx(bT,{scope:n,value:l,max:a,children:u.jsx(Y.div,{"aria-valuemax":a,"aria-valuemin":0,"aria-valuenow":Wa(l)?l:void 0,"aria-valuetext":c,role:"progressbar","data-state":sw(l,a),"data-value":l??void 0,"data-max":a,...i,ref:t})})});nw.displayName=If;var rw="ProgressIndicator",ow=p.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=ST(rw,n);return u.jsx(Y.div,{"data-state":sw(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});ow.displayName=rw;function CT(e,t){return`${Math.round(e/t*100)}%`}function sw(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Wa(e){return typeof e=="number"}function Yh(e){return Wa(e)&&!isNaN(e)&&e>0}function Xh(e,t){return Wa(e)&&!isNaN(e)&&e<=t&&e>=0}function ET(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Af}\`.`}function NT(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Af} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var iw=nw,kT=ow;const aw=p.forwardRef(({className:e,value:t,...n},r)=>u.jsx(iw,{ref:r,className:V("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...n,children:u.jsx(kT,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));aw.displayName=iw.displayName;function PT({item:e,onClose:t}){const n=[{name:"Person",confidence:95},{name:"Cat",confidence:89},{name:"Furniture",confidence:76},{name:"Plant",confidence:62}];return u.jsx("div",{className:"fixed inset-y-0 right-0 w-96 bg-card border-l border-border z-50 animate-slide-in",children:u.jsxs("div",{className:"h-full flex flex-col",children:[u.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[u.jsx("h3",{className:"text-lg font-semibold text-card-foreground",children:"Event Details"}),u.jsx(he,{variant:"ghost",size:"icon",onClick:t,className:"text-muted-foreground hover:text-card-foreground",children:u.jsx(si,{className:"h-5 w-5"})})]}),u.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[u.jsx(xt,{className:"bg-secondary border-border",children:u.jsx(wt,{className:"p-0",children:u.jsx("img",{src:e.thumbnail||"https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400&h=300&fit=crop",alt:"Event capture",className:"w-full h-48 object-cover rounded-t-lg"})})}),u.jsxs("div",{className:"space-y-3",children:[u.jsxs("div",{children:[u.jsx("h4",{className:"text-card-foreground font-medium mb-1",children:"Event Time"}),u.jsx("p",{className:"text-muted-foreground font-mono text-sm",children:e.timestamp||e.time})]}),u.jsxs("div",{children:[u.jsx("h4",{className:"text-card-foreground font-medium mb-1",children:"AI Description"}),u.jsx("p",{className:"text-muted-foreground text-sm",children:e.description})]})]}),u.jsx(xt,{className:"bg-secondary border-border",children:u.jsxs(wt,{className:"p-4",children:[u.jsx("h4",{className:"text-card-foreground font-medium mb-3",children:"Detected Objects"}),u.jsx("div",{className:"space-y-3",children:n.map((r,o)=>u.jsxs("div",{className:"space-y-1",children:[u.jsxs("div",{className:"flex justify-between text-sm",children:[u.jsx("span",{className:"text-secondary-foreground",children:r.name}),u.jsxs("span",{className:"text-primary",children:[r.confidence,"%"]})]}),u.jsx(aw,{value:r.confidence,className:"h-2 bg-muted"})]},o))})]})}),u.jsxs("div",{children:[u.jsx("h4",{className:"text-card-foreground font-medium mb-2",children:"Classification"}),u.jsxs("div",{className:"flex gap-2",children:[u.jsx(ln,{variant:"secondary",className:"bg-yellow-600 text-white",children:"Motion Event"}),u.jsx(ln,{variant:"outline",className:"border-primary text-primary",children:"Normal Activity"})]})]})]}),u.jsx("div",{className:"border-t border-border p-4",children:u.jsxs("div",{className:"flex gap-2",children:[u.jsxs(he,{className:"flex-1 bg-primary hover:bg-primary/90 text-primary-foreground",children:[u.jsx(DC,{className:"h-4 w-4 mr-2"}),"Mark Important"]}),u.jsx(he,{variant:"outline",className:"border-border text-muted-foreground hover:border-primary hover:text-primary",children:u.jsx(Gv,{className:"h-4 w-4"})}),u.jsx(he,{variant:"outline",className:"border-border text-muted-foreground hover:border-primary hover:text-primary",children:u.jsx(IC,{className:"h-4 w-4"})})]})})]})})}function RT({isOpen:e,onClose:t}){if(!e)return null;const n=[{id:1,time:"2 min ago",type:"Intrusion",severity:"high",message:"Unauthorized person detected in backyard",image:"https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=60&h=60&fit=crop"},{id:2,time:"15 min ago",type:"Animal",severity:"low",message:"Cat detected in kitchen area",image:"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=60&h=60&fit=crop"},{id:3,time:"1 hour ago",type:"Object",severity:"medium",message:"Package left unattended at front door",image:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=60&h=60&fit=crop"}],r=o=>{switch(o){case"high":return"bg-red-600";case"medium":return"bg-yellow-600";case"low":return"bg-green-600";default:return"bg-gray-600"}};return u.jsxs("div",{className:"fixed inset-0 z-50",children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:t}),u.jsx("div",{className:"absolute inset-y-0 right-0 w-96 bg-card border-l border-border animate-slide-in",children:u.jsxs("div",{className:"h-full flex flex-col",children:[u.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Aa,{className:"h-5 w-5 text-red-400"}),u.jsx("h3",{className:"text-lg font-semibold text-card-foreground",children:"Recent Alerts"})]}),u.jsx(he,{variant:"ghost",size:"icon",onClick:t,className:"text-muted-foreground hover:text-card-foreground",children:u.jsx(si,{className:"h-5 w-5"})})]}),u.jsx("div",{className:"p-4 border-b border-border",children:u.jsxs("div",{className:"flex gap-2",children:[u.jsxs(he,{size:"sm",className:"flex-1 bg-primary hover:bg-primary/90 text-primary-foreground",children:[u.jsx(EC,{className:"h-4 w-4 mr-2"}),"Acknowledge All"]}),u.jsxs(he,{variant:"outline",size:"sm",className:"border-border text-muted-foreground hover:border-primary hover:text-primary",children:[u.jsx(LC,{className:"h-4 w-4 mr-2"}),"Mute"]})]})}),u.jsx("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:n.map(o=>u.jsx("div",{className:"bg-secondary rounded-lg p-4 border-l-4 border-l-red-400 hover:bg-accent transition-colors",children:u.jsxs("div",{className:"flex items-start gap-3",children:[u.jsx("img",{src:o.image,alt:"Alert thumbnail",className:"w-12 h-12 rounded-lg object-cover"}),u.jsxs("div",{className:"flex-1",children:[u.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[u.jsx(ln,{className:`text-white ${r(o.severity)}`,children:o.type}),u.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[u.jsx(ml,{className:"h-3 w-3"}),o.time]})]}),u.jsx("p",{className:"text-secondary-foreground text-sm",children:o.message})]})]})},o.id))}),u.jsx("div",{className:"border-t border-border p-4",children:u.jsx(he,{variant:"outline",className:"w-full border-border text-muted-foreground hover:border-primary hover:text-primary",children:"View All Alerts History"})})]})})]})}var Of="Tabs",[jT,kM]=rr(Of,[Tl]),lw=Tl(),[TT,Df]=jT(Of),uw=p.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:s,orientation:i="horizontal",dir:a,activationMode:l="automatic",...c}=e,h=Rl(a),[f,d]=or({prop:r,onChange:o,defaultProp:s});return u.jsx(TT,{scope:n,baseId:Wn(),value:f,onValueChange:d,orientation:i,dir:h,activationMode:l,children:u.jsx(Y.div,{dir:h,"data-orientation":i,...c,ref:t})})});uw.displayName=Of;var cw="TabsList",dw=p.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,s=Df(cw,n),i=lw(n);return u.jsx(ix,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:r,children:u.jsx(Y.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});dw.displayName=cw;var fw="TabsTrigger",pw=p.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...s}=e,i=Df(fw,n),a=lw(n),l=gw(i.baseId,r),c=vw(i.baseId,r),h=r===i.value;return u.jsx(ax,{asChild:!0,...a,focusable:!o,active:h,children:u.jsx(Y.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":c,"data-state":h?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...s,ref:t,onMouseDown:$(e.onMouseDown,f=>{!o&&f.button===0&&f.ctrlKey===!1?i.onValueChange(r):f.preventDefault()}),onKeyDown:$(e.onKeyDown,f=>{[" ","Enter"].includes(f.key)&&i.onValueChange(r)}),onFocus:$(e.onFocus,()=>{const f=i.activationMode!=="manual";!h&&!o&&f&&i.onValueChange(r)})})})});pw.displayName=fw;var hw="TabsContent",mw=p.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:s,...i}=e,a=Df(hw,n),l=gw(a.baseId,r),c=vw(a.baseId,r),h=r===a.value,f=p.useRef(h);return p.useEffect(()=>{const d=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(d)},[]),u.jsx(Lt,{present:o||h,children:({present:d})=>u.jsx(Y.div,{"data-state":h?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":l,hidden:!d,id:c,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:d&&s})})});mw.displayName=hw;function gw(e,t){return`${e}-trigger-${t}`}function vw(e,t){return`${e}-content-${t}`}var MT=uw,yw=dw,xw=pw,ww=mw;const _T=MT,bw=p.forwardRef(({className:e,...t},n)=>u.jsx(yw,{ref:n,className:V("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));bw.displayName=yw.displayName;const aa=p.forwardRef(({className:e,...t},n)=>u.jsx(xw,{ref:n,className:V("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));aa.displayName=xw.displayName;const la=p.forwardRef(({className:e,...t},n)=>u.jsx(ww,{ref:n,className:V("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));la.displayName=ww.displayName;function Sw(e,[t,n]){return Math.min(n,Math.max(t,e))}function Cw(e){const t=p.useRef({value:e,previous:e});return p.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Ew=["PageUp","PageDown"],Nw=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],kw={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Ko="Slider",[Jc,IT,AT]=ul(Ko),[Pw,PM]=rr(Ko,[AT]),[OT,Il]=Pw(Ko),Rw=p.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:s=1,orientation:i="horizontal",disabled:a=!1,minStepsBetweenThumbs:l=0,defaultValue:c=[r],value:h,onValueChange:f=()=>{},onValueCommit:d=()=>{},inverted:y=!1,form:w,...m}=e,b=p.useRef(new Set),g=p.useRef(0),x=i==="horizontal"?DT:LT,[S=[],C]=or({prop:h,defaultProp:c,onChange:L=>{var W;(W=[...b.current][g.current])==null||W.focus(),f(L)}}),P=p.useRef(S);function N(L){const A=BT(S,L);_(L,A)}function R(L){_(L,g.current)}function M(){const L=P.current[g.current];S[g.current]!==L&&d(S)}function _(L,A,{commit:W}={commit:!1}){const O=KT(s),G=QT(Math.round((L-r)/s)*s+r,O),B=Sw(G,[r,o]);C((K=[])=>{const k=$T(K,B,A);if(WT(k,l*s)){g.current=k.indexOf(B);const j=String(k)!==String(K);return j&&W&&d(k),j?k:K}else return K})}return u.jsx(OT,{scope:e.__scopeSlider,name:n,disabled:a,min:r,max:o,valueIndexToChangeRef:g,thumbs:b.current,values:S,orientation:i,form:w,children:u.jsx(Jc.Provider,{scope:e.__scopeSlider,children:u.jsx(Jc.Slot,{scope:e.__scopeSlider,children:u.jsx(x,{"aria-disabled":a,"data-disabled":a?"":void 0,...m,ref:t,onPointerDown:$(m.onPointerDown,()=>{a||(P.current=S)}),min:r,max:o,inverted:y,onSlideStart:a?void 0:N,onSlideMove:a?void 0:R,onSlideEnd:a?void 0:M,onHomeKeyDown:()=>!a&&_(r,0,{commit:!0}),onEndKeyDown:()=>!a&&_(o,S.length-1,{commit:!0}),onStepKeyDown:({event:L,direction:A})=>{if(!a){const G=Ew.includes(L.key)||L.shiftKey&&Nw.includes(L.key)?10:1,B=g.current,K=S[B],k=s*G*A;_(K+k,B,{commit:!0})}}})})})})});Rw.displayName=Ko;var[jw,Tw]=Pw(Ko,{startEdge:"left",endEdge:"right",size:"width",direction:1}),DT=p.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:s,onSlideStart:i,onSlideMove:a,onSlideEnd:l,onStepKeyDown:c,...h}=e,[f,d]=p.useState(null),y=ae(t,x=>d(x)),w=p.useRef(),m=Rl(o),b=m==="ltr",g=b&&!s||!b&&s;function v(x){const S=w.current||f.getBoundingClientRect(),C=[0,S.width],N=Lf(C,g?[n,r]:[r,n]);return w.current=S,N(x-S.left)}return u.jsx(jw,{scope:e.__scopeSlider,startEdge:g?"left":"right",endEdge:g?"right":"left",direction:g?1:-1,size:"width",children:u.jsx(Mw,{dir:m,"data-orientation":"horizontal",...h,ref:y,style:{...h.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:x=>{const S=v(x.clientX);i==null||i(S)},onSlideMove:x=>{const S=v(x.clientX);a==null||a(S)},onSlideEnd:()=>{w.current=void 0,l==null||l()},onStepKeyDown:x=>{const C=kw[g?"from-left":"from-right"].includes(x.key);c==null||c({event:x,direction:C?-1:1})}})})}),LT=p.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:s,onSlideMove:i,onSlideEnd:a,onStepKeyDown:l,...c}=e,h=p.useRef(null),f=ae(t,h),d=p.useRef(),y=!o;function w(m){const b=d.current||h.current.getBoundingClientRect(),g=[0,b.height],x=Lf(g,y?[r,n]:[n,r]);return d.current=b,x(m-b.top)}return u.jsx(jw,{scope:e.__scopeSlider,startEdge:y?"bottom":"top",endEdge:y?"top":"bottom",size:"height",direction:y?1:-1,children:u.jsx(Mw,{"data-orientation":"vertical",...c,ref:f,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const b=w(m.clientY);s==null||s(b)},onSlideMove:m=>{const b=w(m.clientY);i==null||i(b)},onSlideEnd:()=>{d.current=void 0,a==null||a()},onStepKeyDown:m=>{const g=kw[y?"from-bottom":"from-top"].includes(m.key);l==null||l({event:m,direction:g?-1:1})}})})}),Mw=p.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:s,onHomeKeyDown:i,onEndKeyDown:a,onStepKeyDown:l,...c}=e,h=Il(Ko,n);return u.jsx(Y.span,{...c,ref:t,onKeyDown:$(e.onKeyDown,f=>{f.key==="Home"?(i(f),f.preventDefault()):f.key==="End"?(a(f),f.preventDefault()):Ew.concat(Nw).includes(f.key)&&(l(f),f.preventDefault())}),onPointerDown:$(e.onPointerDown,f=>{const d=f.target;d.setPointerCapture(f.pointerId),f.preventDefault(),h.thumbs.has(d)?d.focus():r(f)}),onPointerMove:$(e.onPointerMove,f=>{f.target.hasPointerCapture(f.pointerId)&&o(f)}),onPointerUp:$(e.onPointerUp,f=>{const d=f.target;d.hasPointerCapture(f.pointerId)&&(d.releasePointerCapture(f.pointerId),s(f))})})}),_w="SliderTrack",Iw=p.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Il(_w,n);return u.jsx(Y.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});Iw.displayName=_w;var ed="SliderRange",Aw=p.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Il(ed,n),s=Tw(ed,n),i=p.useRef(null),a=ae(t,i),l=o.values.length,c=o.values.map(d=>Dw(d,o.min,o.max)),h=l>1?Math.min(...c):0,f=100-Math.max(...c);return u.jsx(Y.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:a,style:{...e.style,[s.startEdge]:h+"%",[s.endEdge]:f+"%"}})});Aw.displayName=ed;var td="SliderThumb",Ow=p.forwardRef((e,t)=>{const n=IT(e.__scopeSlider),[r,o]=p.useState(null),s=ae(t,a=>o(a)),i=p.useMemo(()=>r?n().findIndex(a=>a.ref.current===r):-1,[n,r]);return u.jsx(FT,{...e,ref:s,index:i})}),FT=p.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...s}=e,i=Il(td,n),a=Tw(td,n),[l,c]=p.useState(null),h=ae(t,v=>c(v)),f=l?i.form||!!l.closest("form"):!0,d=uf(l),y=i.values[r],w=y===void 0?0:Dw(y,i.min,i.max),m=UT(r,i.values.length),b=d==null?void 0:d[a.size],g=b?VT(b,w,a.direction):0;return p.useEffect(()=>{if(l)return i.thumbs.add(l),()=>{i.thumbs.delete(l)}},[l,i.thumbs]),u.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[a.startEdge]:`calc(${w}% + ${g}px)`},children:[u.jsx(Jc.ItemSlot,{scope:e.__scopeSlider,children:u.jsx(Y.span,{role:"slider","aria-label":e["aria-label"]||m,"aria-valuemin":i.min,"aria-valuenow":y,"aria-valuemax":i.max,"aria-orientation":i.orientation,"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,tabIndex:i.disabled?void 0:0,...s,ref:h,style:y===void 0?{display:"none"}:e.style,onFocus:$(e.onFocus,()=>{i.valueIndexToChangeRef.current=r})})}),f&&u.jsx(zT,{name:o??(i.name?i.name+(i.values.length>1?"[]":""):void 0),form:i.form,value:y},r)]})});Ow.displayName=td;var zT=e=>{const{value:t,...n}=e,r=p.useRef(null),o=Cw(t);return p.useEffect(()=>{const s=r.current,i=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(i,"value").set;if(o!==t&&l){const c=new Event("input",{bubbles:!0});l.call(s,t),s.dispatchEvent(c)}},[o,t]),u.jsx("input",{style:{display:"none"},...n,ref:r,defaultValue:t})};function $T(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,s)=>o-s)}function Dw(e,t,n){const s=100/(n-t)*(e-t);return Sw(s,[0,100])}function UT(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function BT(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function VT(e,t,n){const r=e/2,s=Lf([0,50],[0,r]);return(r-s(t)*n)*n}function HT(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function WT(e,t){if(t>0){const n=HT(e);return Math.min(...n)>=t}return!0}function Lf(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function KT(e){return(String(e).split(".")[1]||"").length}function QT(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var Lw=Rw,GT=Iw,YT=Aw,XT=Ow;const cs=p.forwardRef(({className:e,...t},n)=>u.jsxs(Lw,{ref:n,className:V("relative flex w-full touch-none select-none items-center",e),...t,children:[u.jsx(GT,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:u.jsx(YT,{className:"absolute h-full bg-primary"})}),u.jsx(XT,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));cs.displayName=Lw.displayName;var Ff="Switch",[qT,RM]=rr(Ff),[ZT,JT]=qT(Ff),Fw=p.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:o,defaultChecked:s,required:i,disabled:a,value:l="on",onCheckedChange:c,form:h,...f}=e,[d,y]=p.useState(null),w=ae(t,x=>y(x)),m=p.useRef(!1),b=d?h||!!d.closest("form"):!0,[g=!1,v]=or({prop:o,defaultProp:s,onChange:c});return u.jsxs(ZT,{scope:n,checked:g,disabled:a,children:[u.jsx(Y.button,{type:"button",role:"switch","aria-checked":g,"aria-required":i,"data-state":Uw(g),"data-disabled":a?"":void 0,disabled:a,value:l,...f,ref:w,onClick:$(e.onClick,x=>{v(S=>!S),b&&(m.current=x.isPropagationStopped(),m.current||x.stopPropagation())})}),b&&u.jsx(eM,{control:d,bubbles:!m.current,name:r,value:l,checked:g,required:i,disabled:a,form:h,style:{transform:"translateX(-100%)"}})]})});Fw.displayName=Ff;var zw="SwitchThumb",$w=p.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=JT(zw,n);return u.jsx(Y.span,{"data-state":Uw(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});$w.displayName=zw;var eM=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,s=p.useRef(null),i=Cw(n),a=uf(t);return p.useEffect(()=>{const l=s.current,c=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(c,"checked").set;if(i!==n&&f){const d=new Event("click",{bubbles:r});f.call(l,n),l.dispatchEvent(d)}},[i,n,r]),u.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:s,style:{...e.style,...a,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function Uw(e){return e?"checked":"unchecked"}var Bw=Fw,tM=$w;const ds=p.forwardRef(({className:e,...t},n)=>u.jsx(Bw,{className:V("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:n,children:u.jsx(tM,{className:V("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));ds.displayName=Bw.displayName;var nM="Label",Vw=p.forwardRef((e,t)=>u.jsx(Y.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));Vw.displayName=nM;var Hw=Vw;const rM=Uo("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),tn=p.forwardRef(({className:e,...t},n)=>u.jsx(Hw,{ref:n,className:V(rM(),e),...t}));tn.displayName=Hw.displayName;function oM({isOpen:e,onClose:t}){return e?u.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:t}),u.jsxs(xt,{className:"relative w-full max-w-2xl max-h-[80vh] bg-gray-900 border-gray-800 animate-scale-in",children:[u.jsx(Or,{className:"border-b border-gray-800",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(Dr,{className:"text-white",children:"System Settings"}),u.jsx(he,{variant:"ghost",size:"icon",onClick:t,className:"text-gray-400 hover:text-white",children:u.jsx(si,{className:"h-5 w-5"})})]})}),u.jsx(wt,{className:"p-0",children:u.jsxs(_T,{defaultValue:"capture",className:"w-full",children:[u.jsxs(bw,{className:"grid w-full grid-cols-3 bg-secondary",children:[u.jsx(aa,{value:"capture",className:"text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:"Capture"}),u.jsx(aa,{value:"sensors",className:"text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:"Sensors"}),u.jsx(aa,{value:"notifications",className:"text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:"Notifications"})]}),u.jsxs("div",{className:"p-6 max-h-96 overflow-y-auto",children:[u.jsxs(la,{value:"capture",className:"space-y-6 mt-0",children:[u.jsxs("div",{className:"space-y-3",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Capture Interval (seconds)"}),u.jsx(cs,{defaultValue:[30],max:300,min:5,step:5,className:"w-full"}),u.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[u.jsx("span",{children:"5s"}),u.jsx("span",{children:"Current: 30s"}),u.jsx("span",{children:"300s"})]})]}),u.jsxs("div",{className:"space-y-3",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Image Quality"}),u.jsx(cs,{defaultValue:[80],max:100,min:20,step:10,className:"w-full"}),u.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[u.jsx("span",{children:"Low"}),u.jsx("span",{children:"Current: High"}),u.jsx("span",{children:"Ultra"})]})]})]}),u.jsxs(la,{value:"sensors",className:"space-y-6 mt-0",children:[u.jsxs("div",{className:"space-y-3",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Motion Sensitivity"}),u.jsx(cs,{defaultValue:[70],max:100,min:10,step:10,className:"w-full"}),u.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[u.jsx("span",{children:"Low"}),u.jsx("span",{children:"Current: 70%"}),u.jsx("span",{children:"High"})]})]}),u.jsxs("div",{className:"space-y-3",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Audio Detection"}),u.jsx(cs,{defaultValue:[50],max:100,min:0,step:10,className:"w-full"}),u.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[u.jsx("span",{children:"Off"}),u.jsx("span",{children:"Current: 50%"}),u.jsx("span",{children:"Max"})]})]})]}),u.jsx(la,{value:"notifications",className:"space-y-6 mt-0",children:u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Telegram Notifications"}),u.jsx(ds,{defaultChecked:!0})]}),u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"WhatsApp Alerts"}),u.jsx(ds,{})]}),u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Email Reports"}),u.jsx(ds,{defaultChecked:!0})]}),u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(tn,{className:"text-card-foreground text-sm font-medium",children:"Push Notifications"}),u.jsx(ds,{defaultChecked:!0})]})]})})]})]})}),u.jsxs("div",{className:"border-t border-border p-4 flex gap-2",children:[u.jsx(he,{onClick:t,variant:"outline",className:"flex-1 border-border text-muted-foreground hover:border-primary hover:text-primary",children:"Cancel"}),u.jsxs(he,{onClick:t,className:"flex-1 bg-primary hover:bg-primary/90 text-primary-foreground",children:[u.jsx(AC,{className:"h-4 w-4 mr-2"}),"Save Changes"]})]})]})]}):null}function Ww({children:e,title:t="Dashboard Home"}){const[n,r]=p.useState(null),[o,s]=p.useState(!1),[i,a]=p.useState(!1);return u.jsx(W0,{children:u.jsxs("div",{className:"min-h-screen flex w-full bg-background",children:[u.jsx(FR,{}),u.jsxs("div",{className:"flex-1 flex flex-col",children:[u.jsx(mT,{onShowAlerts:()=>s(!0),onShowSettings:()=>a(!0),title:t}),u.jsxs("main",{className:"flex-1 p-6 relative",children:[typeof e=="function"?e(r):e,n&&u.jsx(PT,{item:n,onClose:()=>r(null)}),u.jsx(RT,{isOpen:o,onClose:()=>s(!1)}),u.jsx(oM,{isOpen:i,onClose:()=>a(!1)})]})]})]})})}const Wr="http://localhost:5000",wr={async getSystemHealth(){try{return{status:(await fetch(`${Wr}/api/logs`)).ok?"healthy":"unhealthy",uptime:"2d 14h 32m",cpu:45,memory:62,storage:78,lastUpdate:new Date().toISOString()}}catch{return{status:"unhealthy",uptime:"0m",cpu:0,memory:0,storage:0,lastUpdate:new Date().toISOString()}}},async getAlerts(){try{return(await(await fetch(`${Wr}/api/logs`)).json()).slice(-5).map((r,o)=>{var s;return{id:`alert_${o}`,type:"info",message:((s=r.summary)==null?void 0:s.substring(0,100))+"..."||"Activity detected",timestamp:r.interval_start||new Date().toISOString(),severity:"medium",images:r.images||[]}})}catch(e){return console.error("Failed to fetch alerts:",e),[]}},async getRecentCaptures(){try{const t=await(await fetch(`${Wr}/api/logs`)).json(),n=[];return t.forEach(r=>{r.images&&Array.isArray(r.images)&&r.images.forEach(o=>{n.push({id:o.replace(".jpg",""),filename:o,timestamp:r.interval_start||new Date().toISOString(),thumbnail:`/captured_images/${o}`,fullSize:`/captured_images/${o}`,location:"Security Camera",tags:["motion"],summary:r.summary||"Activity detected"})})}),n.sort((r,o)=>new Date(o.timestamp).getTime()-new Date(r.timestamp).getTime()).slice(0,20)}catch(e){return console.error("Failed to fetch captures:",e),[]}},async getSensorEvents(){try{return(await(await fetch(`${Wr}/api/logs`)).json()).slice(-10).map((r,o)=>{var s;return{id:`event_${o}`,sensor:"Security Camera System",event:((s=r.summary)==null?void 0:s.substring(0,50))+"..."||"Activity detected",timestamp:r.interval_start||new Date().toISOString(),status:"resolved",images:r.images||[]}}).reverse()}catch(e){return console.error("Failed to fetch sensor events:",e),[]}},async searchObjects(e){try{const n=await(await fetch(`${Wr}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:`Find images with ${e}`})})).json();return(n.images_to_show||[]).map(o=>({id:o.replace(".jpg",""),filename:o,timestamp:new Date().toISOString(),confidence:.85,objects:[e],thumbnail:`/captured_images/${o}`,description:n.answer||"Object found in image"}))}catch(t){return console.error("Failed to search objects:",t),[]}},async chatWithAI(e){try{const n=await(await fetch(`${Wr}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e})})).json();return{answer:n.answer||"Sorry, I could not process your request.",images:n.images_to_show||[]}}catch(t){return console.error("Failed to chat with AI:",t),{answer:"Sorry, the AI service is currently unavailable.",images:[]}}}};function sM(){const{data:e,isLoading:t}=ho({queryKey:["systemHealth"],queryFn:wr.getSystemHealth,refetchInterval:1e4}),{data:n=[]}=ho({queryKey:["recentCaptures"],queryFn:wr.getRecentCaptures,refetchInterval:3e4}),{data:r=[]}=ho({queryKey:["sensorEvents"],queryFn:wr.getSensorEvents,refetchInterval:3e4}),o=a=>{const l=new Date,c=new Date(a),h=l.getTime()-c.getTime(),f=Math.floor(h/(1e3*60));if(f<1)return"Just now";if(f<60)return`${f} min ago`;const d=Math.floor(f/60);return d<24?`${d}h ago`:`${Math.floor(d/24)}d ago`},s=n[0],i=r[0];return u.jsxs(xt,{className:"bg-card border-border hover:border-primary transition-colors",children:[u.jsx(Or,{children:u.jsxs(Dr,{className:"text-card-foreground flex items-center gap-2",children:[u.jsx(CC,{className:"h-5 w-5 text-primary"}),"System Health"]})}),u.jsx(wt,{className:"space-y-4",children:t?u.jsxs("div",{className:"flex items-center justify-center h-32",children:[u.jsx(oi,{className:"h-4 w-4 animate-spin text-muted-foreground"}),u.jsx("span",{className:"ml-2 text-muted-foreground text-sm",children:"Loading..."})]}):u.jsxs(u.Fragment,{children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center gap-2 text-muted-foreground",children:[u.jsx(hl,{className:"h-4 w-4"}),u.jsx("span",{children:"Last Capture"})]}),u.jsx("span",{className:"text-card-foreground font-mono",children:s?o(s.timestamp):"No data"})]}),u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center gap-2 text-muted-foreground",children:[u.jsx(ml,{className:"h-4 w-4"}),u.jsx("span",{children:"Last Sensor Event"})]}),u.jsx("span",{className:"text-card-foreground font-mono",children:i?o(i.timestamp):"No data"})]}),u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx("span",{className:"text-muted-foreground",children:"Overall Status"}),u.jsx(ln,{className:(e==null?void 0:e.status)==="healthy"?"bg-green-600 hover:bg-green-700":"bg-red-600 hover:bg-red-700",children:(e==null?void 0:e.status)==="healthy"?"All Systems Operational":"System Issues Detected"})]}),u.jsxs("div",{className:"pt-2 border-t border-border",children:[u.jsxs("div",{className:"flex justify-between text-sm",children:[u.jsx("span",{className:"text-muted-foreground",children:"CPU Usage"}),u.jsxs("span",{className:"text-card-foreground",children:[(e==null?void 0:e.cpu)||0,"%"]})]}),u.jsx("div",{className:"w-full bg-secondary rounded-full h-2 mt-1",children:u.jsx("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${(e==null?void 0:e.cpu)||0}%`}})})]})]})})]})}function iM(){const{data:e=[],isLoading:t}=ho({queryKey:["alerts"],queryFn:wr.getAlerts,refetchInterval:3e4}),r=(o=>{const s={"Motion Detection":{count:0,icon:hl,color:"bg-blue-600"},Activity:{count:0,icon:Aa,color:"bg-green-600"},Other:{count:0,icon:MC,color:"bg-gray-600"}};return o.forEach(i=>{var l;const a=((l=i.message)==null?void 0:l.toLowerCase())||"";a.includes("motion")||a.includes("movement")?s["Motion Detection"].count++:a.includes("activity")||a.includes("person")||a.includes("people")?s.Activity.count++:s.Other.count++}),Object.entries(s).map(([i,a])=>({type:i,...a}))})(e);return u.jsxs(xt,{className:"bg-card border-border hover:border-primary transition-colors",children:[u.jsx(Or,{children:u.jsxs(Dr,{className:"text-card-foreground flex items-center gap-2",children:[u.jsx(Aa,{className:"h-5 w-5 text-primary"}),"Alerts Overview"]})}),u.jsx(wt,{className:"space-y-4",children:t?u.jsxs("div",{className:"flex items-center justify-center h-32",children:[u.jsx(oi,{className:"h-4 w-4 animate-spin text-muted-foreground"}),u.jsx("span",{className:"ml-2 text-muted-foreground text-sm",children:"Loading alerts..."})]}):r.length===0?u.jsx("div",{className:"text-center text-muted-foreground py-8",children:u.jsx("p",{children:"No alerts available"})}):u.jsxs(u.Fragment,{children:[r.map(o=>u.jsxs("div",{className:"flex items-center justify-between p-3 bg-secondary rounded-lg hover:bg-accent transition-colors",children:[u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx("div",{className:`p-2 rounded-lg ${o.color}`,children:u.jsx(o.icon,{className:"h-4 w-4 text-white"})}),u.jsx("span",{className:"text-secondary-foreground font-medium",children:o.type})]}),u.jsx(ln,{variant:"outline",className:"border-border text-card-foreground",children:o.count})]},o.type)),u.jsx("div",{className:"pt-3 border-t border-border",children:u.jsxs("div",{className:"text-center",children:[u.jsx("span",{className:"text-2xl font-bold text-card-foreground",children:e.length}),u.jsx("p",{className:"text-muted-foreground text-sm",children:"Total alerts today"})]})})]})})]})}function aM({onSelectImage:e}){const t=s0(),{data:n=[],isLoading:r,error:o}=ho({queryKey:["recentCaptures"],queryFn:wr.getRecentCaptures,refetchInterval:3e4}),s=n.slice(0,4).map(i=>{var a;return{...i,timestamp:new Date(i.timestamp).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),description:((a=i.summary)==null?void 0:a.substring(0,50))+"..."||"Activity detected"}});return u.jsxs(xt,{className:"bg-card border-border",children:[u.jsxs(Or,{className:"flex flex-row items-center justify-between",children:[u.jsxs(Dr,{className:"text-card-foreground flex items-center gap-2",children:[u.jsx(Xv,{className:"h-5 w-5 text-primary"}),"Recent Captures"]}),u.jsx(he,{variant:"outline",size:"sm",className:"border-border text-muted-foreground hover:border-primary hover:text-primary",onClick:()=>t("/all-captures"),children:"View All"})]}),u.jsx(wt,{children:r?u.jsxs("div",{className:"flex items-center justify-center h-32",children:[u.jsx(oi,{className:"h-6 w-6 animate-spin text-muted-foreground"}),u.jsx("span",{className:"ml-2 text-muted-foreground",children:"Loading captures..."})]}):o?u.jsx("div",{className:"flex items-center justify-center h-32 text-muted-foreground",children:u.jsx("p",{children:"Failed to load captures. Please check if the backend is running."})}):s.length===0?u.jsx("div",{className:"flex items-center justify-center h-32 text-muted-foreground",children:u.jsx("p",{children:"No captures available yet."})}):u.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:s.map(i=>u.jsxs("div",{className:"cursor-pointer transition-all duration-300 hover:scale-105 group",onClick:()=>e(i),children:[u.jsxs("div",{className:"relative",children:[u.jsx("img",{src:i.thumbnail,alt:i.description,className:"w-full h-24 object-cover rounded-lg",onError:a=>{a.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NyA2NUw5MyA3MUwxMDUgNTlMMTIzIDc3SDE2M1Y5N0g0N1Y3N0w2NSA1OUw4NyA2NVoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"}}),u.jsx("div",{className:"absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded",children:i.timestamp}),u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg"})]}),u.jsx("p",{className:"text-xs text-muted-foreground mt-1 truncate",children:i.description})]},i.id))})})]})}function lM({onSelectEvent:e}){const{data:t=[],isLoading:n}=ho({queryKey:["sensorEvents"],queryFn:wr.getSensorEvents,refetchInterval:3e4}),r=t.slice(0,5).map(o=>({id:o.id,time:new Date(o.timestamp).toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),type:"camera",icon:hl,thumbnail:o.images&&o.images.length>0?`/captured_images/${o.images[0]}`:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNyAxM0wxOSAxNUwyMyAxMUwyOSAxN0gzM1YyM0gxM1YxN0wxNyAxM1oiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+",description:o.event||"Activity detected"}));return u.jsxs(xt,{className:"bg-card border-border",children:[u.jsx(Or,{children:u.jsxs(Dr,{className:"text-card-foreground flex items-center gap-2",children:[u.jsx(ef,{className:"h-5 w-5 text-primary"}),"Recent Sensor Events"]})}),u.jsx(wt,{children:n?u.jsxs("div",{className:"flex items-center justify-center h-32",children:[u.jsx(oi,{className:"h-4 w-4 animate-spin text-muted-foreground"}),u.jsx("span",{className:"ml-2 text-muted-foreground text-sm",children:"Loading events..."})]}):r.length===0?u.jsx("div",{className:"text-center text-muted-foreground py-8",children:u.jsx("p",{children:"No sensor events available"})}):u.jsxs(u.Fragment,{children:[u.jsx("div",{className:"space-y-3",children:r.map(o=>u.jsxs("div",{className:"flex items-center gap-4 p-3 bg-secondary rounded-lg hover:bg-accent transition-colors cursor-pointer group",onClick:()=>e(o),children:[u.jsx("div",{className:"text-muted-foreground font-mono text-sm min-w-[70px]",children:o.time}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(o.icon,{className:"h-4 w-4 text-primary"}),u.jsx("img",{src:o.thumbnail,alt:"Event thumbnail",className:"w-8 h-8 rounded object-cover",onError:s=>{s.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNyAxM0wxOSAxNUwyMyAxMUwyOSAxN0gzM1YyM0gxM1YxN0wxNyAxM1oiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"}})]}),u.jsx("div",{className:"flex-1 text-secondary-foreground text-sm",children:o.description}),u.jsx(he,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity text-primary hover:text-primary/80",children:u.jsx(Yv,{className:"h-4 w-4"})})]},o.id))}),u.jsx("div",{className:"mt-4 text-center",children:u.jsx(he,{variant:"outline",className:"border-border text-muted-foreground hover:border-primary hover:text-primary",children:"View All Events"})})]})})]})}function uM(){const[e,t]=p.useState(""),[n,r]=p.useState([]),[o,s]=p.useState(!1),i=async()=>{if(e.trim()){s(!0);try{const l=(await wr.searchObjects(e)).map(c=>({id:c.id,timestamp:new Date(c.timestamp).toLocaleString(),thumbnail:c.thumbnail,description:c.description||`${e} found in image`,confidence:Math.round(c.confidence*100),filename:c.filename}));r(l)}catch(a){console.error("Search failed:",a),r([])}finally{s(!1)}}};return u.jsxs(xt,{className:"bg-card border-border",children:[u.jsx(Or,{children:u.jsxs(Dr,{className:"text-card-foreground flex items-center gap-2",children:[u.jsx(Ia,{className:"h-5 w-5 text-primary"}),"Quick Object Search"]})}),u.jsxs(wt,{className:"space-y-4",children:[u.jsxs("div",{className:"flex gap-2",children:[u.jsx(Nl,{placeholder:"Where did I last keep my phone?",value:e,onChange:a=>t(a.target.value),className:"bg-input border-border text-foreground placeholder-muted-foreground focus:border-primary",onKeyPress:a=>a.key==="Enter"&&i()}),u.jsx(he,{onClick:i,disabled:o||!e.trim(),className:"bg-primary hover:bg-primary/90 text-primary-foreground disabled:opacity-50",children:o?u.jsx(oi,{className:"h-4 w-4 animate-spin"}):u.jsx(Ia,{className:"h-4 w-4"})})]}),n.length>0&&u.jsx("div",{className:"space-y-3 max-h-48 overflow-y-auto",children:n.map(a=>u.jsxs("div",{className:"flex items-center gap-3 p-3 bg-secondary rounded-lg hover:bg-accent transition-colors cursor-pointer",children:[u.jsx("img",{src:a.thumbnail,alt:"Search result",className:"w-10 h-10 rounded object-cover",onError:l=>{l.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNyAxM0wxOSAxNUwyMyAxMUwyOSAxN0gzM1YyM0gxM1YxN0wxNyAxM1oiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"}}),u.jsxs("div",{className:"flex-1",children:[u.jsx("div",{className:"text-secondary-foreground text-sm font-medium",children:a.description}),u.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[u.jsx(ml,{className:"h-3 w-3"}),a.timestamp,u.jsxs("span",{className:"ml-auto text-primary",children:[a.confidence,"% confident"]})]})]})]},a.id))})]})]})}function cM({onSelectItem:e}){return u.jsxs("div",{className:"space-y-6 animate-fade-in",children:[u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[u.jsx(sM,{}),u.jsx(iM,{})]}),u.jsx("div",{className:"w-full",children:u.jsx(aM,{onSelectImage:e})}),u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[u.jsx(lM,{onSelectEvent:e}),u.jsx(uM,{})]})]})}const dM=()=>u.jsx(Ww,{title:"Dashboard Home",children:e=>u.jsx(cM,{onSelectItem:e})});function fM({onSelectImage:e}){const[t,n]=p.useState(""),[r,o]=p.useState("all"),i=[{id:1,timestamp:"2024-01-15 14:30",description:"Motion detected in living room",thumbnail:"https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=800&h=600&fit=crop",tags:["motion","living room","daytime"],confidence:95},{id:2,timestamp:"2024-01-15 14:25",description:"Cat moving through kitchen",thumbnail:"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=800&h=600&fit=crop",tags:["animal","kitchen","movement"],confidence:88},{id:3,timestamp:"2024-01-15 14:20",description:"Package delivery detected",thumbnail:"https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=800&h=600&fit=crop",tags:["delivery","outdoor","package"],confidence:92},{id:4,timestamp:"2024-01-15 14:15",description:"Normal surveillance",thumbnail:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=600&fit=crop",tags:["normal","surveillance"],confidence:75},{id:5,timestamp:"2024-01-15 14:10",description:"Movement in backyard",thumbnail:"https://images.unsplash.com/photo-1500673922987-e212871fec22?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1500673922987-e212871fec22?w=800&h=600&fit=crop",tags:["backyard","movement","outdoor"],confidence:89},{id:6,timestamp:"2024-01-15 13:45",description:"Person entering front door",thumbnail:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop",tags:["person","entrance","front door"],confidence:97},{id:7,timestamp:"2024-01-15 13:30",description:"Vehicle in driveway",thumbnail:"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop",tags:["vehicle","driveway","outdoor"],confidence:91},{id:8,timestamp:"2024-01-15 13:15",description:"Bird activity in garden",thumbnail:"https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop",tags:["bird","garden","nature"],confidence:82},{id:9,timestamp:"2024-01-15 12:50",description:"Security check - all clear",thumbnail:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop",tags:["security","clear","routine"],confidence:70},{id:10,timestamp:"2024-01-15 12:30",description:"Wind moving plants",thumbnail:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",tags:["wind","plants","natural"],confidence:65},{id:11,timestamp:"2024-01-15 12:15",description:"Mailman delivery",thumbnail:"https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop",tags:["mailman","delivery","person"],confidence:94},{id:12,timestamp:"2024-01-15 11:45",description:"Neighbor walking dog",thumbnail:"https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=200&fit=crop",fullImage:"https://images.unsplash.com/photo-1552053831-71594a27632d?w=800&h=600&fit=crop",tags:["neighbor","dog","walking"],confidence:90}].filter(a=>{const l=a.description.toLowerCase().includes(t.toLowerCase())||a.tags.some(c=>c.toLowerCase().includes(t.toLowerCase()));return r==="all"?l:r==="motion"?l&&a.tags.includes("motion"):r==="people"?l&&(a.tags.includes("person")||a.tags.includes("mailman")):r==="animals"?l&&(a.tags.includes("animal")||a.tags.includes("bird")||a.tags.includes("dog")):l});return u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"All Captures"}),u.jsx("p",{className:"text-muted-foreground",children:"Browse through all captured images and events"})]}),u.jsx("div",{className:"flex items-center gap-2",children:u.jsxs(ln,{variant:"outline",className:"border-border text-muted-foreground",children:[i.length," captures"]})})]}),u.jsx(xt,{className:"bg-card border-border",children:u.jsx(wt,{className:"p-4",children:u.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[u.jsx("div",{className:"flex-1",children:u.jsxs("div",{className:"relative",children:[u.jsx(Ia,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),u.jsx(Nl,{placeholder:"Search captures by description or tags...",value:t,onChange:a=>n(a.target.value),className:"pl-10 bg-input border-border text-foreground placeholder-muted-foreground"})]})}),u.jsxs("div",{className:"flex gap-2",children:[u.jsx(he,{variant:r==="all"?"default":"outline",size:"sm",onClick:()=>o("all"),className:r==="all"?"bg-primary text-primary-foreground":"border-border text-muted-foreground hover:border-primary hover:text-primary",children:"All"}),u.jsx(he,{variant:r==="motion"?"default":"outline",size:"sm",onClick:()=>o("motion"),className:r==="motion"?"bg-primary text-primary-foreground":"border-border text-muted-foreground hover:border-primary hover:text-primary",children:"Motion"}),u.jsx(he,{variant:r==="people"?"default":"outline",size:"sm",onClick:()=>o("people"),className:r==="people"?"bg-primary text-primary-foreground":"border-border text-muted-foreground hover:border-primary hover:text-primary",children:"People"}),u.jsx(he,{variant:r==="animals"?"default":"outline",size:"sm",onClick:()=>o("animals"),className:r==="animals"?"bg-primary text-primary-foreground":"border-border text-muted-foreground hover:border-primary hover:text-primary",children:"Animals"})]})]})})}),u.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:i.map(a=>u.jsx(xt,{className:"bg-card border-border hover:border-primary transition-colors group cursor-pointer",children:u.jsxs(wt,{className:"p-0",children:[u.jsxs("div",{className:"relative",children:[u.jsx("img",{src:a.thumbnail,alt:a.description,className:"w-full h-48 object-cover rounded-t-lg",onClick:()=>e==null?void 0:e(a)}),u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-t-lg"}),u.jsxs("div",{className:"absolute top-2 right-2 flex gap-1",children:[u.jsx(he,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-black/50 hover:bg-black/70 text-white border-0",onClick:l=>{l.stopPropagation(),e==null||e(a)},children:u.jsx(Yv,{className:"h-4 w-4"})}),u.jsx(he,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-black/50 hover:bg-black/70 text-white border-0",onClick:l=>{l.stopPropagation()},children:u.jsx(Gv,{className:"h-4 w-4"})})]}),u.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded",children:u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(ml,{className:"h-3 w-3"}),a.timestamp]})})]}),u.jsxs("div",{className:"p-4",children:[u.jsx("p",{className:"text-sm text-card-foreground font-medium mb-2",children:a.description}),u.jsxs("div",{className:"flex flex-wrap gap-1 mb-2",children:[a.tags.slice(0,3).map(l=>u.jsx(ln,{variant:"outline",className:"text-xs border-border text-muted-foreground",children:l},l)),a.tags.length>3&&u.jsxs(ln,{variant:"outline",className:"text-xs border-border text-muted-foreground",children:["+",a.tags.length-3]})]}),u.jsxs("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[u.jsxs("span",{children:["Confidence: ",a.confidence,"%"]}),u.jsxs("span",{children:["ID: ",a.id]})]})]})]})},a.id))}),i.length===0&&u.jsx(xt,{className:"bg-card border-border",children:u.jsxs(wt,{className:"p-8 text-center",children:[u.jsx(Xv,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),u.jsx("h3",{className:"text-lg font-medium text-card-foreground mb-2",children:"No captures found"}),u.jsx("p",{className:"text-muted-foreground",children:"Try adjusting your search or filter criteria."})]})})]})}function pM(){return u.jsx(Ww,{title:"All Captures",children:e=>u.jsx(fM,{onSelectImage:e})})}const hM=()=>{const e=ui();return p.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:u.jsxs("div",{className:"text-center",children:[u.jsx("h1",{className:"text-4xl font-bold mb-4 text-foreground",children:"404"}),u.jsx("p",{className:"text-xl text-muted-foreground mb-4",children:"Oops! Page not found"}),u.jsx("a",{href:"/",className:"text-primary hover:text-primary/80 underline",children:"Return to Home"})]})})},mM=new zN,gM=()=>u.jsx(HN,{client:mM,children:u.jsxs(Fy,{children:[u.jsx(xE,{}),u.jsx(GE,{}),u.jsx(Zk,{children:u.jsxs(Wk,{children:[u.jsx(oa,{path:"/",element:u.jsx(dM,{})}),u.jsx(oa,{path:"/all-captures",element:u.jsx(pM,{})}),u.jsx(oa,{path:"*",element:u.jsx(hM,{})})]})})]})});bv(document.getElementById("root")).render(u.jsx(gM,{}));
