
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Bug, Package, Loader2, Camera } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";

export function AlertsOverviewCard() {
  const { data: alerts = [], isLoading } = useQuery({
    queryKey: ['alerts'],
    queryFn: api.getAlerts,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Categorize alerts by type (based on keywords in the message)
  const categorizeAlerts = (alerts: any[]) => {
    const categories = {
      'Motion Detection': { count: 0, icon: Camera, color: "bg-blue-600" },
      'Activity': { count: 0, icon: AlertTriangle, color: "bg-green-600" },
      'Other': { count: 0, icon: Package, color: "bg-gray-600" },
    };

    alerts.forEach(alert => {
      const message = alert.message?.toLowerCase() || '';
      if (message.includes('motion') || message.includes('movement')) {
        categories['Motion Detection'].count++;
      } else if (message.includes('activity') || message.includes('person') || message.includes('people')) {
        categories['Activity'].count++;
      } else {
        categories['Other'].count++;
      }
    });

    return Object.entries(categories).map(([type, data]) => ({
      type,
      ...data
    }));
  };

  const alertCategories = categorizeAlerts(alerts);

  return (
    <Card className="bg-card border-border hover:border-primary transition-colors">
      <CardHeader>
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-primary" />
          Alerts Overview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground text-sm">Loading alerts...</span>
          </div>
        ) : alertCategories.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <p>No alerts available</p>
          </div>
        ) : (
          <>
            {alertCategories.map((alert) => (
              <div key={alert.type} className="flex items-center justify-between p-3 bg-secondary rounded-lg hover:bg-accent transition-colors">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${alert.color}`}>
                    <alert.icon className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-secondary-foreground font-medium">{alert.type}</span>
                </div>
                <Badge variant="outline" className="border-border text-card-foreground">
                  {alert.count}
                </Badge>
              </div>
            ))}

            <div className="pt-3 border-t border-border">
              <div className="text-center">
                <span className="text-2xl font-bold text-card-foreground">{alerts.length}</span>
                <p className="text-muted-foreground text-sm">Total alerts today</p>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
