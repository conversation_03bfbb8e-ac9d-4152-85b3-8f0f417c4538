import os
import datetime
import time
import json
import imagehash
import subprocess
import platform
import requests
from io import BytesIO
from threading import Thread, Event

from PIL import Image
import cv2
import numpy as np
from flask import Flask, request, jsonify, send_from_directory, send_file
import google.generativeai as genai

# --- Configuration ---
UPLOAD_FOLDER        = 'captured_images'
ANALYSIS_LOG_FILE    = 'analysis_log.json'
HIST_DUP_THRESH      = 0.9        # OpenCV histogram correlation threshold
PHASH_DUP_THRESH     = 5          # Perceptual‑hash difference threshold
GEMINI_MODEL_NAME    = 'gemini-1.5-flash'
BATCH_INTERVAL_SEC   = 60         # process every 60 seconds

# ESP32 Camera Configuration
ESP32_CAM_URL = "http://*************/capture"  # Change this to your ESP32 IP
ESP32_CAM_TIMEOUT = 10  # seconds

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "1815448221"  # Replace with actual user ID
TELEGRAM_API_URL = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}"

# Gemini API Key — set via environment
GEMINI_API_KEY = "AIzaSyDfADnGrJ9LwlrYTnnKY2dxeMh8qsHYjiU"
if not GEMINI_API_KEY:
    print("ERROR: set GEMINI_API_KEY in env before running")
    exit(1)

# initialize Gemini client
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel(GEMINI_MODEL_NAME)

# --- Globals & Setup ---
app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

previous_hist = None
previous_phash = None
image_buffer = []  # list of (filepath, timestamp_iso)
stop_event = Event()

# Ensure JSON log exists
def init_log():
    if not os.path.exists(ANALYSIS_LOG_FILE):
        with open(ANALYSIS_LOG_FILE, 'w') as f:
            json.dump([], f)

# --- Duplicate Detection ---
def is_duplicate_opencv(img, prev_img, thresh=HIST_DUP_THRESH):
    hist1 = cv2.calcHist([img],[0],None,[256],[0,256])
    hist2 = cv2.calcHist([prev_img],[0],None,[256],[0,256])
    return cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL) >= thresh

# --- JSON Logging & AI Processing ---
def append_log(entry):
    # Safe load or initialize
    try:
        with open(ANALYSIS_LOG_FILE, 'r+') as f:
            try:
                data = json.load(f)
            except (json.JSONDecodeError, ValueError):
                data = []
            data.append(entry)
            f.seek(0)
            json.dump(data, f, indent=2)
            f.truncate()
    except Exception as e:
        print(f"Error writing log: {e}")


def process_batch(batch):
    init_log()
    if not batch:
        return
    start_ts = batch[0][1]
    pil_images, names = [], []
    for path, ts in batch:
        try:
            pil_images.append(Image.open(path))
            names.append(os.path.basename(path))
        except Exception:
            continue
    prompt = [
        "You are an activity‑monitoring AI. Analyze this one‑minute sequence of images."
    ] + pil_images
    try:
        resp = gemini_model.generate_content(prompt)
        summary = resp.text.strip()
    except Exception as e:
        summary = f"<Gemini error: {e}>"
    entry = {
        "interval_start": start_ts,
        "interval_end": datetime.datetime.utcnow().isoformat(),
        "summary": summary,
        "images": names
    }
    append_log(entry)
    print(f"Logged JSON batch: {entry}")

# --- Background Batch Thread ---
def batch_worker():
    init_log()
    while not stop_event.is_set():
        time.sleep(BATCH_INTERVAL_SEC)
        current_batch = []
        # Drain buffer
        while image_buffer:
            current_batch.append(image_buffer.pop(0))
        process_batch(current_batch)

# --- Static Files ---
@app.route('/')
def index():
    return send_from_directory('static', 'index.html')

@app.route('/static/<path:path>')
def serve_static(path):
    return send_from_directory('static', path)

# --- API Endpoints ---
@app.route('/api/analysis', methods=['GET'])
def get_analysis():
    try:
        with open(ANALYSIS_LOG_FILE, 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify(error=str(e)), 500

@app.route('/api/images/<path:filename>')
def get_image(filename):
    return send_from_directory(UPLOAD_FOLDER, filename)

@app.route('/api/images')
def list_images():
    try:
        files = os.listdir(UPLOAD_FOLDER)
        files = [f for f in files if f.endswith('.jpg')]
        files.sort()
        return jsonify(files)
    except Exception as e:
        return jsonify(error=str(e)), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        query = data.get('query', '')

        # Load analysis data
        with open(ANALYSIS_LOG_FILE, 'r') as f:
            analysis_data = json.load(f)

        if not analysis_data:
            return jsonify({
                'response': "I don't have any activity data to analyze yet.",
                'images': []
            })

        # Prepare context for Gemini
        context = json.dumps(analysis_data, indent=2)

        # Create prompt for Gemini with structured output instructions
        prompt = f"""You are an AI assistant for a Life Tracker application that analyzes images and activities.
I'll provide you with JSON data containing analysis of image batches. Each batch has:
- interval_start: timestamp when the batch started
- interval_end: timestamp when the batch ended
- summary: AI-generated description of what's in the images
- images: list of image filenames

The user is asking: "{query}"

Use the following JSON data as context to answer their question:
{context}

Important instructions:
1. Answer based ONLY on the information in the JSON data
2. If you can't find relevant information, say so clearly
3. Format dates in a user-friendly way (e.g., "May 14, 2025 at 7:23 PM")
4. Your response should be conversational and helpful
5. IMPORTANT: Return your response in this JSON structure:
   {{"response": "Your text response here",
     "relevant_images": ["img_filename1.jpg", "img_filename2.jpg"]}}
6. Only include images that are directly relevant to the user's query
7. Limit to 5 most relevant images maximum
8. Don't mention the JSON structure in your response text
"""

        # Call Gemini API
        response = gemini_model.generate_content(prompt)
        response_text = response.text.strip()

        # Parse the JSON response from Gemini
        try:
            # Extract JSON from the response (in case Gemini adds extra text)
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)

                # Extract response and images
                ai_response = result.get('response', '')
                images = result.get('relevant_images', [])

                # Validate images exist in our data
                valid_images = []
                for img in images:
                    for batch in analysis_data:
                        if img in batch['images'] and img not in valid_images:
                            valid_images.append(img)

                return jsonify({
                    'response': ai_response,
                    'images': valid_images
                })
            else:
                # Fallback if JSON parsing fails
                return jsonify({
                    'response': "I processed your request, but couldn't format the response properly. Please try again.",
                    'images': []
                })

        except json.JSONDecodeError:
            # If Gemini didn't return valid JSON, use the text response
            print(f"Failed to parse JSON from Gemini response: {response_text}")
            return jsonify({
                'response': "I understood your question, but had trouble formatting my response. Here's what I found: " + response_text,
                'images': []
            })

    except Exception as e:
        print(f"Chat error: {e}")
        return jsonify({
            'response': "Sorry, I encountered an error while processing your request.",
            'images': []
        }), 500

def send_telegram_photo(image_path, caption=""):
    """
    Send a photo to Telegram bot
    """
    try:
        url = f"{TELEGRAM_API_URL}/sendPhoto"
        with open(image_path, 'rb') as photo:
            files = {'photo': photo}
            data = {
                'chat_id': TELEGRAM_CHAT_ID,
                'caption': caption
            }
            response = requests.post(url, files=files, data=data)
            response.raise_for_status()
            return True, response.json()
    except Exception as e:
        print(f"Failed to send Telegram photo: {e}")
        return False, str(e)

# --- Instant Trigger Endpoint ---
@app.route('/trigger', methods=['POST', 'GET'])
def trigger_capture():
    """
    Trigger endpoint that captures/sends an image, plays a sound file and sends Telegram notification.
    Can be called from another device.
    """
    try:
        ts = datetime.datetime.utcnow()
        
        # Get list of images before triggering ESP32
        initial_images = set()
        try:
            initial_images = set(f for f in os.listdir(UPLOAD_FOLDER) if f.endswith('.jpg'))
            print(f"Initial image count: {len(initial_images)}")
        except Exception as e:
            print(f"Error listing initial images: {e}")
        
        # Trigger ESP32 to capture a new image
        esp32_capture_success = False
        try:
            print("Triggering ESP32 camera capture...")
            response = requests.get(ESP32_CAM_URL, timeout=ESP32_CAM_TIMEOUT)
            if response.status_code == 200:
                esp32_capture_success = True
                print("ESP32 capture triggered successfully")
            else:
                print(f"ESP32 capture failed with status: {response.status_code}")
        except Exception as e:
            print(f"Failed to trigger ESP32 capture: {e}")
        
        # Wait for new image to be saved (ESP32 should POST to /upload)
        print("Waiting 5 seconds for new image to be saved...")
        time.sleep(5)
        
        # Find the new image that was just captured
        image_to_send = None
        new_image_found = False
        try:
            # Get current list of images
            current_images = set(f for f in os.listdir(UPLOAD_FOLDER) if f.endswith('.jpg'))
            new_images = current_images - initial_images
            
            if new_images:
                # Sort new images by timestamp and get the most recent
                new_images_list = list(new_images)
                new_images_list.sort(reverse=True)
                most_recent_new = new_images_list[0]
                image_to_send = os.path.join(UPLOAD_FOLDER, most_recent_new)
                new_image_found = True
                print(f"Found new ESP32 image: {most_recent_new}")
            else:
                # Fallback to most recent existing image if no new image
                all_images = list(current_images)
                if all_images:
                    all_images.sort(reverse=True)
                    most_recent = all_images[0]
                    image_to_send = os.path.join(UPLOAD_FOLDER, most_recent)
                    print(f"No new image found, using most recent: {most_recent}")
                else:
                    print("No images found in folder")
        except Exception as e:
            print(f"Error finding new ESP32 image: {e}")
        
        # Send trigger notification to Telegram (with image if available)
        telegram_sent = False
        telegram_error = None
        if TELEGRAM_CHAT_ID and TELEGRAM_CHAT_ID != "YOUR_CHAT_ID_HERE":
            telegram_caption = f"🚨 Trigger activated at {ts.strftime('%Y-%m-%d %H:%M:%S')} UTC"
            
            if image_to_send and os.path.exists(image_to_send):
                # Send photo with caption
                try:
                    success, result = send_telegram_photo(image_to_send, telegram_caption)
                    if success:
                        telegram_sent = True
                        print(f"Telegram photo sent successfully")
                    else:
                        telegram_error = f"Failed to send photo: {result}"
                        print(f"Failed to send Telegram photo: {result}")
                except Exception as e:
                    telegram_error = f"Photo send error: {str(e)}"
                    print(f"Telegram photo error: {e}")
            else:
                # Send text message only if no image available
                try:
                    url = f"{TELEGRAM_API_URL}/sendMessage"
                    data = {
                        'chat_id': TELEGRAM_CHAT_ID,
                        'text': telegram_caption + "\n(No recent image available)"
                    }
                    response = requests.post(url, data=data)
                    response.raise_for_status()
                    telegram_sent = True
                    print("Telegram text message sent (no image available)")
                except Exception as e:
                    telegram_error = str(e)
                    print(f"Telegram text error: {e}")
        else:
            telegram_error = "Telegram user ID not configured"
        # Play sound file after trigger
        sound_played = False
        sound_error = None
        
        # Check for sound file (try multiple possible locations and names)
        possible_sound_files = [
            'a.mp4',
            '/a.mp4', 
            'static/a.mp4',
            'a.mp3',
            'a.wav',
            'trigger_sound.mp4',
            'trigger_sound.mp3'
        ]
        
        sound_file = None
        for sf in possible_sound_files:
            if os.path.exists(sf):
                sound_file = sf
                break
        
        if sound_file:
            try:
                # Play sound based on platform
                if platform.system() == "Windows":
                    # Windows
                    subprocess.run(['start', '', sound_file], shell=True, check=False)
                elif platform.system() == "Darwin":
                    # macOS
                    subprocess.run(['afplay', sound_file], check=False)
                else:
                    # Linux
                    subprocess.run(['aplay', sound_file], check=False)
                sound_played = True
            except Exception as e:
                sound_error = str(e)
        else:
            sound_error = "Sound file not found (looked for: " + ", ".join(possible_sound_files) + ")"
        
        response_data = {
            "status": "success",
            "message": "Trigger executed successfully",
            "timestamp": ts.isoformat(),
            "sound_played": sound_played,
            "telegram_sent": telegram_sent,
            "image_sent": image_to_send is not None and os.path.exists(image_to_send) if image_to_send else False
        }
        
        if sound_error:
            response_data["sound_error"] = sound_error
        
        if telegram_error:
            response_data["telegram_error"] = telegram_error
        
        print(f"Trigger executed at {ts.isoformat()}, Sound played: {sound_played}, Telegram sent: {telegram_sent}")
        
        return jsonify(response_data), 200
        
    except Exception as e:
        print(f"Trigger error: {e}")
        return jsonify(error=f"Trigger failed: {str(e)}"), 500

# --- Flask Upload Endpoint ---
@app.route('/upload', methods=['POST'])
def upload():
    global previous_hist, previous_phash
    data = request.data
    if not data:
        return jsonify(error="No image data"), 400
    arr = np.frombuffer(data, np.uint8)
    img = cv2.imdecode(arr, cv2.IMREAD_COLOR)
    if img is None:
        return jsonify(error="Invalid image"), 400
    if previous_hist is not None and is_duplicate_opencv(img, previous_hist):
        return jsonify(status="duplicate (hist)"), 200
    previous_hist = img.copy()
    try:
        phash = imagehash.phash(Image.open(BytesIO(data)))
    except Exception:
        phash = None
    if previous_phash and phash and (phash - previous_phash) < PHASH_DUP_THRESH:
        return jsonify(status="duplicate (phash)"), 200
    previous_phash = phash
    ts = datetime.datetime.utcnow()
    ts_file = ts.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    filename = f"img_{ts_file}.jpg"
    path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    with open(path, 'wb') as f:
        f.write(data)
    image_buffer.append((path, ts.isoformat()))
    return jsonify(status="saved", filename=filename), 201

if __name__ == '__main__':
    Thread(target=batch_worker, daemon=True).start()
    print(f"Starting server with Gemini model {GEMINI_MODEL_NAME}")
    app.run(host='0.0.0.0', port=5000)
