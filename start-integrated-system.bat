@echo off
echo Starting Integrated Security Dashboard System...
echo.

echo Installing backend dependencies...
cd lifeTrack
pip install -r requirements.txt
echo.

echo Starting integrated Flask server with React UI...
echo This will serve both the backend API and the React frontend on port 5000
echo.
start "Integrated LifeTrack System" python app
cd ..

echo.
echo ========================================
echo System started successfully!
echo.
echo Integrated Dashboard: http://localhost:5000
echo.
echo The system combines:
echo - lifeTrack backend (Flask API + AI analysis)
echo - shadow-guardian-home frontend (React UI)
echo.
echo All running on a single server at port 5000
echo ========================================
pause
