let allCapturesData = [];
let filteredData = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    loadAllCaptures();
    setupEventListeners();
});

function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('input', handleSearch);

    // Filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', handleFilter);
    });

    // Modal functionality
    const modal = document.getElementById('image-modal');
    const closeBtn = document.querySelector('.close');

    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

async function loadAllCaptures() {
    try {
        const response = await fetch('/api/analysis');
        const data = await response.json();

        if (data.status === 'success') {
            allCapturesData = data.data;
            filteredData = [...allCapturesData];
            updateCaptureCount();
            displayCaptures();
        } else {
            showError('Failed to load captures');
        }
    } catch (error) {
        console.error('Error loading captures:', error);
        showError('Error loading captures');
    }
}

function updateCaptureCount() {
    const totalCount = filteredData.length;
    document.getElementById('total-captures').textContent = totalCount;
}

function displayCaptures() {
    const capturesGrid = document.getElementById('captures-grid');

    if (filteredData.length === 0) {
        capturesGrid.innerHTML = `
            <div class="no-captures">
                <i class="fas fa-camera"></i>
                <p>No captures found</p>
            </div>
        `;
        return;
    }

    // Sort by most recent first
    const sortedCaptures = [...filteredData]
        .sort((a, b) => new Date(b.interval_start) - new Date(a.interval_start));

    capturesGrid.innerHTML = '';

    sortedCaptures.forEach(batch => {
        batch.images.forEach(image => {
            const captureCard = createCaptureCard(image, batch);
            capturesGrid.appendChild(captureCard);
        });
    });
}

function createCaptureCard(image, batch) {
    const card = document.createElement('div');
    card.className = 'capture-card';

    const timestamp = new Date(batch.interval_start).toLocaleString();
    const description = batch.summary || 'No description available';
    const confidence = 85; // Default confidence since it's not in the current data structure

    // Determine tags based on summary content
    const tags = [];
    const summaryLower = (batch.summary || '').toLowerCase();
    if (summaryLower.includes('motion') || summaryLower.includes('movement')) {
        tags.push('motion');
    }
    if (summaryLower.includes('person') || summaryLower.includes('people')) {
        tags.push('people');
    }
    if (summaryLower.includes('cat') || summaryLower.includes('dog') || summaryLower.includes('animal')) {
        tags.push('animals');
    }

    card.innerHTML = `
        <div class="capture-image-container">
            <img src="/api/images/${image}" alt="Capture" loading="lazy">
            <div class="capture-overlay">
                <button class="view-btn" onclick="openImageModal('${image}', '${timestamp}', '${description}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="download-btn" onclick="downloadImage('${image}')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        </div>
        <div class="capture-info">
            <div class="capture-timestamp">
                <i class="fas fa-clock"></i>
                ${timestamp}
            </div>
            <div class="capture-description">
                ${description}
            </div>
            <div class="capture-meta">
                <div class="confidence">
                    Confidence: ${confidence}%
                </div>
                <div class="tags">
                    ${tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
        </div>
    `;

    // Store data for filtering
    card.dataset.tags = tags.join(',');
    card.dataset.description = description.toLowerCase();

    return card;
}

function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();

    if (searchTerm === '') {
        filteredData = [...allCapturesData];
    } else {
        filteredData = allCapturesData.filter(batch => {
            const description = (batch.summary || '').toLowerCase();
            return description.includes(searchTerm);
        });
    }

    updateCaptureCount();
    displayCaptures();
}

function handleFilter(e) {
    const filterType = e.target.dataset.filter;

    // Update active button
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    e.target.classList.add('active');

    if (filterType === 'all') {
        filteredData = [...allCapturesData];
    } else {
        filteredData = allCapturesData.filter(batch => {
            const summaryLower = (batch.summary || '').toLowerCase();
            if (filterType === 'motion') {
                return summaryLower.includes('motion') || summaryLower.includes('movement');
            } else if (filterType === 'people') {
                return summaryLower.includes('person') || summaryLower.includes('people');
            } else if (filterType === 'animals') {
                return summaryLower.includes('cat') || summaryLower.includes('dog') || summaryLower.includes('animal');
            }
            return false;
        });
    }

    updateCaptureCount();
    displayCaptures();
}

function openImageModal(imagePath, timestamp, description) {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const modalTimestamp = document.getElementById('modal-timestamp');
    const modalDescription = document.getElementById('modal-description');

    modalImage.src = `/api/images/${imagePath}`;
    modalTimestamp.textContent = timestamp;
    modalDescription.textContent = description;

    modal.style.display = 'block';
}

function downloadImage(imagePath) {
    const link = document.createElement('a');
    link.href = `/api/images/${imagePath}`;
    link.download = imagePath;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showError(message) {
    const capturesGrid = document.getElementById('captures-grid');
    capturesGrid.innerHTML = `
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <p>${message}</p>
        </div>
    `;
}
